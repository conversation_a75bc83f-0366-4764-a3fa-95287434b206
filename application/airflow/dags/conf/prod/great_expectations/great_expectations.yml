# config_version refers to the syntactic version of this config file, and is used in maintaining backwards compatibility
# It is auto-generated and usually does not need to be changed.
config_version: 3.0

# Datasources tell Great Expectations where your data lives and how to get it.
datasources:
  athena_datastore:
    data_connectors:
      default_runtime_data_connector_name:
        batch_identifiers:
          - default_identifier_name
        module_name: great_expectations.datasource.data_connector
        class_name: RuntimeDataConnector
      default_inferred_data_connector_name:
        module_name: great_expectations.datasource.data_connector
        include_schema_name: true
        class_name: InferredAssetSqlDataConnector
      default_configured_data_connector_name:
        module_name: great_expectations.datasource.data_connector
        class_name: ConfiguredAssetSqlDataConnector
    module_name: great_expectations.datasource
    execution_engine:
      module_name: great_expectations.execution_engine
      connection_string: awsathena+rest://@athena.eu-central-1.amazonaws.com/?s3_staging_dir=s3://eu-central-1-001139134621-prod-datalake-infra-base-data/ge/&work_group=data_engineering_workgroup
      class_name: SqlAlchemyExecutionEngine
    class_name: Datasource
  mga:
    module_name: great_expectations.datasource
    class_name: Datasource
    data_connectors:
      default_runtime_data_connector_name:
        module_name: great_expectations.datasource.data_connector
        class_name: RuntimeDataConnector
        batch_identifiers:
          - task
      default_inferred_data_connector_name:
        module_name: great_expectations.datasource.data_connector
        class_name: InferredAssetSqlDataConnector
        include_schema_name: true
    execution_engine:
      module_name: great_expectations.execution_engine
      credentials: ${ro_mga}
      class_name: SqlAlchemyExecutionEngine

config_variables_file_path: config_variables.yml

# The plugins_directory will be added to your python path for custom modules
# used to override and extend Great Expectations.
plugins_directory: plugins/

stores:
# Stores are configurable places to store things like Expectations, Validations
# Data Docs, and more. These are for advanced users only - most users can simply
# leave this section alone.
#
# Three stores are required: expectations, validations, and
# evaluation_parameters, and must exist with a valid store entry. Additional
# stores can be configured for uses such as data_docs, etc.
  expectations_s3_store:
    class_name: ExpectationsStore
    store_backend:
      class_name: TupleS3StoreBackend
      bucket: eu-central-1-001139134621-prod-datalake-airflow-artifacts
      prefix: mga-great-expectations/expectations

  validations_s3_store:
    class_name: ValidationsStore
    store_backend:
      class_name: TupleS3StoreBackend
      bucket: eu-central-1-001139134621-prod-datalake-airflow-artifacts
      prefix: great-expectations/validations

  evaluation_parameter_store:
    class_name: EvaluationParameterStore
    store_backend:
      class_name: DatabaseStoreBackend
      credentials: ${ge_metrics_store}

  checkpoint_s3_store:
    class_name: CheckpointStore
    store_backend:
      class_name: TupleS3StoreBackend
      bucket: eu-central-1-001139134621-prod-datalake-airflow-artifacts
      prefix: mga-great-expectations/checkpoints
      filepath_template:
      filepath_suffix: .yml

  metric_store:
    class_name: MetricStore
    store_backend:
      class_name: DatabaseStoreBackend
      credentials: ${ge_metrics_store}

evaluation_parameter_store_name: evaluation_parameter_store
expectations_store_name: expectations_s3_store
validations_store_name: validations_s3_store
checkpoint_store_name: checkpoint_s3_store

# Data Docs make it simple to visualize data quality in your project. These
data_docs_sites:
  local_site:
    class_name: SiteBuilder
    # set to false to hide how-to buttons in Data Docs
    show_how_to_buttons: true
    store_backend:
      class_name: TupleS3StoreBackend
      bucket: eu-central-1-001139134621-prod-datalake-airflow-artifacts
      prefix: great-expectations/data_docs
      base_public_path: http://data-quality-k8s.prod.datalake.caramelspec.com
    site_index_builder:
      class_name: DefaultSiteIndexBuilder
      show_cta_footer: false
      validation_results_limit: 100

anonymous_usage_statistics:
  enabled: false
