anonymous_usage_statistics:
  enabled: false
checkpoint_store_name: checkpoint_s3_store
config_variables_file_path: config_variables.yml
config_version: 3.0
data_docs_sites:
  s3_site:
    class_name: SiteBuilder
    show_how_to_buttons: false
    site_index_builder:
      class_name: DefaultSiteIndexBuilder
      show_cta_footer: false
      validation_results_limit: 100
    store_backend:
      boto3_options: &id001
        aws_access_key_id: foo
        aws_secret_access_key: bar
        endpoint_url: http://localstack:4566
      bucket: artifacts
      class_name: TupleS3StoreBackend
      prefix: great-expectations/data_docs
datasources:
  athena_datastore:
    class_name: Datasource
    data_connectors:
      default_configured_data_connector_name:
        class_name: ConfiguredAssetSqlDataConnector
        module_name: great_expectations.datasource.data_connector
      default_inferred_data_connector_name:
        class_name: InferredAssetSqlDataConnector
        include_schema_name: true
        module_name: great_expectations.datasource.data_connector
      default_runtime_data_connector_name:
        batch_identifiers:
        - default_identifier_name
        class_name: RuntimeDataConnector
        module_name: great_expectations.datasource.data_connector
    execution_engine:
      class_name: SqlAlchemyExecutionEngine
      connection_string: awsathena+rest://@athena.eu-central-1.amazonaws.com/?s3_staging_dir=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/ge/&work_group=data_engineering_workgroup
      module_name: great_expectations.execution_engine
    module_name: great_expectations.datasource
  mga:
    class_name: Datasource
    data_connectors:
      default_inferred_data_connector_name:
        class_name: InferredAssetSqlDataConnector
        include_schema_name: true
        module_name: great_expectations.datasource.data_connector
      default_runtime_data_connector_name:
        batch_identifiers:
        - task
        class_name: RuntimeDataConnector
        module_name: great_expectations.datasource.data_connector
    execution_engine:
      class_name: SqlAlchemyExecutionEngine
      credentials: ${ro_mga}
      module_name: great_expectations.execution_engine
    module_name: great_expectations.datasource
evaluation_parameter_store_name: evaluation_parameter_store
expectations_store_name: expectations_s3_store
plugins_directory: plugins/
stores:
  checkpoint_s3_store:
    class_name: CheckpointStore
    store_backend:
      boto3_options: *id001
      bucket: artifacts
      class_name: TupleS3StoreBackend
      filepath_suffix: .yml
      filepath_template: null
      prefix: mga-great-expectations/checkpoints
  evaluation_parameter_store:
    class_name: EvaluationParameterStore
    store_backend:
      class_name: DatabaseStoreBackend
      credentials: ${ge_metrics_store}
  expectations_s3_store:
    class_name: ExpectationsStore
    store_backend:
      boto3_options: *id001
      bucket: artifacts
      class_name: TupleS3StoreBackend
      prefix: mga-great-expectations/expectations
  metric_store:
    class_name: MetricStore
    store_backend:
      class_name: DatabaseStoreBackend
      credentials: ${ge_metrics_store}
  validations_s3_store:
    class_name: ValidationsStore
    store_backend:
      boto3_options: *id001
      bucket: artifacts
      class_name: TupleS3StoreBackend
      prefix: great-expectations/validations
validations_store_name: validations_s3_store
