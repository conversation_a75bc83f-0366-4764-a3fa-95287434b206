#!/bin/bash

################################################################################
# Script Name: upload-to-s3.sh
# Description: uploads and tags files to an S3 bucket in a way that allows the Loader ingest them.
#
# Usage: ./upload-to-s3.sh [options] [arguments]
#
# Options:
#   -h, --help        Display this help message and exit
#   -e, --environment The environment where to upload the file (local, dev, stg, prod)
#   -b, --bucket      The bucket name the file should be uploaded to. Default <env>-mga-incoming-bucket.
#   -d, --dir         The key prefix within the bucket where to upload the file (typically it is the partner name)
#
# Arguments:
#   <filepath>       Path of file to upload to S3
#
# Example Usage:
#   ./upload_to_s3.sh -e local -d tuio sample_file.csv
#
################################################################################

function copy_to_s3() {
  aws s3 cp "$file_path" "s3://$bucket_name/$directory_in_bucket/$file_name"
}

function put_tags(){
  md5_value=$(md5 -r "$file_path"| awk '{ print $1 }')
  mod_time=$(stat -f "%m" "$file_path")

  aws s3api put-object-tagging \
  --bucket "$bucket_name" \
  --key "$directory_in_bucket/$file_name" \
  --tagging "{\"TagSet\": [{\"Key\": \"MOD_TIME\", \"Value\": \"$mod_time\"},{\"Key\": \"MD5\", \"Value\": \"$md5_value\"}]}"

}

usage() {
  echo "
  Usage: ./upload-to-s3.sh [options] [arguments]

  Options:
  -h, --help        Display this help message and exit
  -e, --environment The environment where to upload the file (local, dev, stg, prod)
  -b, --bucket      The bucket name the file should be uploaded. Default <env>-mga-incoming-bucket
  -d, --dir         The key prefix within the bucket where to upload the file (typically it is the partner name)

  Arguments:
    <filepath>       Path of file to upload to S3

  Example Usage:
    ./upload_to_s3.sh -e local -d tuio sample_file.csv
    "
    exit 1
}

environment=""
file_path=""
bucket_name=""
directory_in_bucket=""

while  [[ $# -gt 0 ]]; do
    case "$1" in
        -e | --environment)
            environment="$2"
            shift
            shift
            ;;
        -b | --bucket)
            bucket_name="$2"
            shift
            shift
            ;;
        -d | --dir)
            directory_in_bucket="$2"
            shift
            shift
            ;;
        -h)
            usage
            ;;
        *)
            file_path="$1"
            shift
            ;;
    esac
done

if [[ -z "$environment" || -z "$file_path" ]]; then
    echo "Error: Missing required parameters."
    usage
fi

case "$environment" in
    "local"|"dev"|"stg"|"prod")
        bucket_name="$environment-mga-incoming-bucket"
        echo "Bucket set to $bucket_name"
        ;;
    *)
        echo "Error: The environment argument <$environment> is not accepted." >&2
        usage
        ;;
esac

if [ "$environment" == "local" ]; then
  # shellcheck disable=SC2034
  AWS_ENDPOINT_URL="http://localhost:4666"
fi

if [ ! -f "$file_path" ]; then
    echo "Path $file_path either does not exist or is not a file."
    usage
fi

file_name=$(basename "$file_path")

echo "Uploading $file_path to $bucket_name/$directory_in_bucket in $environment environment."

if copy_to_s3; then
    echo "File uploaded successfully to S3 bucket $bucket_name"
else
    echo "Error: Failed to upload file to S3 bucket $bucket_name"
    exit 1
fi

if put_tags; then
  echo "File $file_name successfully tagged"
else
  echo "Error: Could not tag the file"
  exit 1
fi

echo "Upload completed."
