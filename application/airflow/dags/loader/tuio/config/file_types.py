from loader.tuio.factory import *
from loader.tuio.factory.xlsx_complaints import TuioComplaintsXLSXToDataFrameFactory

legacy_loads = ["tuio_claims"]  # to account for commas in the claim descriptions

file_types = [
    {
        "name": "tuio_csv_policies",
        "factory": TuioPoliciesCSVToDataFrameFactory,
        "regexp": r"^(\d{8})-(\d{8})-iptiq_policies\.csv$",
        "category": "policies",
    },
    {
        "name": "tuio_csv_claims",
        "factory": TuioClaimsCSVToDataFrameFactory,
        "regexp": r"^(\d{8})-(\d{8}).*-iptiq_claims\.csv$",
        "category": "claims",
    },
    {
        "name": "tuio_xlsx_complaints",
        "factory": TuioComplaintsXLSXToDataFrameFactory,
        "regexp": r"^Tuio_complaints_\d{4}\.xlsx$",
        "category": "complaints",
    },
]
