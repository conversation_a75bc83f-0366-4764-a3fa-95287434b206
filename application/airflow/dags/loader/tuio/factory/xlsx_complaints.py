from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.tuio.interface import TuioXLSXPandasInterface
from loader.tuio.dto.complaints import TuioComplaintsDTO


class TuioComplaintsXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = TuioXLSXPandasInterface(dto=TuioComplaintsDTO, sheet_names=["iptiq-complaints"])
