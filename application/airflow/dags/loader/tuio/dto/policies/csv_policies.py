import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime
import numpy as np


@dataclass(init=False, repr=False)
class TuioCSVPoliciesDTO:
    policy_reference: str = dataclasses.field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    policy_contract_reference: str = dataclasses.field(
        metadata={
            "source": "Policy Contract Reference",
            "mapping": "policy_contract_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for the contract",
        }
    )

    nature_of_contract: str = dataclasses.field(
        metadata={
            "source": "Nature of Contract",
            "mapping": "nature_of_contract",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "New Business, Cancellation, Substitution, etc.",
        }
    )

    underwriting_location: str = dataclasses.field(
        metadata={
            "source": "Location of Underwriting",
            "mapping": "underwriting_location",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "ISO code for Spain",
        }
    )

    risk_location: str = dataclasses.field(
        metadata={
            "source": "Location of Risk",
            "mapping": "risk_location",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "ISO code for Spain",
        }
    )

    class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class of business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II class (in {8, 9, 13, 17, 18})",
        }
    )

    insurance_cover: str = dataclasses.field(
        metadata={
            "source": "Insurance cover",
            "mapping": "insurance_cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover description, one line per cover",
        }
    )

    insured_value: str = dataclasses.field(
        metadata={
            "source": "Insured value",
            "mapping": "insured_value",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured value amount",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy start date",
        }
    )

    policy_end_date: str = dataclasses.field(
        metadata={
            "source": "Policy end date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy end date",
        }
    )

    period_start_date: str = dataclasses.field(
        metadata={
            "source": "Period begin date",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period start date",
        }
    )

    period_end_date: str = dataclasses.field(
        metadata={
            "source": "Period end date",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period end date",
        }
    )

    issuance_date: str = dataclasses.field(
        metadata={
            "source": "Issuance date",
            "mapping": "issuance_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date of issuance",
        }
    )

    payment_date: str = dataclasses.field(
        metadata={
            "source": "Payment date",
            "mapping": "payment_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Payment date; in some cases policies can be bought a few days or weeks in advance",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year; the year of the policy begin date",
        }
    )

    transaction_currency: str = dataclasses.field(
        metadata={
            "source": "Transaction Currency",
            "mapping": "transaction_currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "ISO code of the transaction currency",
        }
    )

    premium_without_taxes: str = dataclasses.field(
        metadata={
            "source": "Premium without Taxes",
            "mapping": "premium_without_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insurance premium without taxes",
        }
    )

    ipt_rate: str = dataclasses.field(
        metadata={
            "source": "IPT_Rate",
            "mapping": "ipt_rate",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insurance Premium Tax rate of 8%",
        }
    )

    ipt_amount: str = dataclasses.field(
        metadata={
            "source": "IPT_Amount",
            "mapping": "ipt_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insurance Premium Tax rate of 8%",
        }
    )

    premium_with_taxes: str = dataclasses.field(
        metadata={
            "source": "Premium with Taxes",
            "mapping": "premium_with_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium without taxes + all taxes",
        }
    )

    commission_amount: str = dataclasses.field(
        metadata={
            "source": "Commission_amount",
            "mapping": "commission_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount of iptiQ's commission",
        }
    )

    net_balance_due_to_iptiq: str = dataclasses.field(
        metadata={
            "source": "Net Balance due to IPTIQ",
            "mapping": "net_balance_due_to_iptiq",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Net Balance due to iptiQ",
        }
    )

    annual_premium_without_taxes: str = dataclasses.field(
        metadata={
            "source": "Annual Premium without Taxes",
            "mapping": "annual_premium_without_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annualised insurance premium without taxes",
        }
    )

    annual_tax_amount: str = dataclasses.field(
        metadata={
            "source": "Annual tax amount",
            "mapping": "annual_tax_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual tax amount",
        }
    )

    ipt_territory: str = dataclasses.field(
        metadata={
            "source": "IPT_Territory",
            "mapping": "ipt_territory",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Province, Local area (Mandatory if fire)",
        }
    )

    policyholder_name: str = dataclasses.field(
        metadata={
            "source": "Policyholder name / Insured Name",
            "mapping": "policyholder_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder: First name and Family name",
        }
    )

    policyholder_birthdate: str = dataclasses.field(
        metadata={
            "source": "Policyholder date of birth",
            "mapping": "policyholder_birthdate",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policyholder: Date of birth ",
        }
    )

    policyholder_tax_id: str = dataclasses.field(
        metadata={
            "source": "Policyholder Tax identification number",
            "mapping": "policyholder_tax_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder: tax identification number (NIF format)",
        }
    )

    payment_frequency: str = dataclasses.field(
        metadata={
            "source": "Payment Frequency",
            "mapping": "payment_frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The frequency of the payment ",
        }
    )

    distribution_channel: str = dataclasses.field(
        metadata={
            "source": "Distribution channel",
            "mapping": "distribution_channel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the policy was sold (e.g.:  Tuio_Website, Rastreator, Qlip, Masmovil, etc.)",
        }
    )

    policyholder_zip_code: str = dataclasses.field(
        metadata={
            "source": "zip_code_of_policy_holder",
            "mapping": "policyholder_zip_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder: zip code",
        }
    )

    policyholder_street: str = dataclasses.field(
        metadata={
            "source": "policyholder: street name",
            "mapping": "policyholder_street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder: street name",
        }
    )

    policyholder_number: str = dataclasses.field(
        metadata={
            "source": "policyholder: number",
            "mapping": "policyholder_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder: house number",
        }
    )

    insured_address_province: str = dataclasses.field(
        metadata={
            "source": "insured address: Province",
            "mapping": "insured_address_province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: province",
        }
    )

    insured_address_city: str = dataclasses.field(
        metadata={
            "source": "insured address: City",
            "mapping": "insured_address_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: city",
        }
    )

    insured_address_street: str = dataclasses.field(
        metadata={
            "source": "insured address: Street name",
            "mapping": "insured_address_street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: street name",
        }
    )

    insured_address_street_type: str = dataclasses.field(
        metadata={
            "source": "insured address: Street type",
            "mapping": "insured_address_street_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: street type",
        }
    )

    insured_address_number: str = dataclasses.field(
        metadata={
            "source": "insured_address: House number",
            "mapping": "insured_address_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: house number",
        }
    )

    insured_address_block: str = dataclasses.field(
        metadata={
            "source": "insured address: Block",
            "mapping": "insured_address_block",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: block",
        }
    )

    insured_address_staircase: str = dataclasses.field(
        metadata={
            "source": "insured address: Staircase",
            "mapping": "insured_address_staircase",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: staircase",
        }
    )

    insured_address_floor: str = dataclasses.field(
        metadata={
            "source": "insured address: Floor",
            "mapping": "insured_address_floor",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: floor",
        }
    )

    insured_address_door: str = dataclasses.field(
        metadata={
            "source": "insured address: Door",
            "mapping": "insured_address_door",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: door",
        }
    )

    insured_address_zip_code: str = dataclasses.field(
        metadata={
            "source": "insured address: Zip Code",
            "mapping": "insured_address_zip_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: zip code",
        }
    )

    building_type: str = dataclasses.field(
        metadata={
            "source": "Type of building",
            "mapping": "building_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Building type (e.g. HOUSE, APARTMENT)",
        }
    )

    use: str = dataclasses.field(
        metadata={
            "source": "Use",
            "mapping": "use",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Use of the property e.g RESIDENTIAL, RENTED,  SECOND_HOUSE, TENANT",
        }
    )

    square_meters: str = dataclasses.field(
        metadata={
            "source": "Square meters",
            "mapping": "square_meters",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Size of the property in square meters",
        }
    )

    construction_year: str = dataclasses.field(
        metadata={
            "source": "Construction Year",
            "mapping": "construction_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "The year of construction",
        }
    )

    house_type: str = dataclasses.field(
        metadata={
            "source": "House type",
            "mapping": "house_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of building",
        }
    )

    cover_level_deductible: str = dataclasses.field(
        metadata={
            "source": "cover_level_deductible",
            "mapping": "cover_level_deductible",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Deductible for the cover",
        }
    )

    modxx: str = dataclasses.field(
        metadata={
            "source": "modxx",
            "mapping": "modxx",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "MODxx for Property and Fire",
        }
    )

    clea: str = dataclasses.field(
        metadata={
            "source": "clea",
            "mapping": "clea",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Fund for Winding up of insurance companies also called MOD50 or CLEA",
        }
    )

    fire_brigade_charge_amount: str = dataclasses.field(
        metadata={
            "source": "fire_brigade_charge_amount",
            "mapping": "fire_brigade_charge_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Fire brigade charges",
        }
    )

    cancellation_reason: str = dataclasses.field(
        metadata={
            "source": "cancellation_reason",
            "mapping": "cancellation_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cancellation reason",
        }
    )

    # New columns added to the BDX since Feb 13, 2024 -> all previously sent BDXs reissued
    valuable_item_amount: str = dataclasses.field(
        metadata={
            "source": "valuable_item_amount",
            "mapping": "valuable_item_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount of valuable item insured",
        }
    )

    house_occupation: str = dataclasses.field(
        metadata={
            "source": "house_occupation",
            "mapping": "house_occupation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Specifies if the insured house is rented or owned",
        }
    )

    contract_start_date: str = dataclasses.field(
        metadata={
            "source": "contract_start_date",
            "mapping": "contract_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "The start date of the contract (as opposed to the main policy)",
        }
    )

    collection_group_status: str = dataclasses.field(
        metadata={
            "source": "collection_group_status",
            "mapping": "collection_group_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The status of the collection/payment",
        }
    )

    collection_id: str = dataclasses.field(
        metadata={
            "source": "collection_id",
            "mapping": "collection_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of the collection/payment",
        }
    )

    payment_confirmation_date: str = dataclasses.field(
        metadata={
            "source": "payment_confirmation_date",
            "mapping": "payment_confirmation_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the payment is fully confirmed",
        }
    )

    # A new column added to the BDX since Feb 27, 2024 -> all previously sent BDXs reissued
    policyholder_type: str = dataclasses.field(
        metadata={
            "source": "Policyholder Type",
            "mapping": "policyholder_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Separates individuals from legal entity policyholders",
        }
    )

    # Two new columns added to the BDX since April 24, requested by Finance team -> all previously sent BDXs reissued
    positive_liquidation: str = dataclasses.field(
        metadata={
            "source": "positive_liquidation",
            "mapping": "positive_liquidation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Mentions the batch the collection is liquidated in. Can be used for the reconciliation",
        }
    )

    negative_liquidation: str = dataclasses.field(
        metadata={
            "source": "negative_liquidation",
            "mapping": "negative_liquidation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "In case the collection payment is rejected after the first liquidation, mentions the batch "
            "of the negative liquidation. Can be used for the reconciliation",
        }
    )
