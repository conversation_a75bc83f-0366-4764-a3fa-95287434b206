import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, DateTime
import numpy as np


@dataclass(init=False, repr=False)
class TuioCSVClaimsDTO:
    policy_reference: str = dataclasses.field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    claim_reference: str = dataclasses.field(
        metadata={
            "source": "Claim Reference",
            "mapping": "claim_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each claim on <PERSON><PERSON>'s side",
        }
    )

    occurrence_date: str = dataclasses.field(
        metadata={
            "source": "Occurrence Date",
            "mapping": "occurrence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the damage (accident) occurs",
        }
    )

    occurrence_postcode: str = dataclasses.field(
        metadata={
            "source": "Postcode Occurrence",
            "mapping": "occurrence_postcode",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Postcode where the damage (accident) occurred",
        }
    )

    occurrence_city: str = dataclasses.field(
        metadata={
            "source": "City Occurrence",
            "mapping": "occurrence_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City where the damage (accident) occurred",
        }
    )

    occurrence_province: str = dataclasses.field(
        metadata={
            "source": "Province Occurrence",
            "mapping": "occurrence_province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Province where the damage (accident) occurred",
        }
    )

    insured_postcode: str = dataclasses.field(
        metadata={
            "source": "Postcode Insured",
            "mapping": "insured_postcode",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Postcode of the property insured",
        }
    )

    notification_date: str = dataclasses.field(
        metadata={
            "source": "Date Notified",
            "mapping": "notification_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the damage (accident) is notified to Tuio",
        }
    )

    opening_date: str = dataclasses.field(
        metadata={
            "source": "Opening Date",
            "mapping": "opening_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the claim is opened",
        }
    )

    last_review: str = dataclasses.field(
        metadata={
            "source": "Last Review",
            "mapping": "last_review",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the claim has last been reviewed by claim handler",
        }
    )

    claim_amount_agreed_date: str = dataclasses.field(
        metadata={
            "source": "Date Claim Amount Agreed",
            "mapping": "claim_amount_agreed_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the claim amount is agreed between the parties",
        }
    )

    claim_paid_date: str = dataclasses.field(
        metadata={
            "source": "Date Claims Paid",
            "mapping": "claim_paid_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the final payment is done",
        }
    )

    first_request_date: str = dataclasses.field(
        metadata={
            "source": "Date of First Request",
            "mapping": "first_request_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date of first request",
        }
    )

    insured: str = dataclasses.field(
        metadata={
            "source": "Insured",
            "mapping": "insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder information",
        }
    )

    insured_city: str = dataclasses.field(
        metadata={
            "source": "Insured City",
            "mapping": "insured_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder information",
        }
    )

    insured_province: str = dataclasses.field(
        metadata={
            "source": "Insured Province",
            "mapping": "insured_province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder information",
        }
    )

    class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class of Business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II class",
        }
    )

    coverage: str = dataclasses.field(
        metadata={
            "source": "Coverage",
            "mapping": "coverage",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The cover the damage is related with: Basic/Upgrade/Optional",
        }
    )

    claimant: str = dataclasses.field(
        metadata={
            "source": "Claimant",
            "mapping": "claimant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant",
        }
    )

    claimant_documentation_number: str = dataclasses.field(
        metadata={
            "source": "Claimant Documentation Number",
            "mapping": "claimant_documentation_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant Documentation Number",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "Policy Begin Date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy start date",
        }
    )

    policy_end_date: str = dataclasses.field(
        metadata={
            "source": "Policy End Date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy end date",
        }
    )

    period_start_date: str = dataclasses.field(
        metadata={
            "source": "Period Begin Date",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period start date",
        }
    )

    period_end_date: str = dataclasses.field(
        metadata={
            "source": "Period End Date",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period end date",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year should be the year of the Policy begin date (as no renewal)",
        }
    )

    complaint_received_date: str = dataclasses.field(
        metadata={
            "source": "Date Complaint Received",
            "mapping": "complaint_received_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when a complaint linked to this claim is received",
        }
    )

    claim_closing_date: str = dataclasses.field(
        metadata={
            "source": "Closing Date",
            "mapping": "claim_closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when the claim is closed",
        }
    )

    opened_claim_type: str = dataclasses.field(
        metadata={
            "source": "Opened Claim Type",
            "mapping": "opened_claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of claim at opening: MTPL - Mandatory  or MTPL - Mandatory & Voluntary",
        }
    )

    claim_type: str = dataclasses.field(
        metadata={
            "source": "Claim Type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of claim currently: MTPL - Mandatory  or MTPL - Mandatory & Voluntary",
        }
    )

    claim_description: str = dataclasses.field(
        metadata={
            "source": "Description of the Claim",
            "mapping": "claim_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "What has happened?",
        }
    )

    previous_claim_status: str = dataclasses.field(
        metadata={
            "source": "Claim Status at Start of the Month",
            "mapping": "previous_claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Status of the claim by start of the month",
        }
    )

    claim_status: str = dataclasses.field(
        metadata={
            "source": "Claim Status at End of the Month",
            "mapping": "claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Status of the claim by the end of the month",
        }
    )

    paid: str = dataclasses.field(
        metadata={
            "source": "Paid",
            "mapping": "paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid amount during the current month",
        }
    )

    claim_assessment_paid: str = dataclasses.field(
        metadata={
            "source": "Claim Assessment Paid",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim assessment (=Loss adjustment expenses) paid whether from 3rd parties, "
            "insured, etc. this month",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "Recovered",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount recovered whether from 3rd parties, insured, etc. this month",
        }
    )

    reserve: str = dataclasses.field(
        metadata={
            "source": "Reserved",
            "mapping": "reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Increase or Decrease of the Reserve amount during the current month",
        }
    )

    claim_assessment_reserve: str = dataclasses.field(
        metadata={
            "source": "Claim Assessment Reserves",
            "mapping": "claim_assessment_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim assessment (=Loss adjustment expenses) reserve whether from 3rd parties, "
            "insured, etc. this month",
        }
    )

    recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Recovery Reserve",
            "mapping": "recovery_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovery demand/reserves whether from 3rd parties, insured, etc. this month",
        }
    )

    total_incurred: str = dataclasses.field(
        metadata={
            "source": "Total Incurred",
            "mapping": "total_incurred",
            "dtype": np.dtype(str),  # To support the initial claim file that had strings instead of numbers
            "sqlalchemy_type": String,
            "description": "Total incurred for this claim at the end of the month",
        }
    )

    claim_referred: str = dataclasses.field(
        metadata={
            "source": "Claims Referred",
            "mapping": "claim_referred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "For claims above the threshold according to the delegated authority agreement",
        }
    )

    litigation: str = dataclasses.field(
        metadata={
            "source": "Litigation",
            "mapping": "litigation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is there litigation associated with this claim? ",
        }
    )

    recovery_date: str = dataclasses.field(
        metadata={
            "source": "Date of Recovery",
            "mapping": "recovery_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date to which the recovery relates to as recovery process can take several months",
        }
    )

    denial_reason: str = dataclasses.field(
        metadata={
            "source": "Reason for Denial",
            "mapping": "denial_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Reason of denial",
        }
    )

    deductible: str = dataclasses.field(
        metadata={
            "source": "Deductible",
            "mapping": "deductible",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Deductible",
        }
    )

    # New columns added to the BDX since Feb 27, 2024 -> all previously sent BDXs reissued
    policy_contract_reference: str = dataclasses.field(
        metadata={
            "source": "Policy Contract Reference",
            "mapping": "policy_contract_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each contract",
        }
    )
