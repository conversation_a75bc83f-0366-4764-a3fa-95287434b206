# This file is autogenerated

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class TuioComplaintsDTO:

    complaint_id: str = field(
        metadata={
            "source": "Complaint ID",
            "mapping": "complaint_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Id of complaint",
        }
    )

    url: str = field(
        metadata={
            "source": "URL",
            "mapping": "url",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Url",
        }
    )

    url_web: str = field(
        metadata={
            "source": "URL Web",
            "mapping": "url_web",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Url web",
        }
    )

    received_at: str = field(
        metadata={
            "source": "Received At",
            "mapping": "received_at",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when complaint was created",
        }
    )

    updated_at: str = field(
        metadata={
            "source": "Updated At",
            "mapping": "updated_at",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when complaint was updated",
        }
    )

    solved_at: str = field(
        metadata={
            "source": "Solved At",
            "mapping": "solved_at",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when complaint was closed",
        }
    )

    first_resolution_days: str = field(
        metadata={
            "source": "First Resolution Days",
            "mapping": "first_resolution_days",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Days count till resolution",
        }
    )

    status: str = field(
        metadata={
            "source": "Status",
            "mapping": "status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "State of complaint (closed, open, new)",
        }
    )

    claims_id_tentative: str = field(
        metadata={
            "source": "Claims ID Tentative",
            "mapping": "claims_id_tentative",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claims  Id Tentative",
        }
    )

    claim_id: str = field(
        metadata={
            "source": "Claim ID",
            "mapping": "claim_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim identifier related to the complaint",
        }
    )

    policy_id: str = field(
        metadata={
            "source": "Policy ID",
            "mapping": "policy_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy Id related to complaint",
        }
    )

    claim_type: str = field(
        metadata={
            "source": "Claim Type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Indicates the figure who created the complaint. See Category description.",
        }
    )

    complaint_summary: str = field(
        metadata={
            "source": "Complaint summary *",
            "mapping": "complaint_summary",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Summary of complaint",
        }
    )

    response_summary: str = field(
        metadata={
            "source": "Response summary *",
            "mapping": "response_summary",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Summary of response",
        }
    )

    type: str = field(
        metadata={
            "source": "Type",
            "mapping": "type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cause of complaint",
        }
    )

    resolution: str = field(
        metadata={
            "source": "Resolution",
            "mapping": "resolution",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Result of complaint",
        }
    )

    category: str = field(
        metadata={
            "source": "Category",
            "mapping": "category",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Descriptive form of Claim Type. Complaint, Advocate, Consumer Affairs, Regulator etc.",
        }
    )

    topic: str = field(
        metadata={
            "source": "Topic",
            "mapping": "topic",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Topic of complaint",
        }
    )

    compensation: str = field(
        metadata={
            "source": "Compensation",
            "mapping": "compensation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Compensation value",
        }
    )

    external_procedure: str = field(
        metadata={
            "source": "External Procedure",
            "mapping": "external_procedure",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is complaint under external procedure",
        }
    )

    settled_in_court: str = field(
        metadata={
            "source": "Settled in Court",
            "mapping": "settled_in_court",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is the complaint settled in court",
        }
    )

    caa_category: str = field(
        metadata={
            "source": "CAA Category",
            "mapping": "caa_category",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
