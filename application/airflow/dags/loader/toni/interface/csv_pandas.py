from typing import Callable
from loader.core.csv_pandas import CSVPandasInterface


class ToniCSVPandasInterface(CSVPandasInterface):
    # some edit need to be change for when leveraging other DTOs / filetype
    def __init__(self, dto):
        self._separator = ","
        self._encoding = "utf-8"
        self._header = 0
        self._decimal = "."
        self._thousands = None
        self._dto = dto()

    @classmethod
    def date_parser(cls) -> Callable:
        from dateutil.parser import parse

        skip_list = ["nan", "None"]
        return lambda x: parse(str(x)) if str(x) not in skip_list else None
