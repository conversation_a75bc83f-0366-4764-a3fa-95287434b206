# This file is autogenerated

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class ToniCSVPoliciesDTO:
    policy_number: str = field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    nature_of_contract: str = field(
        metadata={
            "source": "Nature of Contract",
            "mapping": "nature_of_contract",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Nature of contract, e.g. new business, endorsement, cancellation",
        }
    )

    transaction_sequence_number: str = field(
        metadata={
            "source": "Transaction Sequence Number",
            "mapping": "transaction_sequence_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "An incremental identifier for a policy, which increases by any changes to the policy",
        }
    )

    location_of_underwriting: str = field(
        metadata={
            "source": "Location of Underwriting",
            "mapping": "location_of_underwriting",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Location of underwriting, ISO code (e.g., CH)",
        }
    )

    location_of_risk: str = field(
        metadata={
            "source": "Location of Risk",
            "mapping": "location_of_risk",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Location of risk, ISO code (e.g., CH)",
        }
    )

    class_of_business: str = field(
        metadata={
            "source": "Class of business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Class of business, solvency II class",
        }
    )

    cover: str = field(
        metadata={
            "source": "Insurance cover",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance cover, e.g., basic_1_liability, basic_2_partially-comprehensive",
        }
    )

    cover_type: str = field(
        metadata={
            "source": "Type of insurance cover",
            "mapping": "cover_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of insurance cover, e.g., mandatory, optional",
        }
    )

    bundle: str = field(
        metadata={
            "source": "Bundle",
            "mapping": "bundle",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Bundle type, also known as package (e.g., full-kasko, partial-kasko)",
        }
    )

    deductible_amount: str = field(
        metadata={
            "source": "Deductible",
            "mapping": "deductible_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Deductible amount",
        }
    )

    insured_value: str = field(
        metadata={
            "source": "InsuredValue",
            "mapping": "insured_value",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Maximum amount to be payed in case of total loss",
        }
    )

    policy_start_date: str = field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy start date, YYYY-MM-DD HH:mm",
        }
    )

    policy_end_date: str = field(
        metadata={
            "source": "Policy end date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy end date, YYYY-MM-DD HH:mm",
        }
    )

    period_start_date: str = field(
        metadata={
            "source": "Period begin date",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period when the payment starts, YYYY-MM-DD HH:mm",
        }
    )

    period_end_date: str = field(
        metadata={
            "source": "Period end date",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period when the payment ends, YYYY-MM-DD HH:mm",
        }
    )

    issuance_date: str = field(
        metadata={
            "source": "Issuance date",
            "mapping": "issuance_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Issuance date of the policy, e.g., YYYY-MM-DD HH:mm",
        }
    )

    payment_date: str = field(
        metadata={
            "source": "Payment date",
            "mapping": "payment_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Payment date, YYYY-MM-DD HH:mm",
        }
    )

    underwriting_year: str = field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    transaction_currency: str = field(
        metadata={
            "source": "Transaction Currency",
            "mapping": "transaction_currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Transaction currency (e.g., CHF)",
        }
    )

    premium_without_taxes: str = field(
        metadata={
            "source": "Premium without taxes (and any parafiscal charges)",
            "mapping": "premium_without_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium without taxes",
        }
    )

    ipt_rate: str = field(
        metadata={
            "source": "IPT_Rate",
            "mapping": "ipt_rate",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance Premium Tax rate",
        }
    )

    ipt_amount: str = field(
        metadata={
            "source": "IPT_Amount",
            "mapping": "ipt_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insurance Premium Tax",
        }
    )

    swiss_accident_prevention_tax: str = field(
        metadata={
            "source": "Swiss Accident prevention contribution",
            "mapping": "swiss_accident_prevention_tax",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Swiss Accident prevention contribution",
        }
    )

    swiss_ngf: str = field(
        metadata={
            "source": "Swiss National Guarantee Fund (NGF)",
            "mapping": "swiss_ngf",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Swiss National Guarantee Fund (NGF)",
        }
    )

    swiss_nbi: str = field(
        metadata={
            "source": "Swiss National Bureau of Insurance (NBI)",
            "mapping": "swiss_nbi",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Swiss National Bureau of Insurance (NBI)",
        }
    )

    premium_with_taxes: str = field(
        metadata={
            "source": "Premium with taxes",
            "mapping": "premium_with_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium with taxes",
        }
    )

    commission_mga: str = field(
        metadata={
            "source": "Commission MGA",
            "mapping": "commission_mga",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Commission MGA",
        }
    )

    commission_distributor: str = field(
        metadata={
            "source": "Commission distributor",
            "mapping": "commission_distributor",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Commission distributor",
        }
    )

    installment_fee: str = field(
        metadata={
            "source": "InstalmentFee",
            "mapping": "installment_fee",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Installment fee",
        }
    )

    net_balance_due_iptiq: str = field(
        metadata={
            "source": "Net Balance due to IPTIQ",
            "mapping": "net_balance_due_iptiq",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Net balance due to iptiQ",
        }
    )

    annual_premium_without_taxes: str = field(
        metadata={
            "source": "Annualized Premium without taxes",
            "mapping": "annual_premium_without_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual premium without taxes",
        }
    )

    annual_tax_amount: str = field(
        metadata={
            "source": "Annual tax amount",
            "mapping": "annual_tax_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual tax amount",
        }
    )

    ph_country: str = field(
        metadata={
            "source": "Insurance Holder's Country",
            "mapping": "ph_country",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance holder's country",
        }
    )

    ph_canton: str = field(
        metadata={
            "source": "IPT_Territory of the Policyholder",
            "mapping": "ph_canton",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "IPT territory of the policyholder, e.g., ZH, BS",
        }
    )

    ph_first_name: str = field(
        metadata={
            "source": "Insurance Holder's First name",
            "mapping": "ph_first_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder first name",
        }
    )

    ph_last_name: str = field(
        metadata={
            "source": "Insurance Holder's Last name",
            "mapping": "ph_last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder last name",
        }
    )

    company_name: str = field(
        metadata={
            "source": "Company Name",
            "mapping": "company_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder Company name",
        }
    )

    ph_dob: str = field(
        metadata={
            "source": "Insurance Holder's Date of Birth",
            "mapping": "ph_dob",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policyholder date of birth,  YYYY-MM-DD",
        }
    )

    ph_address_street: str = field(
        metadata={
            "source": "Insurance Holder's Street name",
            "mapping": "ph_address_street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder street",
        }
    )

    ph_address_number: str = field(
        metadata={
            "source": "Insurance Holder's Street number",
            "mapping": "ph_address_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder street number",
        }
    )

    ph_address_postcode: str = field(
        metadata={
            "source": "Insurance Holder's Postcode",
            "mapping": "ph_address_postcode",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder postcode",
        }
    )

    ph_address_city: str = field(
        metadata={
            "source": "Insurance Holder's City",
            "mapping": "ph_address_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder city",
        }
    )

    ph_nationality: str = field(
        metadata={
            "source": "Insurance Holder's Nationality",
            "mapping": "ph_nationality",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder nationality",
        }
    )

    ph_residency_status: str = field(
        metadata={
            "source": "Insurance Holder's Residency Status",
            "mapping": "ph_residency_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder residency status",
        }
    )

    vehicle_owner_first_name: str = field(
        metadata={
            "source": "Vehicle Owner's First name",
            "mapping": "vehicle_owner_first_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle Owner's First name",
        }
    )

    vehicle_owner_last_name: str = field(
        metadata={
            "source": "Vehicle Owner's Last name",
            "mapping": "vehicle_owner_last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle Owner's Last name",
        }
    )

    vehicle_user_first_name: str = field(
        metadata={
            "source": "Car User's First name",
            "mapping": "vehicle_user_first_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's First name",
        }
    )

    vehicle_user_last_name: str = field(
        metadata={
            "source": "Car User's Last name",
            "mapping": "vehicle_user_last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's Last name",
        }
    )

    vehicle_user_dob: str = field(
        metadata={
            "source": "Car User's Date of Birth",
            "mapping": "vehicle_user_dob",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Car User's Date of Birth, YYYY-MM-DD",
        }
    )

    vehicle_user_gender: str = field(
        metadata={
            "source": "Car User's Gender",
            "mapping": "vehicle_user_gender",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's Gender, e.g., male, female",
        }
    )

    vehicle_user_address_street: str = field(
        metadata={
            "source": "Car User's Street name",
            "mapping": "vehicle_user_address_street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's Street name",
        }
    )

    vehicle_user_address_number: str = field(
        metadata={
            "source": "Car User's Street number",
            "mapping": "vehicle_user_address_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's Street number",
        }
    )

    vehicle_user_address_postcode: str = field(
        metadata={
            "source": "Car User's Postcode",
            "mapping": "vehicle_user_address_postcode",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's Postcode",
        }
    )

    vehicle_user_address_city: str = field(
        metadata={
            "source": "Car User's City",
            "mapping": "vehicle_user_address_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's City",
        }
    )

    vehicle_user_address_country: str = field(
        metadata={
            "source": "Car User's Country",
            "mapping": "vehicle_user_address_country",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car User's Country",
        }
    )

    driving_licence_date: str = field(
        metadata={
            "source": "Driving licence date",
            "mapping": "driving_licence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Driving licence issuance date",
        }
    )

    uw_claims_mtpl: str = field(
        metadata={
            "source": "Underwriting questions: how many claims MTPL (0, 1, 2, 3+)",
            "mapping": "uw_claims_mtpl",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Underwriting questions: how many claims MTPL (0, 1, 2, 3+)",
        }
    )

    uw_claims_casco: str = field(
        metadata={
            "source": "Underwriting questions: how many claims Casco (0, 1, 2, 3+)",
            "mapping": "uw_claims_casco",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Underwriting questions: how many claims Casco (0, 1, 2, 3+)",
        }
    )

    licence_plate_complete: str = field(
        metadata={
            "source": "Complete License Plate",
            "mapping": "licence_plate_complete",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complete License Plate (e.g., TI 123456)",
        }
    )

    licence_plate_canton: str = field(
        metadata={
            "source": "Swiss Kanton of the License Plate",
            "mapping": "licence_plate_canton",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Swiss Canton of the License Plate (e.g., TI, BE)",
        }
    )

    vehicle_brand: str = field(
        metadata={
            "source": "Brand",
            "mapping": "vehicle_brand",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle brand , e.g., VW, Toyota",
        }
    )

    vehicle_model: str = field(
        metadata={
            "source": "Model",
            "mapping": "vehicle_model",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle model",
        }
    )

    vehicle_registration_date: str = field(
        metadata={
            "source": "First registration of the vehicle",
            "mapping": "vehicle_registration_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "First registration of the vehicle",
        }
    )

    vehicle_catalogue_value: str = field(
        metadata={
            "source": "Catalogue value of the vehicle / Vehicle value",
            "mapping": "vehicle_catalogue_value",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Catalogue value of the vehicle / Vehicle value",
        }
    )

    vehicle_accessories_value: str = field(
        metadata={
            "source": "Actual value of the accessories / Vehicle value",
            "mapping": "vehicle_accessories_value",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Actual value of the accessories / Vehicle value",
        }
    )

    vehicle_horse_power: str = field(
        metadata={
            "source": "Horse power of the vehicle",
            "mapping": "vehicle_horse_power",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Horse power of the vehicle",
        }
    )

    vehicle_type_class: str = field(
        metadata={
            "source": "Typklasse",
            "mapping": "vehicle_type_class",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle Typklasse",
        }
    )

    vehicle_ccm: str = field(
        metadata={
            "source": "Displacement in ccm",
            "mapping": "vehicle_ccm",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle displacement in ccm",
        }
    )

    vehicle_weight: str = field(
        metadata={
            "source": "Total Vehicle weight in kilograms",
            "mapping": "vehicle_weight",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Total Vehicle weight in kilograms",
        }
    )

    vehicle_fuel: str = field(
        metadata={
            "source": "Type of fuel",
            "mapping": "vehicle_fuel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle type of fuel",
        }
    )

    vehicle_category: str = field(
        metadata={
            "source": "Category",
            "mapping": "vehicle_category",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle category",
        }
    )

    vehicle_yearly_mileage: str = field(
        metadata={
            "source": "Yearly mileage",
            "mapping": "vehicle_yearly_mileage",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Vehicle yearly mileage, e.g., 10000-14000",
        }
    )

    vehicle_intended_use: str = field(
        metadata={
            "source": "Intended use of the vehicle",
            "mapping": "vehicle_intended_use",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Intended use of the vehicle, e.g., commute, private, business",
        }
    )

    is_vehicle_leased: str = field(
        metadata={
            "source": "Is the vehicle leased?",
            "mapping": "is_vehicle_leased",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is the vehicle leased, e.g., Yes, No",
        }
    )

    vehicle_num_young_drivers: str = field(
        metadata={
            "source": "Number drivers between 17-25",
            "mapping": "vehicle_num_young_drivers",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Number drivers between 17-25",
        }
    )

    payment_installment: str = field(
        metadata={
            "source": "Payment Instalment",
            "mapping": "payment_installment",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment installment",
        }
    )

    payment_invoice_id: str = field(
        metadata={
            "source": "Payment invoice id",
            "mapping": "payment_invoice_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment invoice id",
        }
    )

    payment_frequency: str = field(
        metadata={
            "source": "Payment Frequency",
            "mapping": "payment_frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment Frequency, e.g., annually, semiannually, etc.",
        }
    )

    distribution_channel: str = field(
        metadata={
            "source": "DistributionPartner",
            "mapping": "distribution_channel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution channel",
        }
    )

    distribution_channel_1: str = field(
        metadata={
            "source": "Distribution channel (Level 1)",
            "mapping": "distribution_channel_1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution channel (Level 1)",
        }
    )

    distribution_channel_2: str = field(
        metadata={
            "source": "Distribution channel (Level 2)",
            "mapping": "distribution_channel_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution channel (Level 2)",
        }
    )

    distribution_channel_3: str = field(
        metadata={
            "source": "Distribution channel (Level 3)",
            "mapping": "distribution_channel_3",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution channel (Level 3)",
        }
    )

    bonus_malus_main_driver: str = field(
        metadata={
            "source": "Bonus-Malus of the main driver",
            "mapping": "bonus_malus_main_driver",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Bonus-Malus of the main driver",
        }
    )

    cancellation_reason: str = field(
        metadata={
            "source": "Cancellation reason",
            "mapping": "cancellation_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cancellation reason",
        }
    )
