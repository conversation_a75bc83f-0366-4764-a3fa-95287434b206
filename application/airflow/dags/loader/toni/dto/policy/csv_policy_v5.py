# This file is autogenerated

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.toni.dto.policy import ToniCSVPoliciesDTOV4


@dataclass(init=False, repr=False)
class ToniCSVPoliciesDTOV5(ToniCSVPoliciesDTOV4):
    old_policy_number: str = field(
        metadata={
            "source": "Old Policy Number",
            "mapping": "old_policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The original policy number for policies that are assinged a new policy number with renewal",
        }
    )
