# This file is autogenerated

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.toni.dto.policy import ToniCSVPoliciesDTOV2


@dataclass(init=False, repr=False)
class ToniCSVPoliciesDTOV3(ToniCSVPoliciesDTOV2):
    distribution_channel_1: str = field(
        metadata={
            "source": "Distribution channel",
            "mapping": "distribution_channel_1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution channel (Level 1)",
        }
    )
