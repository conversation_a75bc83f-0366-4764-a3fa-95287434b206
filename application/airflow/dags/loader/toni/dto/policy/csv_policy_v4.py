# This file is autogenerated

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.toni.dto.policy import ToniCSVPoliciesDTOV3


@dataclass(init=False, repr=False)
class ToniCSVPoliciesDTOV4(ToniCSVPoliciesDTOV3):
    policy_origin: str = field(
        metadata={
            "source": "Policy Origin",
            "mapping": "policy_origin",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Shows if the policy is a new business or reappearing (part of portfolio transfer)",
        }
    )
