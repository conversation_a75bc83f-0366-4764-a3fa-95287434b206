# This file is autogenerated

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.toni.dto.policy import ToniCSVPoliciesDTO


@dataclass(init=False, repr=False)
class ToniCSVPoliciesDTOV2(ToniCSVPoliciesDTO):
    service_center_fee: str = field(
        metadata={
            "source": "Service Center Fee",
            "mapping": "service_center_fee",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The extra fee paid to the MGA on top of the commission",
        }
    )
