import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, String  # type: ignore


@dataclass(init=False, repr=False)
class ToniCSVClaimsDTO:
    policy_number: str = dataclasses.field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    claim_number: str = dataclasses.field(
        metadata={
            "source": "Claim reference",
            "mapping": "claim_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each claim",
        }
    )

    tpa: str = dataclasses.field(
        metadata={
            "source": "TPA",
            "mapping": "tpa",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "TPA name",
        }
    )

    tpa_number: str = dataclasses.field(
        metadata={
            "source": "TPA Claim reference",
            "mapping": "tpa_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for TPA",
        }
    )

    occurrence_date: str = dataclasses.field(
        metadata={
            "source": "Occurrence_date",
            "mapping": "occurence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim Event date - When the event = cause took place",
        }
    )

    postcode_occurrence: str = dataclasses.field(
        metadata={
            "source": "Postcode_occurence",
            "mapping": "postcode_occurence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - Postcode",
        }
    )

    city_occurrence: str = dataclasses.field(
        metadata={
            "source": "City_occurence",
            "mapping": "city_occurence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - City",
        }
    )

    canton_occurrence: str = dataclasses.field(
        metadata={
            "source": "Canton_occurrence",
            "mapping": "canton_occurrence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - Canton",
        }
    )

    date_notified_to_toni: str = dataclasses.field(
        metadata={
            "source": "Date_notified",
            "mapping": "date_notified_to_toni",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date that claim was notified to Toni",
        }
    )

    opening_date: str = dataclasses.field(
        metadata={
            "source": "Opening_date",
            "mapping": "opening_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim opening date",
        }
    )

    last_review_date: str = dataclasses.field(
        metadata={
            "source": "Last_review",
            "mapping": "last_review_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim last review date",
        }
    )

    claim_paid_date: str = dataclasses.field(
        metadata={
            "source": "Date_Claims_Paid (Final)",
            "mapping": "claim_paid_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date that the claim was paid",
        }
    )

    insured: str = dataclasses.field(
        metadata={
            "source": "Insured_person",
            "mapping": "insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the insured",
        }
    )

    insured_city: str = dataclasses.field(
        metadata={
            "source": "Insured_city",
            "mapping": "insured_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the insured",
        }
    )

    insured_canton: str = dataclasses.field(
        metadata={
            "source": "Insured_canton",
            "mapping": "insured_canton",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Canton of the insured",
        }
    )

    class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class_of_business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II Class of business, listed as a text field e.g. Motor Third Party Liability",
        }
    )

    damage_type: str = dataclasses.field(
        metadata={
            "source": "Damage Type",
            "mapping": "damage_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Damage type category and description",
        }
    )

    cover: str = dataclasses.field(
        metadata={
            "source": "coverage /Insurance cover",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance cover",
        }
    )

    claimant: str = dataclasses.field(
        metadata={
            "source": "Claimant",
            "mapping": "claimant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant",
        }
    )

    claimant_ssn: str = dataclasses.field(
        metadata={
            "source": "Social Security #/AHV of the Claimant",
            "mapping": "claimant_ssn",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant Social Security number",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy begin date",
        }
    )

    policy_end_date: str = dataclasses.field(
        metadata={
            "source": "Policy end date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy end date",
        }
    )

    period_start_date: str = dataclasses.field(
        metadata={
            "source": "Period begin date",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Period begin date",
        }
    )

    period_end_date: str = dataclasses.field(
        metadata={
            "source": "Period end date",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Period end date",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Underwriting year",
        }
    )

    transaction_sequence_number: str = dataclasses.field(
        metadata={
            "source": "Transaction Sequence Number",
            "mapping": "transaction_sequence_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Transaction Sequence Number",
        }
    )

    date_complaint_received: str = dataclasses.field(
        metadata={
            "source": "Date_complaint_received",
            "mapping": "date_complaint_received",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date that complaint was received",
        }
    )

    closing_date: str = dataclasses.field(
        metadata={
            "source": "Closing_date",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing date",
        }
    )

    vehicle_registration_number: str = dataclasses.field(
        metadata={
            "source": "Car plate",
            "mapping": "vehicle_registration_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car plate",
        }
    )

    claim_description: str = dataclasses.field(
        metadata={
            "source": "Description of the claim",
            "mapping": "claim_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of the claim",
        }
    )

    presence_bi: str = dataclasses.field(
        metadata={
            "source": "Presence_Bodily Injury",
            "mapping": "presence_pd",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Bodily Injury (question)",
        }
    )

    presence_pd: str = dataclasses.field(
        metadata={
            "source": "Presence_Property Damage",
            "mapping": "presence_pd",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Property Damage (question)",
        }
    )

    repairer: str = dataclasses.field(
        metadata={
            "source": "Repairer",
            "mapping": "repairer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the company that repairs damages",
        }
    )

    claim_status_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_start_of_the_month",
            "mapping": "claim_status_at_start_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status at the start of the month",
        }
    )

    claim_status_at_end_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_end_of_the_month",
            "mapping": "claim_status_at_end_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status at the end of the month",
        }
    )

    claim_reopened: str = dataclasses.field(
        metadata={
            "source": "Reopened",
            "mapping": "claim_reopened",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "If claim has been reopened",
        }
    )

    claim_beneficiary: str = dataclasses.field(
        metadata={
            "source": "Claim beneficiary",
            "mapping": "claim_beneficiary",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Person or Entity that benefits the claim",
        }
    )

    paid: str = dataclasses.field(
        metadata={
            "source": "Paid",
            "mapping": "paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount",
        }
    )

    paid_vat: str = dataclasses.field(
        metadata={
            "source": "Paid_VAT",
            "mapping": "paid_vat",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount of the VAT tax",
        }
    )

    claim_assessment_paid: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for claim assessment costs",
        }
    )

    claim_handling_fee_paid_this_month: str = dataclasses.field(
        metadata={
            "source": "claim_handling_fee_paid_this_month",
            "mapping": "claim_handling_fee_paid_this_month",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for claim handling costs",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "Recovered",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the recovery received during the month",
        }
    )

    recovered_vat: str = dataclasses.field(
        metadata={
            "source": "Recovered_VAT",
            "mapping": "recovered_vat",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the VAT amount on the recovery received during the month",
        }
    )

    retention: str = dataclasses.field(
        metadata={
            "source": "Retention",
            "mapping": "retention",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the retention amount during the month",
        }
    )

    reserved: str = dataclasses.field(
        metadata={
            "source": "Reserved",
            "mapping": "reserved",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the reserve amount during the month",
        }
    )

    kickback: str = dataclasses.field(
        metadata={
            "source": "Kickback",
            "mapping": "kickback",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the kickback amount during the month",
        }
    )

    claim_assessment_reserves: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_reserves",
            "mapping": "claim_assessment_reserves",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the claim assessment reserve during the month",
        }
    )

    claim_handling_fee_reserve: str = dataclasses.field(
        metadata={
            "source": "claim_handling_fee_reserve",
            "mapping": "claim_handling_fee_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the claim handling reserve during the month",
        }
    )

    recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Recovery_reserve",
            "mapping": "recovery_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Changes to the recovery reserve amount during the month",
        }
    )

    paid_prev: str = dataclasses.field(
        metadata={
            "source": "Paid_prev",
            "mapping": "paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount that was already paid (by the start of the month)",
        }
    )

    paid_prev_vat: str = dataclasses.field(
        metadata={
            "source": "Paid_VAT_prev",
            "mapping": "paid_prev_vat",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Tax Amount that was already paid (by the start of the month)",
        }
    )

    claim_assessment_paid_prev: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid_prev",
            "mapping": "claim_assessment_paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim assessment amount that was already paid (by the start of the month)",
        }
    )

    claim_handling_fee_paid_prev: str = dataclasses.field(
        metadata={
            "source": "claim_handling_fee_paid_prev",
            "mapping": "claim_handling_fee_paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim handling amount that was already paid (by the start of the month)",
        }
    )

    recovered_prev: str = dataclasses.field(
        metadata={
            "source": "Recovered_prev",
            "mapping": "recovered_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovery amount that was already received (by the start of the month)",
        }
    )

    recovered_prev_vat: str = dataclasses.field(
        metadata={
            "source": "Recovered_VAT_prev",
            "mapping": "recovered_prev_vat",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "VAT on the recovery amount that was already received (by the start of the month)",
        }
    )

    retention_prev: str = dataclasses.field(
        metadata={
            "source": "Retention_prev",
            "mapping": "retention_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the previous amount in case of new retention received (by the start of the month)",
        }
    )

    reserved_prev: str = dataclasses.field(
        metadata={
            "source": "Reserved_prev",
            "mapping": "reserved_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Reserve amount by the start of the month",
        }
    )

    kickback_prev: str = dataclasses.field(
        metadata={
            "source": "Kickback_prev",
            "mapping": "kickback_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Kickback received by the start of the month",
        }
    )

    claim_assessment_reserves_prev: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_reserves_prev",
            "mapping": "claim_assessment_reserves_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim assessment reserve by the start of the month",
        }
    )

    claim_handling_fee_reserve_prev: str = dataclasses.field(
        metadata={
            "source": "claim_handling_fee_reserve_prev",
            "mapping": "claim_handling_fee_reserve_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim handling fee reserve by the start of the month",
        }
    )

    recovery_reserve_prev: str = dataclasses.field(
        metadata={
            "source": "Recovery_reserve_prev",
            "mapping": "recovery_reserve_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovery reserve by the start of the month",
        }
    )

    total_incurred: str = dataclasses.field(
        metadata={
            "source": "Total_incurred",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )

    claims_referred: str = dataclasses.field(
        metadata={
            "source": "Claims_referred",
            "mapping": "claims_referred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claims_referred",
        }
    )

    litigation: str = dataclasses.field(
        metadata={
            "source": "Litigation",
            "mapping": "litigation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "If there is a legal action against the insurance company for the payment of the claim",
        }
    )

    date_of_recovery: str = dataclasses.field(
        metadata={
            "source": "Date of recovery",
            "mapping": "date_of_recovery",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of recovery",
        }
    )
