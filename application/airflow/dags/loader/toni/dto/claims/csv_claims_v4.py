import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, String  # type: ignore

from loader.toni.dto.claims import ToniCSVClaimsDTO


@dataclass(init=False, repr=False)
class ToniCSVClaimsDTOV4(ToniCSVClaimsDTO):
    retention_reserve: str = dataclasses.field(
        metadata={
            "source": "Retention_reserve",
            "mapping": "retention_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Retention reserve changes during the month",
        }
    )

    retention_reserve_prev: str = dataclasses.field(
        metadata={
            "source": "Retention_reserve_prev",
            "mapping": "retention_reserve_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Retention reserves up to the start of the month",
        }
    )
