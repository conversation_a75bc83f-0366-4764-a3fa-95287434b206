import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, String  # type: ignore

from loader.toni.dto.claims import ToniCSVClaimsDTO


@dataclass(init=False, repr=False)
class ToniCSVClaimsDTOException(ToniCSVClaimsDTO):
    closing_date: str = dataclasses.field(
        metadata={
            "source": "Closing _Date",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing date",
        }
    )
