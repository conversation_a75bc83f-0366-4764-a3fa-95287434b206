import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, String  # type: ignore

from loader.toni.dto.claims import ToniCSVClaimsDTO


@dataclass(init=False, repr=False)
class ToniCSVClaimsDTOV3(ToniCSVClaimsDTO):
    closing_date: str = dataclasses.field(
        metadata={
            "source": "ClosingDate",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing date",
        }
    )

    insured_city: str = dataclasses.field(
        metadata={
            "source": "Insured_city.1",
            "mapping": "insured_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the insured",
        }
    )
