from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.toni.interface import ToniCS<PERSON>andasInterface
from loader.toni.dto.claims import ToniCSVClaimsDTOException


class ToniClaimsCSVToDataFrameFactoryException(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = ToniCSVPandasInterface(dto=ToniCSVClaimsDTOException)
