from loader.toni.factory import *

file_types = [
    {
        "name": "toni_csv_policy",
        "factory": ToniPolicyCSVToDataFrameFactory,
        "regexp": r"^202401_iptiQ_policy_premium_bdx\.csv$",
        "category": "policies",
    },
    {   # A new column has been added to separate service center costs
        "name": "toni_csv_policy",
        "factory": ToniPolicyCSVToDataFrameFactoryV2,
        "regexp": r"^202401_iptiQ_policy_premium_bdx_adjusted\.csv$",
        "category": "policies",
    },
    {   # "Distribution channel (Level 1)" -> "Distribution channel"
        "name": "toni_csv_policy",
        "factory": ToniPolicyCSVToDataFrameFactoryV3,
        "regexp": r"^20240[1-9]_iptiQ_policy_premium_bdx\.csv$",
        "category": "policies",
    },
    {   # A new column (Policy Origin) is added for marking portfolio transfer policies
        "name": "toni_csv_policy",
        "factory": ToniPolicyCSVToDataFrameFactoryV4,
        "regexp": r"^2024(0[1-9]|10|11)_iptiQ_policy_premium_bdx.*\.csv$",
        "category": "policies",
    },
    {   # A new column (Old Policy Number) is added to show the original policy for renewals with new policy number
        "name": "toni_csv_policy",
        "factory": ToniPolicyCSVToDataFrameFactoryV5,
        "regexp": r"^\d{6}_iptiQ_policy_premium_bdx.*\.csv$",
        "category": "policies",
    },
    {   # Seems to be an exceptional BDX where "Closing_date" -> "Closing _Date"
        "name": "toni_claims",
        "factory": ToniClaimsCSVToDataFrameFactoryException,
        "regexp": r"^202403_iptiQ_claim_bdx\.csv$",
        "category": "claims",
    },
    {
        "name": "toni_claims",
        "factory": ToniClaimsCSVToDataFrameFactory,
        "regexp": r"^20240[12]_iptiQ_claim_bdx\.csv$",
        "category": "claims",
    },
    # Change in the closing_date column to Closing_Date
    {
        "name": "toni_claims",
        "factory": ToniClaimsCSVToDataFrameFactoryV2,
        "regexp": r"^202404_iptiQ_claim_bdx\.csv$",
        "category": "claims",
    },
    # Closing_Date -> ClosingDate, Insured_city -> Insured_city.1
    {
        "name": "toni_claims",
        "factory": ToniClaimsCSVToDataFrameFactoryV3,
        "regexp": r"^202405_iptiQ_claim_bdx\.csv$",
        "category": "claims",
    },
    # Back to the initial version
    {
        "name": "toni_claims",
        "factory": ToniClaimsCSVToDataFrameFactory,
        "regexp": r"^2024(?:0[1-9]|1[01])_iptiQ_claim_bdx\.csv$",
        "category": "claims",
    },
    # Retention reserve columns added since 202412 bordereau
    {
        "name": "toni_claims",
        "factory": ToniClaimsCSVToDataFrameFactoryV4,
        "regexp": r"^\d{6}_iptiQ_claim_bdx\.csv$",
        "category": "claims",
    }
]
