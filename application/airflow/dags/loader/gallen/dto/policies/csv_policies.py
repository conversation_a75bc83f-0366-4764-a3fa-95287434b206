from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class GallenCSVPoliciesDTO:

    policy_number: str = field(
        metadata={
            "source": "Número de póliza",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy Reference",
        }
    )

    nature_of_contract: str = field(
        metadata={
            "source": "Tipo contrato",
            "mapping": "nature_of_contract",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Nature of Contract",
        }
    )

    location_of_underwriting: str = field(
        metadata={
            "source": "Localización de la suscripción",
            "mapping": "location_of_underwriting",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Location of Underwriting",
        }
    )

    location_of_risk: str = field(
        metadata={
            "source": "Localización del riesgo",
            "mapping": "location_of_risk",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Location of Risk",
        }
    )

    class_of_business: str = field(
        metadata={
            "source": "Garantia Solvencia",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Class of business",
        }
    )

    cover: str = field(
        metadata={
            "source": "Coberturas",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance cover",
        }
    )

    insured_value: str = field(
        metadata={
            "source": "Capital asegurado",
            "mapping": "insured_value",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured value",
        }
    )

    rent_per_month: str = field(
        metadata={
            "source": "Renta alquiler mensual",
            "mapping": "rent_per_month",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Rent amount",
        }
    )

    months_insured: str = field(
        metadata={
            "source": "Número de mensualidades a asegurar",
            "mapping": "months_insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Number of months to be insured",
        }
    )

    loss_of_rent_insured_value: str = field(
        metadata={
            "source": "Capital asegurado por Seguro de impago de alquileres",
            "mapping": "loss_of_rent_insured_value",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured value for loss of rent",
        }
    )

    policy_start_date: str = field(
        metadata={
            "source": "Fecha y hora de inicio del contrato",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy begin date",
        }
    )

    policy_end_date: str = field(
        metadata={
            "source": "Fecha y hora de finalización del contrato",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy end date",
        }
    )

    period_start_date: str = field(
        metadata={
            "source": "Fecha inicio movimiento",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period begin date",
        }
    )

    period_end_date: str = field(
        metadata={
            "source": "Fecha fin movimiento",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Period end date",
        }
    )

    issuance_date: str = field(
        metadata={
            "source": "Fecha y hora de la contratación",
            "mapping": "issuance_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Issuance date",
        }
    )

    payment_date: str = field(
        metadata={
            "source": "Fecha de Pago",
            "mapping": "payment_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Payment date",
        }
    )

    underwriting_year: str = field(
        metadata={
            "source": "Año de suscripción",
            "mapping": "underwriting_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Underwriting Year",
        }
    )

    transaction_currency: str = field(
        metadata={
            "source": "Divisa",
            "mapping": "transaction_currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Transaction Currency",
        }
    )

    premium_without_taxes: str = field(
        metadata={
            "source": "Prima sin impuestos",
            "mapping": "premium_without_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium without taxes",
        }
    )

    ipt_rate: str = field(
        metadata={
            "source": "Impuesto sobre las Primas de Seguros (IPSrate)",
            "mapping": "ipt_rate",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "IPT Rate",
        }
    )

    ipt_amount: str = field(
        metadata={
            "source": "Impuesto sobre las Primas de Seguros (IPS)",
            "mapping": "ipt_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "IPT Amount",
        }
    )

    premium_with_taxes: str = field(
        metadata={
            "source": "Prima total del contrato",
            "mapping": "premium_with_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium with taxes",
        }
    )

    commission_amount: str = field(
        metadata={
            "source": "Comision a la Aseguradora (EUR)",
            "mapping": "commission_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Commission amount",
        }
    )

    commission_rate: str = field(
        metadata={
            "source": "Comision a la Aseguradora (%)",
            "mapping": "commission_rate",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Commission rate",
        }
    )

    net_balance_due_iptiq: str = field(
        metadata={
            "source": "IptiQ neto",
            "mapping": "net_balance_due_iptiq",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Net Balance due to IPTIQ",
        }
    )

    annual_premium_without_taxes: str = field(
        metadata={
            "source": "Primas anuales sin impuestos",
            "mapping": "annual_premium_without_taxes",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual Premium without taxes",
        }
    )

    annual_tax_amount: str = field(
        metadata={
            "source": "Impuesto anuales",
            "mapping": "annual_tax_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual tax amount",
        }
    )

    policyholder_name: str = field(
        metadata={
            "source": "Tomador de la póliza",
            "mapping": "policyholder_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder name",
        }
    )

    ph_dob: str = field(
        metadata={
            "source": "Fecha de nacimiento del tomador",
            "mapping": "ph_dob",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder date of birth",
        }
    )

    policyholder_tax_id: str = field(
        metadata={
            "source": "NIF tomador",
            "mapping": "policyholder_tax_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder Tax identification number (NIF)",
        }
    )

    number_of_tenants: str = field(
        metadata={
            "source": "Nombre del arrendatario",
            "mapping": "number_of_tenants",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Number of tenants",
        }
    )

    renter_dob: str = field(
        metadata={
            "source": "Fecha de nacimiento del arrendatario",
            "mapping": "renter_dob",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Renter's date of birth",
        }
    )

    renter_tax_id: str = field(
        metadata={
            "source": "Arrendatario NIF",
            "mapping": "renter_tax_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Renter's Tax identification number (NIF)",
        }
    )

    owner_name: str = field(
        metadata={
            "source": "Nombre del propietario",
            "mapping": "owner_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Owner's name",
        }
    )

    owner_dob: str = field(
        metadata={
            "source": "Fecha de nacimiento del propietarioPropietario NIF",
            "mapping": "owner_dob",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Owner's date of birth + Tax ID - To be changed",
        }
    )

    installment_fee: str = field(
        metadata={
            "source": "Número de pago",
            "mapping": "installment_fee",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment Instalment",
        }
    )

    payment_frequency: str = field(
        metadata={
            "source": "Fraccionamiento de pago",
            "mapping": "payment_frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment Frequency",
        }
    )

    distribution_channel: str = field(
        metadata={
            "source": "Canal de distribución",
            "mapping": "distribution_channel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution Channel",
        }
    )

    distribution_broker: str = field(
        metadata={
            "source": "Broker de distribución",
            "mapping": "distribution_broker",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Distribution Broker",
        }
    )

    ph_address_postcode: str = field(
        metadata={
            "source": "Código postal del tomador",
            "mapping": "ph_address_postcode",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder postcode",
        }
    )

    ph_address_street: str = field(
        metadata={
            "source": "Tomador: Via",
            "mapping": "ph_address_street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder street name",
        }
    )

    ph_address_number: str = field(
        metadata={
            "source": "Tomador: Número",
            "mapping": "ph_address_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policyholder number",
        }
    )

    insured_address_province: str = field(
        metadata={
            "source": "Provincia",
            "mapping": "insured_address_province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Province",
        }
    )

    insured_address_city: str = field(
        metadata={
            "source": "Municipio",
            "mapping": "insured_address_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: City",
        }
    )

    insured_address_street: str = field(
        metadata={
            "source": "Via",
            "mapping": "insured_address_street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Street name",
        }
    )

    insured_address_street_type: str = field(
        metadata={
            "source": "Tipo de calle",
            "mapping": "insured_address_street_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Street type",
        }
    )

    insured_address_number: str = field(
        metadata={
            "source": "Número",
            "mapping": "insured_address_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: House number",
        }
    )

    insured_address_block: str = field(
        metadata={
            "source": "Bloque",
            "mapping": "insured_address_block",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Block",
        }
    )

    insured_address_staircase: str = field(
        metadata={
            "source": "Escalera",
            "mapping": "insured_address_staircase",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Staricase",
        }
    )

    insured_address_floor: str = field(
        metadata={
            "source": "Planta",
            "mapping": "insured_address_floor",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Floor",
        }
    )

    insured_address_door: str = field(
        metadata={
            "source": "Puerta",
            "mapping": "insured_address_door",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Door",
        }
    )

    insured_address_postcode: str = field(
        metadata={
            "source": "Código postal",
            "mapping": "insured_address_postcode",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured address: Zip Code",
        }
    )

    type_of_building: str = field(
        metadata={
            "source": "Tipo de edificio",
            "mapping": "type_of_building",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of building",
        }
    )

    cover_level_deductible: str = field(
        metadata={
            "source": "Franquicia de la cobertura",
            "mapping": "cover_level_deductible",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover level deductible",
        }
    )

    modxx_property_fire: str = field(
        metadata={
            "source": "CCS - REX",
            "mapping": "modxx_property_fire",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "CCS - REX",
        }
    )

    modxx_misc_financial_loss: str = field(
        metadata={
            "source": "CCS - REX Danos Proprios",
            "mapping": "modxx_misc_financial_loss",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "EOR Extraordinary Risk also called Exceptional surcharge also called"
                           " MODxx for Miscellaneous financial loss",
        }
    )

    clea: str = field(
        metadata={
            "source": "Comisión Liquidadora de Entidades Aseguradoras(CLEA)",
            "mapping": "clea",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Fund for Winding up of insurance companies also called MOD50 or CLEA",
        }
    )

    policy_number_renewal: str = field(
        metadata={
            "source": "Número de póliza - renovación",
            "mapping": "policy_number_renewal",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy number renewal",
        }
    )

    cancellation_reason: str = field(
        metadata={
            "source": "Motivo de la cancelación",
            "mapping": "cancellation_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cancellation reason",
        }
    )
