from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.gallen.dto.claims import GallenXLSXClaimsDTO


@dataclass(init=False, repr=False)
class GallenXLSXClaimsDTOV2(GallenXLSXClaimsDTO):

    paid_prev: str = field(
        metadata={
            "source": "Paid Prev",
            "mapping": "paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Previous paid amount",
        }
    )
