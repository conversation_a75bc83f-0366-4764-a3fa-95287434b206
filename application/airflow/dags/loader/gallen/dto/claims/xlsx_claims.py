from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class GallenXLSXClaimsDTO:

    policy_number: str = field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy Reference",
        }
    )

    claim_number: str = field(
        metadata={
            "source": "Claim reference",
            "mapping": "claim_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim Reference",
        }
    )

    tpa_claim_number: str = field(
        metadata={
            "source": "TPA Claim reference",
            "mapping": "location_of_underwriting",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Third party claim reference",
        }
    )

    occurrence_date: str = field(
        metadata={
            "source": "Occurrence_date",
            "mapping": "occurrence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Occurrence Date",
        }
    )

    postcode_loss: str = field(
        metadata={
            "source": "Postcode_occurence",
            "mapping": "postcode_loss",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Postcode of the incident",
        }
    )

    city_occurence: str = field(
        metadata={
            "source": "City_occurence",
            "mapping": "city_occurence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the incident",
        }
    )

    prov_occurrence: str = field(
        metadata={
            "source": "Prov_occurrence",
            "mapping": "prov_occurrence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Province of the incident",
        }
    )

    date_notified: str = field(
        metadata={
            "source": "Date_notified",
            "mapping": "date_notified",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Notification date",
        }
    )

    opening_date: str = field(
        metadata={
            "source": "Opening_date",
            "mapping": "opening_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date the claim was opened",
        }
    )

    last_review: str = field(
        metadata={
            "source": "Last_review",
            "mapping": "last_review",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of the last review",
        }
    )

    date_claims_paid: str = field(
        metadata={
            "source": "Date_Claims_Paid_(Final)",
            "mapping": "date_claims_paid",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Final date when claim was paid",
        }
    )

    date_of_first_request: str = field(
        metadata={
            "source": "Date_of_first_request",
            "mapping": "date_of_first_request",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when claim was first requested",
        }
    )

    insured: str = field(
        metadata={
            "source": "Insured",
            "mapping": "insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the insured Person/Company",
        }
    )

    insured_city: str = field(
        metadata={
            "source": "Insured_city",
            "mapping": "insured_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the insured",
        }
    )

    insured_province: str = field(
        metadata={
            "source": "Insured_Province",
            "mapping": "insured_Province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Province of the insured",
        }
    )

    class_of_business: str = field(
        metadata={
            "source": "Class_of_business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Class of Business of the claim cover",
        }
    )

    cover: str = field(
        metadata={
            "source": "coverage",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover description",
        }
    )

    policy_start_date: str = field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Start date of the policy",
        }
    )

    policy_end_date: str = field(
        metadata={
            "source": "Policy end date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "End date of the Policy",
        }
    )

    period_start_date: str = field(
        metadata={
            "source": "Period begin date",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Start date of the payment period",
        }
    )

    period_end_date: str = field(
        metadata={
            "source": "Period end date",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "End date of the payment period",
        }
    )

    underwriting_year: str = field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Year of the underwriting",
        }
    )

    date_complaint_received: str = field(
        metadata={
            "source": "Date_complaint_received",
            "mapping": "date_complaint_received",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date of the complaint received",
        }
    )

    closing_date: str = field(
        metadata={
            "source": "Closing_date",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date when claim was closed",
        }
    )

    opened_claim_type: str = field(
        metadata={
            "source": "Opened_claim_type",
            "mapping": "opened_claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim type of the opened claim",
        }
    )

    claim_type: str = field(
        metadata={
            "source": "claim_type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim type",
        }
    )

    claim_description: str = field(
        metadata={
            "source": "Description of the claim",
            "mapping": "claim_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of the claim",
        }
    )

    claim_status_start_of_month: str = field(
        metadata={
            "source": "Claim_status_at_start_of_the_month",
            "mapping": "claim_status_start_of_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Status of the claim start of the month",
        }
    )

    claim_status_end_of_month: str = field(
        metadata={
            "source": "Claim_status_at_end_of_the_month",
            "mapping": "claim_status_end_of_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status at the end of month",
        }
    )

    paid: str = field(
        metadata={
            "source": "Paid",
            "mapping": "paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid amount",
        }
    )

    claim_assessment_paid: str = field(
        metadata={
            "source": "Claim_assessment_paid",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid amount for claim assessment",
        }
    )

    recovered: str = field(
        metadata={
            "source": "Recovered",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovered amount",
        }
    )

    reserved: str = field(
        metadata={
            "source": "Reserved",
            "mapping": "reserved",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Reserved amount",
        }
    )

    claim_assessment_reserves: str = field(
        metadata={
            "source": "Claim_assessment_reserves",
            "mapping": "claim_assessment_reserves",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Reserved amount for claim assessment",
        }
    )

    recovery_reserve: str = field(
        metadata={
            "source": "Recovery_reserve",
            "mapping": "recovery_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount for reserve recovery",
        }
    )

    claim_assessment_paid_prev: str = field(
        metadata={
            "source": "Claim_assessment_paid_prev",
            "mapping": "claim_assessment_paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Previous paid claim assessment amount",
        }
    )

    recovered_prev: str = field(
        metadata={
            "source": "Recovered_prev",
            "mapping": "recovered_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Previous recovered amount",
        }
    )

    reserved_prev: str = field(
        metadata={
            "source": "Reserved_prev",
            "mapping": "reserved_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Previous reserved amount",
        }
    )

    claim_assessment_reserves_prev: str = field(
        metadata={
            "source": "Claim_assessment_reserves_prev",
            "mapping": "claim_assessment_reserves_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Previous reserve amount for claim assessment",
        }
    )

    recovery_reserve_prev: str = field(
        metadata={
            "source": "Recovery_reserve_prev",
            "mapping": "recovery_reserve_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Previous recovery reserve amount",
        }
    )

    total_incurred: str = field(
        metadata={
            "source": "Total_incurred",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total inccured amount",
        }
    )

    claims_referred: str = field(
        metadata={
            "source": "Claims_referred",
            "mapping": "claims_referred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is the claim referred yes/no",
        }
    )

    litigation: str = field(
        metadata={
            "source": "Litigation",
            "mapping": "litigation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is the claim under litigation",
        }
    )

    recovery_date: str = field(
        metadata={
            "source": "Date of recovery",
            "mapping": "recovery_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of recovery",
        }
    )

    claim_denied_reason: str = field(
        metadata={
            "source": "Reason_for_denial",
            "mapping": "claim_denied_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Reason for the claim to be denied",
        }
    )
