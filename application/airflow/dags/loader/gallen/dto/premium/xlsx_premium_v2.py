#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.gallen.dto.premium import GallenXLSXPremiumDTO


@dataclass(init=False, repr=False)
class GallenXLSXPremiumDTOV2(GallenXLSXPremiumDTO):

    ph_tax_id: str = dataclasses.field(
        metadata={
            "source": "Poliza.Cliente.Nif",
            "mapping": "ph_tax_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Tax id of the policy holder",
        }
    )

    ph_name: str = dataclasses.field(
        metadata={
            "source": "Poliza.Cliente.Nombre",
            "mapping": "ph_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy holder name",
        }
    )

    ph_last_name: str = dataclasses.field(
        metadata={
            "source": "Poliza.Cliente.Apellido1",
            "mapping": "ph_last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy holder last name",
        }
    )
