#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, String  # type: ignore

from loader.gallen.dto.sanctions import GallenXLSXSanctionDTOV3


@dataclass(init=False, repr=False)
class GallenXLSXSanctionDTOV4(GallenXLSXSanctionDTOV3):
    account_id: str = dataclasses.field(
        metadata={
            "source": "Account ID",
            "mapping": "account_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Account identifier",
        }
    )
