#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, String  # type: ignore

from loader.gallen.dto.sanctions import GallenXLSXSanctionDTOV2


@dataclass(init=False, repr=False)
class GallenXLSXSanctionDTOV3(GallenXLSXSanctionDTOV2):
    sanction_id: str = dataclasses.field(
        metadata={
            "source": "ID",
            "mapping": "sanction_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identifier for sanctions file",
        }
    )

    account_id: str = dataclasses.field(
        metadata={
            "source": "AccountID",
            "mapping": "account_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Account identifier",
        }
    )

    claim_id: str = dataclasses.field(
        metadata={
            "source": "Other Information2",
            "mapping": "claim_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Information on the claim id that the sanction record is linked",
        }
    )
