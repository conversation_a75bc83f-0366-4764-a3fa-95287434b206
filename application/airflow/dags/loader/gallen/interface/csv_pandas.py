from typing import Callable, Optional

from dateutil.parser import parse, parserinfo

from loader.core.csv_pandas import CSVPandasInterface


class GallenCSVPandasInterface(CSVPandasInterface):
    def __init__(self, dto, header: Optional[int] = 0):
        self._header = header
        self._encoding = "utf-8"
        self._separator = ";"
        self._decimal = ","
        self._thousands = None
        self._sheet_names = [0]
        self._dto = dto()

    @classmethod
    def date_parser(cls) -> Callable:
        day_first = parserinfo(dayfirst=True, yearfirst=False)

        skip_list = ["nan", "None"]

        return lambda x: parse(str(x), parserinfo=day_first) if str(x) not in skip_list else None

    def converters(self):
        return self._dto.converters() if hasattr(self._dto, "converters") else None
