from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.gallen.dto.policies.csv_policies import GallenCSVPoliciesDTO
from loader.gallen.interface import GallenCSVPandasInterface


class GallenPremiumCSVToDataFrameFactory(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = GallenCSVPandasInterface(dto=GallenCSVPoliciesDTO)
