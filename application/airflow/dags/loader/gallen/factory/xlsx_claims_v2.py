from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.gallen.dto.claims import GallenXLSXClaimsDTOV2
from loader.gallen.interface import GallenXLSXPandasInterface
from dateutil.parser import parse


class GallenClaimsXLSXV2ToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = GallenXLSXPandasInterface(dto=GallenXLSXClaimsDTOV2, header=0)

        # Dynamically override the date_parser for this factory instance
        self._interface.date_parser = self.day_first_date_parser

    def day_first_date_parser(self):
        skip_list = ["nan", "None"]

        return lambda x: parse(str(x), dayfirst=True) if str(x) not in skip_list else None
