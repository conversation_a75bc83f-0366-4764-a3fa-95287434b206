from loader.gallen.factory import *

file_types = [
    {
        "name": "gallen_xlsx_premiumV2",
        "factory": GallenPremiumXLSXToDataFrameFactoryV2,
        "regexp": r"^plizas_\d{4}\d{2}\d{2}_.*\.xlsx",
        "category": "premium",
    },
    {
        "name": "gallen_xlsx_premium",
        "factory": GallenPremiumXLSXToDataFrameFactory,
        # Only 2 files have use of this dto
        "regexp": r"^iptiq_(20240403||20240325)_.*\.xlsx",
        "category": "premium",
    },
    {
        "name": "gallen_xlsx_premium",
        "factory": GallenPremiumXLSXToDataFrameFactoryV2,
        "regexp": r"^iptiq_\d{4}\d{2}\d{2}_.*\.xlsx",
        "category": "premium",
    },
    {
        "name": "gallen_csv_policies",
        "factory": GallenPremiumCSVToDataFrameFactory,
        "regexp": r"^BORDERAUX_\d{8}.*\.csv$",
        "category": "policies",
    },
    {
        "name": "gallen_xlsx_sanctions",
        "factory": GallenSanctionXLSXToDataFrameFactory,
        "regexp": r"^(202408|202409)\d{2}_gallen_payment_screening\.xlsx",
        "category": "sanctions",
    },
    {
        "name": "gallen_xlsx_sanctions_v2",
        "factory": GallenSanctionXLSXToDataFrameFactoryV2,
        "regexp": r"^(202410)\d{2}_gallen_payment_screening\.xlsx",
        "category": "sanctions",
    },
    {
        "name": "gallen_xlsx_sanctions_v3",
        "factory": GallenSanctionXLSXToDataFrameFactoryV3,
        "regexp": r"^(202411)\d{2}_gallen_payment_screening\.xlsx",
        "category": "sanctions",
    },
    {
        "name": "gallen_xlsx_sanctions_v4",
        "factory": GallenSanctionXLSXToDataFrameFactoryV4,
        "regexp": r"^(202412\d{2}|20250[1-5]\d{2})_gallen_payment_screening\.xlsx",
        "category": "sanctions",
    },
    {
        "name": "gallen_xlsx_sanctions_v3",
        "factory": GallenSanctionXLSXToDataFrameFactoryV3,
        "regexp": r"^\d{4}\d{2}\d{2}_gallen_payment_screening\.xlsx",
        "category": "sanctions",
    },
    {
        "name": "gallen_xlsx_claims",
        "factory": GallenClaimsXLSXToDataFrameFactory,
        "regexp": r"^\d{4}\d{2}\d{2}_BDX_Iptiq.*_[JUNIO||JULIO]_\d{4}\.xlsx",
        "category": "claims",
    },
    {
        "name": "gallen_xlsx_claims_v2",
        "factory": GallenClaimsXLSXV2ToDataFrameFactory,
        "regexp": r"^\d{4}\d{2}\d{2}_BDX_Iptiq.*\d{4}\.xlsx",
        "category": "claims",
    },

]
