from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.gallen.dto.premium import GallenXLSXPremiumDTO, GallenXLSXPremiumDTOV2

attr_dict = generate_table_model_from_dtos(
    "gallen_premium", [GallenXLSXPremiumDTO, GallenXLSXPremiumDTOV2], schema=DEFAULT_DB_LANDING_SCHEMA
)
GallenPremium = type("GallenPremium", (Base,), attr_dict)
