from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.gallen.dto.policies import GallenCSVPoliciesDTO

attr_dict = generate_table_model_from_dtos(
    "gallen_policies", [GallenCSVPoliciesDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
GallenPolicies = type("GallenPolicies", (Base,), attr_dict)
