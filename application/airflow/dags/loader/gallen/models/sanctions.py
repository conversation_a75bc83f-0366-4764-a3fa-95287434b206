from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.gallen.dto.sanctions import GallenXLSXSanctionDTO, GallenXLSXSanctionDTOV2

attr_dict = generate_table_model_from_dtos(
    "gallen_sanctions", [GallenXLSXSanctionDTO, GallenXLSXSanctionDTOV2], schema=DEFAULT_DB_LANDING_SCHEMA
)
GallenSanctions = type("GallenSanctions", (Base,), attr_dict)
