import pandas as pd  # type: ignore

from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.prima.dto.complaints import PrimaXLSXComplaintsDTOV3
from loader.prima.interface import Prima<PERSON>LSXPandasInterface


class PrimaComplaintsXLSXV3ToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = PrimaXLSXPandasInterface(
            # The header spans the first two rows
            dto=PrimaXLSXComplaintsDTOV3,
            sheet_names=["Foglio1"],
            header=[0, 1],
        )
