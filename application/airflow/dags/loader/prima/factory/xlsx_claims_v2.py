import pandas as pd  # type: ignore

from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.prima.dto.claims import PrimaXLSXClaimsDTOV2
from loader.prima.interface import PrimaClaimsXLSXPandasInterface


class PrimaClaimsXLSXV2ToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = PrimaClaimsXLSXPandasInterface(dto=PrimaXLSXClaimsDTOV2, sheet_names=["Claims"], header=4)
