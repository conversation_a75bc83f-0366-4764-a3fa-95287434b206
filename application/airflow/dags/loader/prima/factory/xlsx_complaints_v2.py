import pandas as pd  # type: ignore

from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.prima.dto.complaints import PrimaXLSXComplaintsDTOV2
from loader.prima.interface import Prima<PERSON>LSXPandasInterface


class PrimaComplaintsXLSXV2ToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = PrimaXLSXPandasInterface(
            # The header spans the first two rows
            dto=PrimaXLSXComplaintsDTOV2,
            sheet_names=["Foglio1"],
            header=[0, 1],
        )
