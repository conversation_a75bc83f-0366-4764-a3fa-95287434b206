import pandas as pd  # type: ignore

from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.prima.dto.claims import PrimaXLSXClaimsDTOHousehold
from loader.prima.interface import PrimaClaimsXLSXPandasInterface


class PrimaClaimsXLSXHouseholdToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = PrimaClaimsXLSXPandasInterface(
            dto=PrimaXLSXClaimsDTOHousehold, sheet_names=["Claims"], header=4
        )
