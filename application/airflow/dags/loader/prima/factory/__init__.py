from loader.prima.factory.csv import PrimaCSVToDataFrameFactory
from loader.prima.factory.xlsx_claims import PrimaClaimsXLSXToDataFrameFactory
from loader.prima.factory.xlsx_claims_v2 import PrimaClaimsXLSXV2ToDataFrameFactory
from loader.prima.factory.xlsx_claims_v3 import PrimaClaimsXLSXV3ToDataFrameFactory
from loader.prima.factory.xlsx_claims_household import PrimaClaimsXLSXHouseholdToDataFrameFactory
from loader.prima.factory.xlsx_claims_household_v2 import PrimaClaimsXLSXHouseholdV2ToDataFrameFactory
from loader.prima.factory.xlsx_claims_spain import PrimaClaimsSpainXLSXToDataFrameFactory
from loader.prima.factory.xlsx_complaints import PrimaComplaintsXLSXToDataFrameFactory
from loader.prima.factory.xlsx_complaints_v2 import PrimaComplaintsXLSXV2ToDataFrameFactory
from loader.prima.factory.xlsx_complaints_v3 import PrimaComplaintsXLSXV3ToDataFrameFactory
from loader.prima.factory.xlsx_complaints_spain import PrimaComplaintsSpainXLSXToDataFrameFactory
from loader.prima.factory.xlsx_complaints_spain_v2 import PrimaComplaintsSpainXLSXV2ToDataFrameFactory
from loader.prima.factory.xlsx_policy import PrimaPolicyXLSXToDataFrameFactory
from loader.prima.factory.xlsx_policy_household import PrimaPolicyHouseholdXLSXToDataFrameFactory
from loader.prima.factory.xlsx_policy_household_v2 import PrimaPolicyHouseholdXLSXV2ToDataFrameFactory
from loader.prima.factory.xlsx_policy_household_v3 import PrimaPolicyHouseholdXLSXV3ToDataFrameFactory
from loader.prima.factory.xlsx_policy_spain import PrimaPolicySpainXLSXToDataFrameFactory
from loader.prima.factory.xlsx_policy_spain_v2 import PrimaPolicySpainXLSXV2ToDataFrameFactory
from loader.prima.factory.xlsx_policy_v2 import PrimaPolicyXLSXV2ToDataFrameFactory
from loader.prima.factory.xlsx_policy_v3 import PrimaPolicyXLSXV3ToDataFrameFactory
from loader.prima.factory.xlsx_policy_v4 import PrimaPolicyXLSXV4ToDataFrameFactory
from loader.prima.factory.xlsx_policy_v5 import PrimaPolicyXLSXV5ToDataFrameFactory
from loader.prima.factory.xlsx_policy_v6 import PrimaPolicyXLSXV6ToDataFrameFactory
from loader.prima.factory.xlsx_policy_v7 import PrimaPolicyXLSXV7ToDataFrameFactory
from loader.prima.factory.xlsx_suspensions_reactivations import PrimaSuspensionsReactivationsXLSXToDataFrameFactory
from loader.prima.factory.csv_complaints_jira import PrimaCSVToDataFrameComplaintsJiraFactory

__all__ = [
    "PrimaCSVToDataFrameFactory",
    "PrimaClaimsXLSXToDataFrameFactory",
    "PrimaClaimsXLSXV2ToDataFrameFactory",
    "PrimaClaimsXLSXV3ToDataFrameFactory",
    "PrimaClaimsXLSXHouseholdToDataFrameFactory",
    "PrimaClaimsXLSXHouseholdV2ToDataFrameFactory",
    "PrimaClaimsSpainXLSXToDataFrameFactory",
    "PrimaComplaintsXLSXToDataFrameFactory",
    "PrimaComplaintsXLSXV2ToDataFrameFactory",
    "PrimaComplaintsXLSXV3ToDataFrameFactory",
    "PrimaComplaintsSpainXLSXToDataFrameFactory",
    "PrimaComplaintsSpainXLSXV2ToDataFrameFactory",
    "PrimaPolicySpainXLSXToDataFrameFactory",
    "PrimaPolicyXLSXToDataFrameFactory",
    "PrimaPolicyXLSXV2ToDataFrameFactory",
    "PrimaPolicyXLSXV3ToDataFrameFactory",
    "PrimaPolicyXLSXV4ToDataFrameFactory",
    "PrimaPolicyXLSXV4ToDataFrameFactory",
    "PrimaPolicyXLSXV5ToDataFrameFactory",
    "PrimaPolicyXLSXV6ToDataFrameFactory",
    "PrimaPolicyXLSXV7ToDataFrameFactory",
    "PrimaPolicyHouseholdXLSXToDataFrameFactory",
    "PrimaPolicyHouseholdXLSXV2ToDataFrameFactory",
    "PrimaPolicyHouseholdXLSXV3ToDataFrameFactory",
    "PrimaSuspensionsReactivationsXLSXToDataFrameFactory",
    "PrimaPolicySpainXLSXV2ToDataFrameFactory",
    "PrimaCSVToDataFrameComplaintsJiraFactory"
]
