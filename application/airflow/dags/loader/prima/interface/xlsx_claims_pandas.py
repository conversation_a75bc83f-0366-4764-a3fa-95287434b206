from contextlib import suppress
from datetime import datetime
from typing import Callable

from dateutil.parser import parse

from loader.prima.interface import PrimaXLSXPandasInterface


class PrimaClaimsXLSXPandasInterface(PrimaXLSXPandasInterface):
    def __init__(self, dto, sheet_names, header):
        super().__init__(dto, sheet_names, header)

    @staticmethod
    def parser_multiple_formats_in_the_same_column(date_string):
        format_list = [
            "%d-%m-%Y %H:%M:%S",
            "%d-%m-%Y",
            "%Y-%m-%d",
            "%Y-%m-%d %H:%M:%S",
            "%d/%m/%Y %H:%M:%S",
            "%d/%m/%Y",
            "%Y/%m/%d",
            "%Y/%m/%d %H:%M:%S",
            "%d.%m.%Y %H:%M:%S",
            "%d.%m.%Y",
            "%Y.%m.%d",
            "%Y.%m.%d %H:%M:%S",
        ]
        for date_format in format_list:
            with suppress(ValueError):
                result = datetime.strptime(date_string, date_format)
                return result
        return parse(date_string)

    @classmethod
    def date_parser(cls) -> Callable:
        skip_list = ["nan", "None"]

        return lambda x: cls.parser_multiple_formats_in_the_same_column(str(x)) if str(x) not in skip_list else None
