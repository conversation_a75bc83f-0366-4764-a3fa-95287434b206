from typing import Callable

from dateutil.parser import parse

from loader.core.xlsx_pandas import XLSXPandasInterface
from loader.prima.dto.policy import PrimaCSVPolicyDTO  # currently same DTO format ->REUSING


class PrimaXLSXPandasInterfaceV2(XLSXPandasInterface):
    def __init__(
        self,
        dto=PrimaCSVPolicyDTO,
        sheet_names=["Premium", "Premium_1", "Premium_2", "Premium_pt1", "Premium_pt2"],
        header=0,
    ):
        self._header = header
        self._decimal = "."
        self._thousands = ","
        self._skipfooterint = 0
        self._skiprows = 0
        self._sheet_names = sheet_names
        self._dto = dto()

    def date_parser(self) -> Callable:

        return lambda x: parse(str(x)) if len(str(x)) >= 8 else None
