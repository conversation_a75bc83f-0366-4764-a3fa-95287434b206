from typing import Callable

from loader.core.csv_pandas import CSVPandasInterface
from loader.prima.dto.policy import PrimaCSVPolicyDTO


class PrimaCSVPandasInterface(CSVPandasInterface):
    # some edit need to be change for when leveraging other DTOs / filetype
    def __init__(self):
        self._separator = ";"
        self._encoding = "cp1252"
        self._header = 0
        self._decimal = "."
        self._thousands = ","
        self._dto = PrimaCSVPolicyDTO()

    @classmethod
    def date_parser(cls) -> Callable:
        from dateutil.parser import parse

        return lambda x: parse(str(x)) if len(str(x)) >= 10 else None
