from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.claims import PrimaXLSXClaimsSpainDTO

attr_dict = generate_table_model_from_dtos(
    "prima_claims_spain",
    [
        PrimaXLSXClaimsSpainDTO,
    ],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
PrimaClaimsSpain = type("PrimaClaimsSpain", (Base,), attr_dict)
