from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.suspensions_reactivations import PrimaXLSXSuspensionsReactivationsDTO

attr_dict = generate_table_model_from_dtos(
    "prima_suspensions_reactivations", [PrimaXLSXSuspensionsReactivationsDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
PrimaSuspensionsReactivations = type("PrimaSuspensionsReactivations", (Base,), attr_dict)
