from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.complaints import PrimaComplaintsJiraDTO

attr_dict = generate_table_model_from_dtos(
    "prima_complaints_jira", [PrimaComplaintsJiraDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
PrimaComplaintsJira = type("PrimaComplaintsJira", (Base,), attr_dict)
