from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.complaints import PrimaXLSXComplaintsDTOV3

attr_dict = generate_table_model_from_dtos(
    "prima_complaints", [PrimaXLSXComplaintsDTOV3], schema=DEFAULT_DB_LANDING_SCHEMA
)
PrimaComplaints = type("PrimaComplaints", (Base,), attr_dict)
