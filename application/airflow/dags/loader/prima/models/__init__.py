from loader.prima.models.claims import PrimaClaims
from loader.prima.models.claims_spain import PrimaClaimsSpain
from loader.prima.models.complaints import PrimaComplaints
from loader.prima.models.complaints_spain import PrimaComplaintsSpain
from loader.prima.models.premium import PrimaPremium
from loader.prima.models.premium_spain import PrimaPremiumSpain
from loader.prima.models.suspensions_reactivations import PrimaSuspensionsReactivations
from loader.prima.models.complaints_jira import PrimaComplaintsJira

__all__ = [
    "PrimaPremium",
    "PrimaClaims",
    "PrimaSuspensionsReactivations",
    "PrimaComplaints",
    "PrimaPremiumSpain",
    "PrimaClaimsSpain",
    "PrimaComplaintsSpain",
    "PrimaComplaintsJira"
]
