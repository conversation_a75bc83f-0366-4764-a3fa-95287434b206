from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.claims import PrimaXLSXClaimsDTO, PrimaXLSXClaimsDTOHousehold, PrimaXLSXClaimsDTOV3

attr_dict = generate_table_model_from_dtos(
    "prima_claims",
    [PrimaXLSXClaimsDTO, PrimaXLSXClaimsDTOV3, PrimaXLSXClaimsDTOHousehold],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
PrimaClaims = type("PrimaClaims", (Base,), attr_dict)
