from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.policy import (
    PrimaCSVPolicyDTO,
    PrimaCSVPolicyDTOV2,
    PrimaCSVPolicyDTOV3,
    PrimaCSVPolicyDTOV4,
    PrimaCSVPolicyDTOV5,
    PrimaCSVPolicyDTOV7,
    PrimaCSVPolicyHouseholdDTO,
    PrimaCSVPolicyHouseholdDTOV2,
    PrimaCSVPolicyHouseholdDTOV3,
)

attr_dict = generate_table_model_from_dtos(
    "prima_premium",
    [
        PrimaCSVPolicyDTO,
        PrimaCSVPolicyHouseholdDTO,
        <PERSON>rimaCSVPolicyHouseholdDTOV2,
        PrimaCSVPolicyHouseholdDTOV3,
        PrimaCSVPolicyDTOV2,
        PrimaCSVPolicyDTOV3,
        PrimaCSVPolicyDTOV4,
        PrimaCSVPolicyDTOV5,
        PrimaCSVPolicyDTOV7,
    ],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
PrimaPremium = type("PrimaPremium", (Base,), attr_dict)
