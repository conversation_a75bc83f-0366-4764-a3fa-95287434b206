from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.policy import PrimaXLSXPolicySpainDTO

attr_dict = generate_table_model_from_dtos(
    "prima_premium_spain",
    [
        PrimaXLSXPolicySpainDTO,
    ],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
PrimaPremiumSpain = type("PrimaPremiumSpain", (Base,), attr_dict)
