from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.prima.dto.complaints import PrimaXLSXComplaintsSpainDTO

attr_dict = generate_table_model_from_dtos(
    "prima_complaints_spain",
    [
        PrimaXLSXComplaintsSpainDTO,
    ],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
PrimaComplaintsSpain = type("PrimaComplaintsSpain", (Base,), attr_dict)
