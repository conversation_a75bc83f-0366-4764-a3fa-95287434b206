import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class PrimaXLSXComplaintsSpainDTO:
    complaint_reference: str = dataclasses.field(
        metadata={
            "source": "Complaint Reference",
            "mapping": "complaint_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint reference",
        }
    )

    complaint_type: str = dataclasses.field(
        metadata={
            "source": "Type of Complaint",
            "mapping": "complaint_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of complaint",
        }
    )

    n_complaint_references: str = dataclasses.field(
        metadata={
            "source": "Nº Ref. Repetición",
            "mapping": "n_complaint_references",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Number of complaint references",
        }
    )

    creation_year: str = dataclasses.field(
        metadata={
            "source": "Year",
            "mapping": "creation_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Year complaint was created",
        }
    )

    complaint_code: str = dataclasses.field(
        metadata={
            "source": "Complaint Code",
            "mapping": "complaint_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint code",
        }
    )

    complaint_handling_code: str = dataclasses.field(
        metadata={
            "source": "Complaint handling code (# Ticket)",
            "mapping": "complaint_handling_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint handling code",
        }
    )

    reception_date: str = dataclasses.field(
        metadata={
            "source": "Date of Reception of the complaint",
            "mapping": "reception_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of reception of the complaint",
        }
    )

    product_type: str = dataclasses.field(
        metadata={
            "source": "Type of product",
            "mapping": "product_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of product",
        }
    )

    sector: str = dataclasses.field(
        metadata={
            "source": "Department/area of the company who received the complaint",
            "mapping": "sector",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Department/area of the company who received the complaint",
        }
    )

    name_policyholder: str = dataclasses.field(
        metadata={
            "source": "Name and First name of the policy holder",
            "mapping": "name_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name and First name of the policy holder",
        }
    )

    address: str = dataclasses.field(
        metadata={
            "source": "Addres",
            "mapping": "address",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address",
        }
    )

    channel: str = dataclasses.field(
        metadata={
            "source": "Channel used",
            "mapping": "channel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Channel used for complaint",
        }
    )

    geographic_area_policyholder: str = dataclasses.field(
        metadata={
            "source": "Geographical area",
            "mapping": "geographic_area_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Geographical area of the policyholder",
        }
    )

    name_complainer: str = dataclasses.field(
        metadata={
            "source": "Name and First name of the complainer",
            "mapping": "name_complainer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name and First name of the complainer",
        }
    )

    address_complainer: str = dataclasses.field(
        metadata={
            "source": "Address of the complainer",
            "mapping": "address_complainer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address of the complainer",
        }
    )

    type_complainer: str = dataclasses.field(
        metadata={
            "source": "Type of complainer",
            "mapping": "type_complainer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of complainer",
        }
    )

    closure_date: str = dataclasses.field(
        metadata={
            "source": "Closure date",
            "mapping": "closure_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closure date",
        }
    )

    status: str = dataclasses.field(
        metadata={
            "source": "Status",
            "mapping": "status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Status",
        }
    )

    judicial_authority_intervention: str = dataclasses.field(
        metadata={
            "source": "Possible intervention of the judicial authorities",
            "mapping": "period_start_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Possible intervention of the judicial authorities",
        }
    )

    monetary_value: str = dataclasses.field(
        metadata={
            "source": "Economic Value",
            "mapping": "monetary_value",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Monetary value",
        }
    )

    duration_till_closure: str = dataclasses.field(
        metadata={
            "source": "Duration to close the complaint",
            "mapping": "duration_till_closure",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Duration to close the complaint",
        }
    )

    complaint_subject: str = dataclasses.field(
        metadata={
            "source": "Subject of the complaint",
            "mapping": "complaint_subject",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subject of the complaint",
        }
    )

    ref_policy: str = dataclasses.field(
        metadata={
            "source": "Policy Reference",
            "mapping": "ref_policy",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy Reference",
        }
    )

    ref_claim: str = dataclasses.field(
        metadata={
            "source": "Claim Reference",
            "mapping": "ref_claim",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim Reference",
        }
    )

    response: str = dataclasses.field(
        metadata={
            "source": "Response",
            "mapping": "response",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Response",
        }
    )

    transferred_judicial_authority: str = dataclasses.field(
        metadata={
            "source": "Judicial Authorities",
            "mapping": "transferred_judicial_authority",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Judicial Authorities",
        }
    )

    reopened: str = dataclasses.field(
        metadata={
            "source": "Reopening",
            "mapping": "reopened",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint reopened",
        }
    )

    complaint_cause: str = dataclasses.field(
        metadata={
            "source": "Causes of complaints",
            "mapping": "complaint_cause",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cause of complaint",
        }
    )

    elapsed_days: str = dataclasses.field(
        metadata={
            "source": "Elapsed days",
            "mapping": "elapsed_days",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Elapsed days",
        }
    )
