from loader.prima.dto.complaints.xlsx_complaints import PrimaXLSXComplaintsDTO
from loader.prima.dto.complaints.xlsx_complaints_v2 import PrimaXLSXComplaintsDTOV2
from loader.prima.dto.complaints.xlsx_complaints_v3 import PrimaXLSXComplaintsDTOV3
from loader.prima.dto.complaints.xlsx_complaints_spain import PrimaXLSXComplaintsSpainDTO
from loader.prima.dto.complaints.xlsx_complaints_spain_v2 import PrimaXLSXComplaintsSpainDTOV2
from loader.prima.dto.complaints.csv_complaints_jira import PrimaComplaintsJiraDTO

__all_ = [
    "PrimaXLSXComplaintsDTO",
    "PrimaXLSXComplaintsDTOV2",
    "PrimaXLSXComplaintsDTOV3",
    "PrimaXLSXComplaintsSpainDTO",
    "PrimaXLSXComplaintsSpainDTOV2",
    "PrimaComplaintsJiraDTO"
]
