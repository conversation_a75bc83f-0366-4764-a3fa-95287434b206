#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore
from loader.prima.dto.complaints import PrimaXLSXComplaintsSpainDTO


@dataclass(init=False, repr=False)
class PrimaXLSXComplaintsSpainDTOV2(PrimaXLSXComplaintsSpainDTO):
    address: str = dataclasses.field(
        metadata={
            "source": "Address",
            "mapping": "address",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address",
        }
    )
