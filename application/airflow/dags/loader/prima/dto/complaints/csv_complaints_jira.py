
from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class PrimaComplaintsJiraDTO:

    jira_ticket: str = field(
        metadata={
            "source": "jira ticket",
            "mapping": "jira_ticket",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Jira ticket",
        }
    )

    field_name: str = field(
        metadata={
            "source": "fieldname",
            "mapping": "field_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of Jira field",
        }
    )

    field_value: str = field(
        metadata={
            "source": "fieldvalue",
            "mapping": "field_value",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Value of Jira field",
        }
    )

    subject: str = field(
        metadata={
            "source": "subject",
            "mapping": "subject",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subject of Jira ticket",
        }
    )

    updated_at: str = field(
        metadata={
            "source": "updated at",
            "mapping": "updated_at",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Time of update",
        }
    )

    created_at: str = field(
        metadata={
            "source": "created at",
            "mapping": "created_at",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Time of update",
        }
    )

    author: str = field(
        metadata={
            "source": "author",
            "mapping": "author",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Author of Jira ticket",
        }
    )
