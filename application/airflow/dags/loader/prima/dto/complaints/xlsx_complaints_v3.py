#!/usr/bin/python
# -*- coding: latin-1 -*-

from dataclasses import dataclass, field

import numpy as np  # type: ignore
from sqlalchemy.types import String  # type: ignore

from loader.prima.dto.complaints import PrimaXLSXComplaintsDTOV2


@dataclass(init=False, repr=False)
class PrimaXLSXComplaintsDTOV3(PrimaXLSXComplaintsDTOV2):
    product: str = field(
        metadata={
            "source": "esito reclamo motor/no motor",
            "mapping": "product",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Whether this is a motor policy or not (Italy only)",
        }
    )
