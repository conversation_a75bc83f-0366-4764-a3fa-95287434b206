from loader.prima.dto.claims.xlsx_claims import PrimaXLSXClaimsDTO
from loader.prima.dto.claims.xlsx_claims_v2 import PrimaXLSXClaimsDTOV2
from loader.prima.dto.claims.xlsx_claims_v3 import PrimaXLSXClaimsDTOV3
from loader.prima.dto.claims.xlsx_claims_household import PrimaXLSXClaimsDTOHousehold
from loader.prima.dto.claims.xlsx_claims_household_v2 import PrimaXLSXClaimsDTOHouseholdV2
from loader.prima.dto.claims.xlsx_claims_spain import PrimaXLSXClaimsSpainDTO

__all__ = [
    "PrimaXLSXClaimsDTO",
    "PrimaXLSXClaimsDTOV2",
    "PrimaXLSXClaimsDTOV3",
    "PrimaXLSXClaimsDTOHousehold",
    "PrimaXLSXClaimsDTOHouseholdV2",
    "PrimaXLSXClaimsSpainDTO",
]
