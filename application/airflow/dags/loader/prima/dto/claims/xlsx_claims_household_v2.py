#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.prima.dto.claims import PrimaXLSXClaimsDTOHousehold


@dataclass(init=False, repr=False)
class PrimaXLSXClaimsDTOHouseholdV2(PrimaXLSXClaimsDTOHousehold):
    Date_notified_to_Prima: str = dataclasses.field(
        metadata={
            "source": "Date_notified_to_Prima",  # Different source (space vs. underscore compared to normal bordereau)
            "mapping": "Date_notified_to_Prima",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date_notified_to_Prima",
        }
    )
