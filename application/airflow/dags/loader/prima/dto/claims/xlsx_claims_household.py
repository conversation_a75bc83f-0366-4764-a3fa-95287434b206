#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class PrimaXLSXClaimsDTOHousehold:
    Claim_id: str = dataclasses.field(
        metadata={
            "source": "ID_claim",
            "mapping": "claim_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim Policy Reference",
        }
    )

    Occurrence_date: str = dataclasses.field(
        metadata={
            "source": "Occurrence_date",
            "mapping": "event_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim Event date - When the event = cause took place",
        }
    )

    Prov_occurrence: str = dataclasses.field(
        metadata={
            "source": "Prov_occurrence",
            "mapping": "event_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - Province in Italy in the format of 2 letters",
        }
    )

    Opening_date: str = dataclasses.field(
        metadata={
            "source": "Opening_date",
            "mapping": "opening_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim opening date",
        }
    )

    Last_review_date: str = dataclasses.field(
        metadata={
            "source": "Last_review",
            "mapping": "last_review_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim last review date",
        }
    )

    Policy_id: str = dataclasses.field(
        metadata={
            "source": "Policy_number",
            "mapping": "policy_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance Policy Reference",
        }
    )

    Class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class_of_business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II Class of business, listed as a text field e.g. Motor Third Party Liability",
        }
    )

    Policy_code: str = dataclasses.field(
        metadata={
            "source": "Policy_code",
            "mapping": "policy_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "PoThis is a field for GL.",
        }
    )

    Period_start_date: str = dataclasses.field(
        metadata={
            "source": "Period_start_date",
            "mapping": "period_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy begin date",
        }
    )

    Period_end_date: str = dataclasses.field(
        metadata={
            "source": "Period_end_date",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Policy end date",
        }
    )

    Underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Underwriting_year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting_year",
        }
    )

    Claim_status_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_start_of_the_month",
            "mapping": "claim_status_at_start_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "O for Open / C for Closed whether closed or denied claims",
        }
    )

    Closing_date: str = dataclasses.field(
        metadata={
            "source": "Closing_date",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing_date",
        }
    )

    Opened_claim_type: str = dataclasses.field(
        metadata={
            "source": "Opened_claim_type",
            "mapping": "opened_claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": """
                Description of the peril/non-peril in iptiQ’s terminology, cover in INSIS’ terminology;
                1 = infortuni conducente = accident
                3 = cristalli / furto incendio = glass breakage / theft and fire
                10 = rca (responsabilita civile automobile) / bonus protetto = motor liability / NCB no reduction
                17 = tutela legale = legal expenses
                18 = assistenza stradale = road side assistance""",
        }
    )

    Presence_BI: str = dataclasses.field(
        metadata={
            "source": "Presence_BI",
            "mapping": "presence_bi",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Bodily Injury",
        }
    )

    Presence_PD: str = dataclasses.field(
        metadata={
            "source": "Presence_PD",
            "mapping": "presence_pd",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Personal Damage (question)",
        }
    )

    Claim_status_at_end_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_end_of_the_month",
            "mapping": "claim_status_at_end_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "O for Open / C for Closed whether closed or denied claims",
        }
    )

    Reserved_PD_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Reserved_PD_at_start_of_the_month",
            "mapping": "reserved_pd_at_start_of_the_month",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to Personal damages at the start of the month",
        }
    )

    Reserved_BI_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Reserved_BI_at_start_of_the_month",
            "mapping": "reserved_bi_at_start_of_the_month",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to Body injury at the start of the month ",
        }
    )

    Paid_PD: str = dataclasses.field(
        metadata={
            "source": "Paid_PD",
            "mapping": "paid_pd",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount related to Personal damages at the start of the month",
        }
    )

    Paid_BI: str = dataclasses.field(
        metadata={
            "source": "Paid_BI",
            "mapping": "paid_bi",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount related to Body injury at the start of the month",
        }
    )

    Reserved_PD: str = dataclasses.field(
        metadata={
            "source": "Reserved_PD",
            "mapping": "reserved_pd",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to Personal damages during the month",
        }
    )

    Reserved_BI: str = dataclasses.field(
        metadata={
            "source": "Reserved_BI",
            "mapping": "reserved_bi",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to Body injury during the month",
        }
    )

    Recovered: str = dataclasses.field(
        metadata={
            "source": "Recovered",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the amount in case of recovery received from Stanza di Compensazione",
        }
    )

    Recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Recovery_reserve",
            "mapping": "recovery_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount in case a recovery opportunity is identified",
        }
    )

    Claim_assessment_reserves: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_reserves",
            "mapping": "claim_assessment_reserves",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount for claim assessment costs",
        }
    )

    Claim_assessment_paid: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for claim assessment costs",
        }
    )

    LAE_reserves: str = dataclasses.field(
        metadata={
            "source": "LAE_reserves",
            "mapping": "lae_reserves",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount for Loss Adjustment Expenses (LAE)",
        }
    )

    LAE_paid: str = dataclasses.field(
        metadata={
            "source": "LAE_paid",
            "mapping": "lae_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for Loss Adjustment Expenses (LAE)",
        }
    )

    Date_notified_to_Prima: str = dataclasses.field(
        metadata={
            "source": "Date notified to Prima",  # Different source (space vs. underscore compared to normal bordereau)
            "mapping": "Date_notified_to_Prima",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date_notified_to_Prima",
        }
    )

    Date_of_first_request: str = dataclasses.field(
        metadata={
            "source": "Date_of_first_request",
            "mapping": "Date_of_first_request",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date_of_first_request",
        }
    )

    Claims_referred: str = dataclasses.field(
        metadata={
            "source": "Claims_referred?",
            "mapping": "Claims_referred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claims_referred",
        }
    )

    Claimant: str = dataclasses.field(
        metadata={
            "source": "Claimant",
            "mapping": "Claimant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant",
        }
    )

    Insured: str = dataclasses.field(
        metadata={
            "source": "Insured",
            "mapping": "Insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured",
        }
    )

    Date_Claims_Paid_Final: str = dataclasses.field(
        metadata={
            "source": "Date_Claims_Paid_(Final)",
            "mapping": "Date_Claims_Paid_Final",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date_Claims_Paid_(Final)",
        }
    )

    Date_complaint_received: str = dataclasses.field(
        metadata={
            "source": "Date_complaint_received",
            "mapping": "Date_complaint_received",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date_complaint_received",
        }
    )

    Next_hearing: str = dataclasses.field(
        metadata={
            "source": "Next_hearing",
            "mapping": "Next_hearing",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Next_hearing",
        }
    )

    Claim_assessment_paid_prev: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid_prev",
            "mapping": "Claim_assessment_paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Claim_assessment_paid_prev",
        }
    )

    Paid_BI_prev: str = dataclasses.field(
        metadata={
            "source": "Paid_BI_prev",
            "mapping": "Paid_BI_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid_BI_prev",
        }
    )

    Litigation: str = dataclasses.field(
        metadata={
            "source": "Litigation",
            "mapping": "Litigation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Litigation",
        }
    )

    Recovered_prev: str = dataclasses.field(
        metadata={
            "source": "Recovered_prev",
            "mapping": "Recovered_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovered_prev",
        }
    )

    Insured_Province: str = dataclasses.field(
        metadata={
            "source": "Insured_Province",
            "mapping": "Insured_Province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured_Province",
        }
    )

    Date_Claim_Amount_Agreed: str = dataclasses.field(
        metadata={
            "source": "Date_Claim_Amount_Agreed",
            "mapping": "Date_Claim_Amount_Agreed",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date_Claim_Amount_Agreed",
        }
    )

    Paid_PD_prev: str = dataclasses.field(
        metadata={
            "source": "Paid_PD_prev",
            "mapping": "Paid_PD_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid_PD_prev ",
        }
    )

    Total_incurred: str = dataclasses.field(
        metadata={
            "source": "Total_incurred",
            "mapping": "Total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total_incurred",
        }
    )

    # household specific
    Postal_code_loss: str = dataclasses.field(
        metadata={
            "source": "Postal_code_loss",
            "mapping": "postal_code_loss",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "postal_code_loss",
        }
    )

    postal_code_Insured: str = dataclasses.field(
        metadata={
            "source": "Postal_code_Insured",
            "mapping": "postal_code_insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "postal_code_Insured",
        }
    )

    Claims_reopening_date: str = dataclasses.field(
        metadata={
            "source": "Claims_reopening_date",
            "mapping": "claims_reopening_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claims_reopening_date",
        }
    )

    Date_claim_denied: str = dataclasses.field(
        metadata={
            "source": "Date_claim_denied",
            "mapping": "date_claim_denied",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "date_claim_denied",
        }
    )

    Reason_for_denial: str = dataclasses.field(
        metadata={
            "source": "Reason_for_denial",
            "mapping": "reason_for_denial",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "reason_for_denial",
        }
    )

    Loss_description: str = dataclasses.field(
        metadata={
            "source": "Loss_description",
            "mapping": "loss_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "loss_description",
        }
    )

    Deductible_amount: str = dataclasses.field(
        metadata={
            "source": " Deductible_amount",
            "mapping": "deductible_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "deductible_amount",
        }
    )

    Coverage_limit: str = dataclasses.field(
        metadata={
            "source": " Coverage_limit",
            "mapping": "coverage_limit",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "coverage_limit",
        }
    )
