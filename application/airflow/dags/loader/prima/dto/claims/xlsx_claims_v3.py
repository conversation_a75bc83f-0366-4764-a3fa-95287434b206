#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore
from loader.prima.dto.claims import PrimaXLSXClaimsDTOV2


@dataclass(init=False, repr=False)
class PrimaXLSXClaimsDTOV3(PrimaXLSXClaimsDTOV2):
    Vehicle_type: str = dataclasses.field(
        metadata={
            "source": "Vehicle_type",
            "mapping": "vehicle_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of vehicle (e.g. car, van, motorbike, etc.)",
        }
    )
