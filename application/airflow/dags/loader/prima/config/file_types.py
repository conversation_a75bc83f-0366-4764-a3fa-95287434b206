from loader.prima.factory import *

legacy_loads = ["prima_complaints"]

file_types = [
    {
        "name": "prima_xlsx_policy_spain_v2",
        "factory": PrimaPolicySpainXLSXV2ToDataFrameFactory,
        "regexp": r"spain/\d{4}_\d{2}_\w+_premia_es_vshared_.*\.xlsx",
        "category": "premium_spain",
    },
    {
        "name": "prima_xlsx_policy_spain",
        "factory": PrimaPolicySpainXLSXToDataFrameFactory,
        "regexp": r"spain/\d{4}/\d{2}\. \w+ Premia ES.*.xlsx",
        "category": "premium_spain",
    },
    {
        "name": "prima_xlsx_claims_spain",
        "factory": PrimaClaimsSpainXLSXToDataFrameFactory,
        "regexp": r"spain/\d{4}_\d{2}_\w+_claims_es_bdx_report_vshared_\d{8}.*\.xlsx",
        "category": "claims_spain",
    },
    {
        "name": "prima_xlsx_complaints_spain",
        "factory": PrimaComplaintsSpainXLSXV2ToDataFrameFactory,
        "regexp": r"spain/\d{4}_\d{2}_\w+_qyr_es_vshared_\d{8}.*\.xlsx",
        "category": "complaints_spain",
    },
    {
        "name": "prima_xlsx_complaints_spain",
        "factory": PrimaComplaintsSpainXLSXToDataFrameFactory,
        "regexp": r"spain/\d{4}/\d{2}\. \w+ Complaints ES.*.xlsx",
        "category": "complaints_spain",
    },
    {
        "name": "prima_xlsx_policy_household",
        "factory": PrimaPolicyHouseholdXLSXToDataFrameFactory,
        "regexp": r"2021/0[1-3].*Premia [A-Z]{2} household.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_household_v2",
        "factory": PrimaPolicyHouseholdXLSXV2ToDataFrameFactory,
        "regexp": r"202[123]/[0-9]{2}.*Premia [A-Z]{2} household.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_household_v3",
        "factory": PrimaPolicyHouseholdXLSXV3ToDataFrameFactory,
        "regexp": r".*Premia [A-Z]{2} household.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy",
        "factory": PrimaPolicyXLSXToDataFrameFactory,
        "regexp": r"07.*Premia .{2}.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy",
        "factory": PrimaPolicyXLSXV2ToDataFrameFactory,
        "regexp": r"2021/[0-6]{2}.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v3",
        "factory": PrimaPolicyXLSXV3ToDataFrameFactory,
        "regexp": r"2021/[0-9]{2}.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v4",
        "factory": PrimaPolicyXLSXV4ToDataFrameFactory,
        "regexp": r"2022/01.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v5",
        "factory": PrimaPolicyXLSXV5ToDataFrameFactory,
        "regexp": r"2022/[0-9]{2}.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v5",
        "factory": PrimaPolicyXLSXV5ToDataFrameFactory,
        "regexp": r"2023/0[1-4].*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v6",
        "factory": PrimaPolicyXLSXV6ToDataFrameFactory,
        "regexp": r"2023/[0-9]{2}.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v6",
        "factory": PrimaPolicyXLSXV6ToDataFrameFactory,
        "regexp": r"2024/0[1-3].*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy_v7",
        "factory": PrimaPolicyXLSXV7ToDataFrameFactory,
        "regexp": r"(?!.*preview).*202[4-9].*\/[0-9]{2}.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_policy",
        "factory": PrimaPolicyXLSXV2ToDataFrameFactory,
        "regexp": r"(?!.*preview)[0-9]{2}.*Premia.+.xlsx",
        "category": "premium",
    },
    {
        "name": "prima_xlsx_claims_household",
        "factory": PrimaClaimsXLSXHouseholdToDataFrameFactory,
        "regexp": r"2021/0[0-6]{1}.*Claims Household.xlsx",
        "category": "claims",
    },
    {
        "name": "prima_xlsx_claims_household_v2",
        "factory": PrimaClaimsXLSXHouseholdV2ToDataFrameFactory,
        "regexp": r".*202[1-9].*\/[0-9]{2}.*Claims Household.*\.xlsx",
        "category": "claims",
    },
    {
        "name": "prima_xlsx_claims",
        "factory": PrimaClaimsXLSXToDataFrameFactory,
        "regexp": r"^(08|09|10|11).*Claims.+.xlsx",
        "category": "claims",
    },
    {
        "name": "prima_xlsx_claims",
        "factory": PrimaClaimsXLSXV2ToDataFrameFactory,
        "regexp": r"^[0-9]{2}.*Claims.+.xlsx",
        "category": "claims",
    },
    {
        "name": "prima_xlsx_claims_v2",
        "factory": PrimaClaimsXLSXV2ToDataFrameFactory,
        "regexp": r"202[123]/[0-9]{2}.*Claims.+.xlsx",
        "category": "claims",
    },
    {
        "name": "prima_xlsx_claims_v3",
        "factory": PrimaClaimsXLSXV3ToDataFrameFactory,
        "regexp": r".*202[4-9].*\/[0-9]{2}.*Claims.+.xlsx",
        "category": "claims",
    },
    {
        "name": "prima_xlsx_suspensions_reactivations",
        "factory": PrimaSuspensionsReactivationsXLSXToDataFrameFactory,
        "regexp": r".*202[4-9].*\/[0-9]{2}.*suspensions & reactivation.+.xlsx",
        "category": "suspensions_reactivations",
    },
    {
        "name": "prima_xlsx_suspensions_reactivations",
        "factory": PrimaSuspensionsReactivationsXLSXToDataFrameFactory,
        "regexp": r".*suspensions & reactivation.+.xlsx",
        "category": "suspensions_reactivations",
    },
    {
        "name": "prima_xlsx_complaints",
        "factory": PrimaComplaintsXLSXToDataFrameFactory,
        "regexp": r"(09|10).\s\w*\s-\s(Complaints (DE|IT)).xlsx",
        "category": "complaints",
    },
    {
        "name": "prima_xlsx_complaints",
        "factory": PrimaComplaintsXLSXV2ToDataFrameFactory,
        "regexp": r"(?!(09|10)).*[0-9]{2}. \w* - Complaints (DE|IT).xlsx",
        "category": "complaints",
    },
    {
        "name": "prima_xlsx_complaints",
        "factory": PrimaComplaintsXLSXV2ToDataFrameFactory,
        "regexp": r"(?!(09|10)).*[0-9]{2}. \w* - Complaints (DE|IT)_adj.xlsx",
        "category": "complaints",
    },
    {
        "name": "prima_xlsx_complaints_italy_2023",
        "factory": PrimaComplaintsXLSXV3ToDataFrameFactory,
        "regexp": r".*Archivio Reclami [0-9]{2} 2023 - iptiQ Italia.xlsx",
        "category": "complaints",
    },
    {
        "name": "prima_xlsx_complaints",
        "factory": PrimaComplaintsXLSXV2ToDataFrameFactory,
        "regexp": r".*Archivio Reclami [0-9]{2} [0-9]{4} - iptiQ (Germania|Italia).xlsx",
        "category": "complaints",
    },
    {
        "name": "prima_xlsx_complaints_v2",
        "factory": PrimaComplaintsXLSXV2ToDataFrameFactory,
        "regexp": r".*Archivio Reclami [0-9]{4} - iptiQ (Germania|Italia)_[0-9]{2}_[0-9]{2}.xlsx",
        "category": "complaints",
    },
    {
        "name": "prima_csv_jira_complaints",
        "factory": PrimaCSVToDataFrameComplaintsJiraFactory,
        "regexp": r"complaints_jira/complaints_jira_[a-zA-Z]+_NARROW_[0-9]{4}-[0-9]{2}-[0-9]{2}\.csv",
        "category": "complaints_jira",
    },
]
