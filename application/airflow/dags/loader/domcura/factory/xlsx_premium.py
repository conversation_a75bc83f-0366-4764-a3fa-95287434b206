import pandas as pd
import re

from loader.domcura.interface import DomcuraPremiumXLSXPandasInterface
from loader.domcura.dto.premium import DomcuraXLSXPremiumDTO

from loader.core.custom_types import PandasDataFrameType
from loader.core.pandas import NA_VALUES
from loader.core.utils import remove_umlaut
from loader.core.xlsx_pandas import XLSXToDataFrameFactory


class DomcuraPremiumXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, csv_path: str, dto=DomcuraXLSXPremiumDTO):
        super().__init__(csv_path)
        self._interface = DomcuraPremiumXLSXPandasInterface(dto=dto)

    def get_dataframe(self) -> PandasDataFrameType:
        """
        This logic allows the loader to ingest files that contain 3 Excel tabs or less
        """
        interface = self._interface

        df_list = []
        for sheet_name in interface.sheet_names:
            try:
                df = pd.read_excel(
                    self._xlsx_path,
                    header=interface.header,
                    parse_dates=interface.get_date_fields(),
                    date_parser=interface.date_parser(),
                    dtype=interface.get_nondate_dtypes(),
                    thousands=interface.thousands,
                    skipfooter=interface.skipfooterint,
                    skiprows=interface.skiprows,
                    sheet_name=sheet_name,
                    na_values=NA_VALUES,
                    keep_default_na=False,
                )
            except ValueError:
                pass
            else:
                df_list.append(df)

        df = pd.concat(df_list, ignore_index=True)

        if isinstance(interface.header, list):
            df.columns = (
                pd.Series(df.columns.get_level_values(0)).apply(str)
                + " "
                + pd.Series(df.columns.get_level_values(1)).apply(str)
            )
            df.columns = [re.sub(" +", " ", col) for col in df.columns]
            df.columns = [col.lower() for col in df.columns]

        source_key = interface.get_all_source_key()
        df.columns = [remove_umlaut(col.strip()) for col in df.columns]
        df.columns = [source_key.get(col, col) for col in df.columns]
        return df
