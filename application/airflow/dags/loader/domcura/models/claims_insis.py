from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.claims import DomcuraXLSXClaimsInsisDTOV4

attr_dict = generate_table_model_from_dtos(
    "domcura_claims_insis", [DomcuraXLSXClaimsInsisDTOV4], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraClaimsInsis = type("DomcuraClaimsINSIS", (Base,), attr_dict)
