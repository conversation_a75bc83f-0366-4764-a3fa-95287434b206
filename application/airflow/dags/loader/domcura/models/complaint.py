from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.complaints import DomcuraXLSXComplaintsDTO

attr_dict = generate_table_model_from_dtos(
    "domcura_complaints", [DomcuraXLSXComplaintsDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraComplaints = type("DomcuraComplaints", (Base,), attr_dict)
