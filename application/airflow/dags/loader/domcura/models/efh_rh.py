from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.policy import DomcuraCSVPolicyDTO, DomcuraCSVPolicyDTOV2

attr_dict = generate_table_model_from_dtos(
    "domcura_efh_rh", [DomcuraCSVPolicyDTO, DomcuraCSVPolicyDTOV2], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraEfhRh = type("DomcuraEfhRh", (Base,), attr_dict)
