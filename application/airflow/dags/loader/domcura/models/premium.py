from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.premium import DomcuraXLSXPremiumDTO

attr_dict = generate_table_model_from_dtos("domcura_premium", [DomcuraXLSXPremiumDTO], schema=DEFAULT_DB_LANDING_SCHEMA)
DomcuraPremium = type("DomcuraPremium", (Base,), attr_dict)
