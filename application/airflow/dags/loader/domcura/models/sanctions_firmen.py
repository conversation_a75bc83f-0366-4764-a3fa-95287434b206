from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.sanctions.xlsx_sanctions_firmen import DomcuraXLSXSanctionsFirmenDTO

attr_dict = generate_table_model_from_dtos(
    "domcura_sanctions_firmen", [DomcuraXLSXSanctionsFirmenDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraSanctionsFirmen = type("DomcuraSanctionsFirmen", (Base,), attr_dict)
