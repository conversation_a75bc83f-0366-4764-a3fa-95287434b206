from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.sanctions.xlsx_sanctions_privat import DomcuraXLSXSanctionsPrivatDTO

attr_dict = generate_table_model_from_dtos(
    "domcura_sanctions_privat", [DomcuraXLSXSanctionsPrivatDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraSanctionsPrivat = type("DomcuraSanctionsPrivat", (Base,), attr_dict)
