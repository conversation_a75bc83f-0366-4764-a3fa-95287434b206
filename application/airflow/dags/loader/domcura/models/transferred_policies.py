from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.policy.xlsx_transferred_policies import DomcuraXLSXTransferredPoliciesDTO

attr_dict = generate_table_model_from_dtos(
    "domcura_transferred_policies", [DomcuraXLSXTransferredPoliciesDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraTransferredPolicies = type("DomcuraTransferredPolicies", (Base,), attr_dict)
