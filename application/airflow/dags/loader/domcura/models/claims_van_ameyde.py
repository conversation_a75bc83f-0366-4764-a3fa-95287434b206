from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.domcura.dto.claims import DomcuraXLSXClaimsVanAmeydeDTO

attr_dict = generate_table_model_from_dtos(
    "domcura_claims_van_ameyde", [DomcuraXLSXClaimsVanAmeydeDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
DomcuraClaimsVanAmeyde = type("DomcuraClaimsVanAmeyde", (Base,), attr_dict)
