from loader.domcura.factory import *

legacy_loads = ["domcura_efh_rh", "domcura_complaints"]

file_types = [
    {
        "name": "domcura_xlsx_sanctions_claims_privat",
        "factory": DomcuraSanctionsPrivatXLSXToDataFrameFactory,
        "regexp": r"domcura_.*_terror_sanktions.*_privat_.*\.xlsx?",
        "category": "sanctions_privat",
    },
    {
        "name": "domcura_xlsx_sanctions_claims_firmen",
        "factory": DomcuraSanctionsFirmenXLSXToDataFrameFactory,
        "regexp": r"domcura_.*_terror_sanktions.*_gewerbe_.*\.xlsx?",
        "category": "sanctions_firmen",
    },
    {
        "name": "domcura_csv_claims",
        "factory": DomcuraClaimsTOCSVToDataFrameFactory,
        "regexp": r"IPTIQ_S_2020_07_07.csv",
        "category": "claims",
    },
    {
        "name": "domcura_csv_claims",
        "factory": DomcuraClaimsV2TOCSVToDataFrameFactory,
        "regexp": r"IPTIQ_S_[\d]{4}_[\d]{2}_[\d]{2}.csv",
        "category": "claims",
    },
    {
        "name": "domcura_xlsx_claims_insis_2022",
        "factory": DomcuraClaimsInsisXLSXToDataFrameFactory,
        "regexp": r"DOM- [\d]{2}\.[\d]{2}\.2022 - large loss Overview.xlsx",
        "category": "claims_insis",
    },
    {
        "name": "domcura_xlsx_claims_insis_jan2023",
        "factory": DomcuraClaimsInsisXLSXToDataFrameFactory,
        "regexp": r"DOM- 31\.01\.2023 - large loss Overview.xlsx",
        "category": "claims_insis",
    },
    {
        "name": "domcura_xlsx_claims_insis_no_policy_id_2022",
        "factory": DomcuraClaimsInsisV2XLSXToDataFrameFactory,
        "regexp": r"DOM- [\d]{2}\.[\d]{2}\.2022 - large loss Overview.xlsx",
        "category": "claims_insis",
    },
    {
        "name": "domcura_xlsx_claims_insis_no_policy_id_2023",
        "factory": DomcuraClaimsInsisV2XLSXToDataFrameFactory,
        "regexp": r"DOM- [\d]{2}\.0[1-4]\.2023 - large loss Overview.xlsx",
        "category": "claims_insis",
    },
    {
        "name": "domcura_xlsx_claims_insis_no_closure_date",
        "factory": DomcuraClaimsInsisV3XLSXToDataFrameFactory,
        "regexp": r"DOM- 31\.05\.2023 - large loss Overview.xlsx",
        "category": "claims_insis",
    },
    {
        "name": "domcura_xlsx_claims_insis",
        "factory": DomcuraClaimsInsisV4XLSXToDataFrameFactory,
        "regexp": r"DOM- [\d]{2}\.[\d]{2}\.[\d]{4} - large loss Overview.xlsx",
        "category": "claims_insis",
    },
    {
        "name": "domcura_xlsx_claims_van_ameyde",
        "factory": DomcuraClaimsVanAmeydeXLSXToDataFrameFactory,
        "regexp": r"BDX VA [\d]{2}\.[\d]{2}\.[\d]{4}.xlsx",
        "category": "claims_van_ameyde",
    },
    {
        "name": "domcura_csv_policy_v1",
        "factory": DomcuraCSVToDataFrameFactory,
        "regexp": r"IPTIQ_EFH_RH_2020_0[2-5].*.csv",
        "category": "efh_rh",
    },
    {
        "name": "domcura_csv_policy_v2",
        "factory": DomcuraPolicyDTOV2CSVToDataFrameFactory,
        "regexp": r"IPTIQ_EFH_RH_[\d]{4}_[\d]{2}_[\d]*.csv",
        "category": "efh_rh",
    },
    {
        "name": "domcura_premium",
        "factory": DomcuraXLSXToDataFrameFactory,
        "regexp": r"iptiQ_[\d]{2}_[\d]{4}.xlsx",
        "category": "premium",
    },
    {
        "name": "domcura_premium_new",
        "factory": DomcuraPremiumXLSXToDataFrameFactory,
        "regexp": r"iptiQ_[\d]{2}_[\d]{4}_DC_Inno.*.xlsx",
        "category": "premium",
    },
    {
        "name": "domcura_complaints",
        "factory": DomcuraComplaintsTOXLSXToDataFrameFactory,
        "regexp": r"IPTIQ_Beschwerdeübersicht_[\d]{4}_[\d]{2}_[\d]{2}.xlsx",
        "category": "complaints",
    },
    {
        "name": "domcura_transferred_policies",
        "factory": DomcuraTransferredPoliciesXLSXToDataFrameFactory,
        "regexp": r"Info HF\d{2}\.xlsx",
        "category": "transferred_policies",
    },
]
