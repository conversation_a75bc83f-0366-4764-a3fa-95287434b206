#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import DateTime, Float, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraXLSXSanctionsFirmenDTO:
    name: str = dataclasses.field(
        metadata={
            "source": "NAME1_P",
            "mapping": "name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Company Name",
        }
    )
    address_line1: str = dataclasses.field(
        metadata={
            "source": "STRASSE_PA",
            "mapping": "address_line1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address location of the company",
        }
    )
    address_line2: str = dataclasses.field(
        metadata={
            "source": "FELD_3",
            "mapping": "address_line2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Possible secondary address",
        }
    )
    city: str = dataclasses.field(
        metadata={
            "source": "ORT_PA",
            "mapping": "city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City where the company resides",
        }
    )
    post_code: str = dataclasses.field(
        metadata={
            "source": "PLZ_PA",
            "mapping": "post_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Postal code of the company",
        }
    )
    country: str = dataclasses.field(
        metadata={
            "source": "LKZ_PA",
            "mapping": "country",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Country where the company resides",
        }
    )
    email: str = dataclasses.field(
        metadata={
            "source": "FELD_7",
            "mapping": "email",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "E-mail of the company",
        }
    )
    bvd_id: str = dataclasses.field(
        metadata={
            "source": "FELD_8",
            "mapping": "bvd_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    national_id: str = dataclasses.field(
        metadata={
            "source": "FELD_9",
            "mapping": "national_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
