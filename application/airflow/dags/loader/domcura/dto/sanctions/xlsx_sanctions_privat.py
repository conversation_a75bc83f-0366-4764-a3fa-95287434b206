#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import DateTime, Float, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraXLSXSanctionsPrivatDTO:
    first_name: str = dataclasses.field(
        metadata={
            "source": "NAME2_P",
            "mapping": "first_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the person",
        }
    )
    middle_name: str = dataclasses.field(
        metadata={
            "source": "FELD_2",
            "mapping": "middle_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "middle name of the person",
        }
    )
    last_name: str = dataclasses.field(
        metadata={
            "source": "NAME1_P",
            "mapping": "last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Last name of the person",
        }
    )
    gender: str = dataclasses.field(
        metadata={
            "source": "FELD_4",
            "mapping": "gender",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "gender of the person",
        }
    )
    date_of_birth: str = dataclasses.field(
        metadata={
            "source": "FELD_5",
            "mapping": "date_of_birth",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Date of Birth",
        }
    )
    nationality: str = dataclasses.field(
        metadata={
            "source": "FELD_6",
            "mapping": "nationality",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Nationality of the perrson",
        }
    )
    address_line1: str = dataclasses.field(
        metadata={
            "source": "STRASSE_PA",
            "mapping": "address_line1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address of the person",
        }
    )
    address_line2: str = dataclasses.field(
        metadata={
            "source": "FELD_8",
            "mapping": "address_line2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Secondary address of the person",
        }
    )
    city: str = dataclasses.field(
        metadata={
            "source": "ORT_PA",
            "mapping": "city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City where the person lives",
        }
    )
    post_code: str = dataclasses.field(
        metadata={
            "source": "PLZ_PA",
            "mapping": "post_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "post code of the area the person lives",
        }
    )
    country: str = dataclasses.field(
        metadata={
            "source": "LKZ_PA",
            "mapping": "country",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "country where the person lives",
        }
    )
    account_id: str = dataclasses.field(
        metadata={
            "source": "FELD_12",
            "mapping": "account_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "account identifier of the person",
        }
    )
    business: str = dataclasses.field(
        metadata={
            "source": "FELD_13",
            "mapping": "business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    tax_id: str = dataclasses.field(
        metadata={
            "source": "FELD_14",
            "mapping": "tax_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "tax identifier of the person",
        }
    )
    ssn: str = dataclasses.field(
        metadata={
            "source": "FELD_15",
            "mapping": "ssn",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    otherinformation_16: str = dataclasses.field(
        metadata={
            "source": "FELD_16",
            "mapping": "otherinformation_16",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    otherinformation_17: str = dataclasses.field(
        metadata={
            "source": "FELD_17",
            "mapping": "otherinformation_17",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    otherinformation_18: str = dataclasses.field(
        metadata={
            "source": "FELD_18",
            "mapping": "otherinformation_18",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    otherinformation_19: str = dataclasses.field(
        metadata={
            "source": "FELD_19",
            "mapping": "otherinformation_19",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
