#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraCSVPolicyDTO:
    VSNR: str = dataclasses.field(
        metadata={
            "source": "VSNR",
            "mapping": "policy_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy Id number",
        }
    )
    Geburtstag: str = dataclasses.field(
        metadata={
            "source": "Geburtstag",
            "mapping": "birthday",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Placheholder",
        }
    )
    Anrede: str = dataclasses.field(
        metadata={
            "source": "Anrede",
            "mapping": "saluation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
    VN_Name1: str = dataclasses.field(
        metadata={
            "source": "VN_Name1",
            "mapping": "first_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
            "pii": True,
        }
    )
    VN_Name2: str = dataclasses.field(
        metadata={
            "source": "VN_Name2",
            "mapping": "last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
            "pii": True,
        }
    )
    VN_PLZ: str = dataclasses.field(
        metadata={
            "source": "VN_PLZ",
            "mapping": "zip_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
    VN_Ort: str = dataclasses.field(
        metadata={
            "source": "VN_Ort",
            "mapping": "city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
    VN_Strasse: str = dataclasses.field(
        metadata={
            "source": "VN_Strasse",
            "mapping": "street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
    Produktname: str = dataclasses.field(
        metadata={
            "source": "Produktname",
            "mapping": "product",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
    Zahlweise: str = dataclasses.field(
        metadata={
            "source": "Zahlweise",
            "mapping": "payment_frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Values could be enum (yearly, quarterly , monthly, bi-yearly) (in german)",
        }
    )
    Vertragsstatus: str = dataclasses.field(
        metadata={
            "source": "Vertragsstatus",
            "mapping": "policy_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Vertragsbeginn: str = dataclasses.field(
        metadata={
            "source": "Vertragsbeginn",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Placeholder",
        }
    )
    Hauptfaelligkeit: str = dataclasses.field(
        metadata={
            "source": "Hauptfälligkeit",
            "mapping": "payment_due_date",  # doesnt look like a date in the value, all ints
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    akt_Vertragsablauf: str = dataclasses.field(
        metadata={
            "source": "akt_Vertragsablauf",
            "mapping": "expiration_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Placeholder",
        }
    )
    Stornogrund: str = dataclasses.field(
        metadata={
            "source": "Stornogrund",
            "mapping": "cancellation_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Selbstbehalt: str = dataclasses.field(
        metadata={
            "source": "Selbstbehalt",
            "mapping": "deductible",  # deductible? not exactly the same name
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Jahresnetto: float = dataclasses.field(
        metadata={
            "source": "Jahresnetto",
            "mapping": "premium_yearly_net",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Placeholder",
        }
    )
    Jahresbrutto: float = dataclasses.field(
        metadata={
            "source": "Jahresbrutto",
            "mapping": "premium_yearly_gross",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Placeholder",
        }
    )
    Gesellschaft_Name: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Name",
            "mapping": "company",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Agenturnummer: str = dataclasses.field(
        metadata={
            "source": "Agenturnummer",
            "mapping": "agency_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Bedingungen: str = dataclasses.field(
        metadata={
            "source": "Bedingungen",
            "mapping": "conditions",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Klauseln1: str = dataclasses.field(
        metadata={
            "source": "Klauseln1",
            "mapping": "clause_1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Klauseln2: str = dataclasses.field(
        metadata={
            "source": "Klauseln2",
            "mapping": "clause_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Risiko: str = dataclasses.field(
        metadata={
            "source": "Risiko",
            "mapping": "risk",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Wohnflaeche: int = dataclasses.field(
        metadata={
            "source": "Wohnfläche",
            "mapping": "living_area",
            "dtype": np.dtype("i4"),
            "sqlalchemy_type": Integer,
            "description": "Placeholder",
        }
    )
    Baujahr: int = dataclasses.field(
        metadata={
            "source": "Baujahr",
            "mapping": "construction_year",
            "dtype": np.dtype("f"),  # column has na field in it error, leveraging float instead
            "sqlalchemy_type": Integer,
            "description": "Placeholder",
        }
    )
    Gebaeudealteralterrabatt: str = dataclasses.field(
        metadata={
            "source": "Gebaeudealteralterrabatt",
            "mapping": "unknown",  # <====
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placeholder",
        }
    )
    Bauart: str = dataclasses.field(
        metadata={
            "source": "Bauart",
            "mapping": "construction_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Dach: str = dataclasses.field(
        metadata={
            "source": "Dach",
            "mapping": "roof_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Tarifzone: int = dataclasses.field(
        metadata={
            "source": "Tarifzone",
            "mapping": "unknown_2",  # <====
            "dtype": np.dtype("f"),  # column has na field in it error, leveraging float instead
            "sqlalchemy_type": Integer,
            "description": "Placeholder",
        }
    )
    ZUeRS_Zone: str = dataclasses.field(
        metadata={
            "source": "ZÜRS_Zone",
            "mapping": "unknown_3",  # <====
            "dtype": np.dtype("f"),  # column has na field in it error, leveraging float instead
            "sqlalchemy_type": Integer,
            "description": "Placeholder",
        }
    )
    Gefahr: str = dataclasses.field(
        metadata={
            "source": "Gefahr",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    GF_Status: str = dataclasses.field(
        metadata={
            "source": "GF_Status",
            "mapping": "cover_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    GF_Einschluss: str = dataclasses.field(
        metadata={
            "source": "GF_Einschluss",
            "mapping": "cover_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Placeholder",
        }
    )
    GF_Ausschluss: str = dataclasses.field(
        metadata={
            "source": "GF_Ausschluss",
            "mapping": "cover_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Placeholder",
        }
    )
    GF_Netto_VN: float = dataclasses.field(
        metadata={
            "source": "GF_Netto_VN",
            "mapping": "cover_premium_net",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Placeholder",
        }
    )
    Vers_Steuer: float = dataclasses.field(
        metadata={
            "source": "Vers_Steuer",
            "mapping": "cover_tax",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Placeholder",
        }
    )
    GF_Brutto_VN: float = dataclasses.field(
        metadata={
            "source": "GF_Brutto_VN",
            "mapping": "cover_premium_gross",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Placeholder",
        }
    )
    Vorversicherer: str = dataclasses.field(
        metadata={
            "source": "Vorversicherer",
            "mapping": "unknown_4",  # <====
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Vorvertragsnummer: str = dataclasses.field(
        metadata={
            "source": "Vorvertragsnummer",
            "mapping": "unknown_5",  # <====
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Anzahl_Vorschaeden: str = dataclasses.field(
        metadata={
            "source": "Anzahl_Vorschaeden",
            "mapping": "unknown_6",  # <====
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Vorschadenhoehe: str = dataclasses.field(
        metadata={
            "source": "Vorschadenhöhe",
            "mapping": "unknown_7",  # <====
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Nutzung: str = dataclasses.field(
        metadata={
            "source": "Nutzung",
            "mapping": "unknown_8",  # <====
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    VM_NR: str = dataclasses.field(
        metadata={
            "source": "VM_NR",
            "mapping": "unknown_9",  # <====
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )
    Beginn_VU: str = dataclasses.field(
        metadata={
            "source": "Beginn_VU",
            "mapping": "unknown_10",  # <====
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Placeholder",
        }
    )
