#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import DateTime, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraXLSXTransferredPoliciesDTO:
    policy_id: str = dataclasses.field(
        metadata={
            "source": "VSNR",
            "mapping": "policy_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy id",
        }
    )
    counter: str = dataclasses.field(
        metadata={
            "source": "Zaehler",
            "mapping": "counter",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "It is always one to be summed to give the count at the final row of the spreadsheet",
        }
    )
    generic_due_date: str = dataclasses.field(
        metadata={
            "source": "Stammfaelligkeit",
            "mapping": "generic_due_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Due date in the generic form of DDMM",
        }
    )
    transfer_date: str = dataclasses.field(
        metadata={
            "source": "Umdeckungszeitpunkt",
            "mapping": "transfer_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "The date when the risk is transferred to the new risk carrier",
        }
    )
