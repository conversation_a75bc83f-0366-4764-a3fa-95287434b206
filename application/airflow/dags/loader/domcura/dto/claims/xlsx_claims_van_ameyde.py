#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraXLSXClaimsVanAmeydeDTO:
    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "UWY",
            "mapping": "underwriting_year",
            "dtype": np.dtype("i4"),
            "sqlalchemy_type": Integer,
            "description": "The underwriting year of the claim",
        }
    )

    policy_type: str = dataclasses.field(
        metadata={
            "source": "Policy Type",
            "mapping": "policy_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The type of policy being claimed",
        }
    )

    claim_type: str = dataclasses.field(
        metadata={
            "source": "Claim Type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The type of claim",
        }
    )

    claim_id_domcura: str = dataclasses.field(
        metadata={
            "source": "Client Ref",
            "mapping": "claim_id_domcura",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The claim identifier from Domcura",
        }
    )

    claim_id_van_ameyde: str = dataclasses.field(
        metadata={
            "source": "TPA Claim Ref",
            "mapping": "claim_id_van_ameyde",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The claim identifier from Van Ameyde",
        }
    )

    date_of_loss: str = dataclasses.field(
        metadata={
            "source": "Date of Loss",
            "mapping": "date_of_loss",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim occurred on",
        }
    )

    date_of_notification: str = dataclasses.field(
        metadata={
            "source": "Date of Notification",
            "mapping": "date_of_notification",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim was made",
        }
    )

    country_of_event: str = dataclasses.field(
        metadata={
            "source": "Country of Event",
            "mapping": "country_of_event",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The country the claim occurred in",
        }
    )

    cause_type: str = dataclasses.field(
        metadata={
            "source": "Cause / Peril",
            "mapping": "cause_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The cause of the claim",
        }
    )

    policy_identifier: str = dataclasses.field(
        metadata={
            "source": "Policy No",
            "mapping": "policy_identifier",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The policy the claim is for",
        }
    )

    contact_type: str = dataclasses.field(
        metadata={
            "source": "Claim origin",
            "mapping": "contact_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The contact channel the claim arrived through",
        }
    )

    calamity: str = dataclasses.field(
        metadata={
            "source": "Calamity Yes / No",
            "mapping": "calamity",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Is the claim considered a calamity",
        }
    )

    event_summary: str = dataclasses.field(
        metadata={
            "source": "Summary of event",
            "mapping": "event_summary",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "A description of the claim",
        }
    )

    policy_holder: str = dataclasses.field(
        metadata={
            "source": "Policy holder",
            "mapping": "policy_holder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The name of the policy holder",
        }
    )

    insured_object: str = dataclasses.field(
        metadata={
            "source": "Risk Type",
            "mapping": "insured_object",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The type of risk insured of the claim",
        }
    )

    accident_code_type: str = dataclasses.field(
        metadata={
            "source": "Accident Code Type",
            "mapping": "accident_code_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The accident code of the claim",
        }
    )

    city: str = dataclasses.field(
        metadata={
            "source": "City",
            "mapping": "city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The city of the insured property",
        }
    )

    street: str = dataclasses.field(
        metadata={
            "source": "Street",
            "mapping": "street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The street of the insured property",
        }
    )

    house_number: str = dataclasses.field(
        metadata={
            "source": "House Number",
            "mapping": "house_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The house number of the insured property",
        }
    )

    post_code: str = dataclasses.field(
        metadata={
            "source": "Postal Code",
            "mapping": "post_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The post code of the insured property",
        }
    )

    country: str = dataclasses.field(
        metadata={
            "source": "Country",
            "mapping": "country",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The country of the insured property",
        }
    )

    liability: str = dataclasses.field(
        metadata={
            "source": "Liability (if applicable)",
            "mapping": "liability",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The liability on the claim",
        }
    )

    claim_status: str = dataclasses.field(
        metadata={
            "source": "File Status",
            "mapping": "claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The status of the claim",
        }
    )

    date_of_closure: str = dataclasses.field(
        metadata={
            "source": "Date of Closure",
            "mapping": "date_of_closure",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim was closed",
        }
    )

    date_of_last_review: str = dataclasses.field(
        metadata={
            "source": "Date of last file Review",
            "mapping": "date_of_last_review",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim was last reviewed",
        }
    )

    policy_excess: str = dataclasses.field(
        metadata={
            "source": "Policy excess amount",
            "mapping": "policy_excess",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The excess on the policy",
        }
    )

    reserve_pd: str = dataclasses.field(
        metadata={
            "source": "Reserve Property Damage",
            "mapping": "reserve_pd",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The reserve for property damage on the claim",
        }
    )

    reserve_bi: str = dataclasses.field(
        metadata={
            "source": "Reserve Bodily Injury",
            "mapping": "reserve_bi",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The reserve for bodily injury on the claim",
        }
    )

    reserve_external: str = dataclasses.field(
        metadata={
            "source": "Reserve External Expenses",
            "mapping": "reserve_external",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The reserve for external expenses on the claim",
        }
    )

    reserve_other: str = dataclasses.field(
        metadata={
            "source": "Reserve Other",
            "mapping": "reserve_other",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The reserve for anything else on the claim",
        }
    )

    paid_pd: str = dataclasses.field(
        metadata={
            "source": "Paid Property Damage",
            "mapping": "paid_pd",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for property damage on the claim",
        }
    )

    paid_bi: str = dataclasses.field(
        metadata={
            "source": "Paid Bodily Injury",
            "mapping": "paid_bi",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for bodily injury on the claim",
        }
    )

    paid_external: str = dataclasses.field(
        metadata={
            "source": "Paid External Expenses",
            "mapping": "paid_external",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for external expenses on the claim",
        }
    )

    paid_other: str = dataclasses.field(
        metadata={
            "source": "Paid Other",
            "mapping": "paid_other",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for anything else on the claim",
        }
    )

    recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Recoveries outstanding",
            "mapping": "recovery_reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The outstanding amount for recoveries on the claim",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "Recoveries Received",
            "mapping": "recovered",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount already recovered on the claim",
        }
    )

    deductible_reserve: str = dataclasses.field(
        metadata={
            "source": "Deductible Outstanding",
            "mapping": "deductible_reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The outstanding amount for deductibles on the claim",
        }
    )

    deducted: str = dataclasses.field(
        metadata={
            "source": "Deductible Received",
            "mapping": "deducted",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount already deducted on the claim",
        }
    )

    total_paid: str = dataclasses.field(
        metadata={
            "source": "Total Paid",
            "mapping": "total_paid",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The total amount paid on the claim",
        }
    )

    total_reserve: str = dataclasses.field(
        metadata={
            "source": "Total Reserve",
            "mapping": "total_reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The total amount reserved on the claim",
        }
    )

    total_cost: str = dataclasses.field(
        metadata={
            "source": "Total Cost",
            "mapping": "total_cost",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The total costs on the claim",
        }
    )

    claim_handling_fee: str = dataclasses.field(
        metadata={
            "source": "Claim Handling Fee",
            "mapping": "claim_handling_fee",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The fee for Van Ameyde handling the claim",
        }
    )

    currency: str = dataclasses.field(
        metadata={
            "source": "Currency",
            "mapping": "currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The currency of the claim",
        }
    )
