#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Float, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraCSVClaimsDTOV2:
    VSNR: str = dataclasses.field(
        metadata={
            "source": "VSNR",
            "mapping": "policy_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )

    Risiko: str = dataclasses.field(
        metadata={
            "source": "Risiko",
            "mapping": "risk_address",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Risk Address",
        }
    )

    Schaden_VU: str = dataclasses.field(
        metadata={
            "source": "Schaden_VU",
            "mapping": "plaholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )

    Schadennr_Eigen: str = dataclasses.field(
        metadata={
            "source": "Schadennr_Eigen",
            "mapping": "claim_number_domcura",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )

    Schadentag: str = dataclasses.field(
        metadata={
            "source": "Schadentag",
            "mapping": "claim_notification_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim notification date / date of loss",
        }
    )

    Meldetag: str = dataclasses.field(
        metadata={
            "source": "Meldetag",
            "mapping": "claim_registration_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim registration date / date of registration",
        }
    )

    Status: str = dataclasses.field(
        metadata={
            "source": "Status",
            "mapping": "claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status",
        }
    )

    Schadenart: str = dataclasses.field(
        metadata={
            "source": "Schadenart",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of claim",
        }
    )

    Schadenursache: str = dataclasses.field(
        metadata={
            "source": "Schadenursache",
            "mapping": "claim_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cause of damage/ reason of claim",
        }
    )

    Gefahr: str = dataclasses.field(
        metadata={
            "source": "Gefahr",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover",
        }
    )

    Produkt: str = dataclasses.field(
        metadata={
            "source": "Produkt",
            "mapping": "product",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Product name",
        }
    )

    Sparte: str = dataclasses.field(
        metadata={
            "source": "Sparte",
            "mapping": "insured_object",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insured object",
        }
    )
    Anteil: str = dataclasses.field(
        metadata={
            "source": "Anteil",
            "mapping": "share",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "share",
        }
    )

    Agenturnummer: str = dataclasses.field(
        metadata={
            "source": "Agenturnummer",
            "mapping": "agent_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "agent_number",
        }
    )

    S_Eingang: str = dataclasses.field(
        metadata={
            "source": "S_Eingang",
            "mapping": "claim_begin_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim begin date",
        }
    )

    S_Ausgang: str = dataclasses.field(
        metadata={
            "source": "S_Ausgang",
            "mapping": "claim_end_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim end date",
        }
    )

    Schliessdatum: str = dataclasses.field(
        metadata={
            "source": "Schliessdatum",
            "mapping": "placeholder_schliessdatum",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Deckungsentsch: str = dataclasses.field(
        metadata={
            "source": "Deckungsentsch",
            "mapping": "cover_decision",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover decision",
        }
    )

    VN_Name: str = dataclasses.field(
        metadata={
            "source": "VN_Name",
            "mapping": "placeholder_VN_Name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Nr: str = dataclasses.field(
        metadata={
            "source": "VM_Nr",
            "mapping": "placeholder_VM_Nr",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Name: str = dataclasses.field(
        metadata={
            "source": "VM_Name",
            "mapping": "placeholder_VM_Name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Typ: str = dataclasses.field(
        metadata={
            "source": "VM_Typ",
            "mapping": "placeholder_VM_Typ",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Reg_Ford: str = dataclasses.field(
        metadata={
            "source": "Reg_Ford",
            "mapping": "recovery_demand",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Recovery demand",
        }
    )

    Reg_Ausgleich: str = dataclasses.field(
        metadata={
            "source": "Reg_Ausgleich",
            "mapping": "recovery_amount_received",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Recovery amount received",
        }
    )

    Kostenentsch: str = dataclasses.field(
        metadata={
            "source": "Kostenentsch",
            "mapping": "lae_expense_paid",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Loss adjustor expenses paid",
        }
    )

    Entschaedigung: str = dataclasses.field(
        metadata={
            "source": "Entschaedigung",
            "mapping": "placeholder_Kostenentsch",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Mandant: str = dataclasses.field(
        metadata={
            "source": "Mandant",
            "mapping": "mandant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Mandant",
        }
    )

    Reserve: str = dataclasses.field(
        metadata={
            "source": "Reserve",
            "mapping": "reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Reserve for loss adjustor",
        }
    )

    Gezahlt: str = dataclasses.field(
        metadata={
            "source": "Gezahlt",
            "mapping": "placeholder_Gezahlt",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Gesamt: str = dataclasses.field(
        metadata={
            "source": "Gesamt",
            "mapping": "total_incurred",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )

    Gesamt_VU: str = dataclasses.field(
        metadata={
            "source": "Gesamt_VU",
            "mapping": "placeholder_Gesamt_VU",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    VN_Vollname: str = dataclasses.field(
        metadata={
            "source": "VN_Vollname",
            "mapping": "placeholder_VN_Vollname",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VN_Vorname: str = dataclasses.field(
        metadata={
            "source": "VN_Vorname",
            "mapping": "placeholder_VN_Vorname",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Zahlungen: str = dataclasses.field(
        metadata={
            "source": "Zahlungen",
            "mapping": "placeholder_Zahlungen",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Reservekosten: str = dataclasses.field(
        metadata={
            "source": "Reservekosten",
            "mapping": "placeholder_Reservekosten",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Verursachertyp: str = dataclasses.field(
        metadata={
            "source": "Verursachertyp",
            "mapping": "placeholder_Verursachertyp",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Anspruchsteller: str = dataclasses.field(
        metadata={
            "source": "Anspruchsteller",
            "mapping": "placeholder_Anspruchsteller",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Prod_Nr: str = dataclasses.field(
        metadata={
            "source": "Prod_Nr",
            "mapping": "placeholder_Prod_Nr",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Produktlinie: str = dataclasses.field(
        metadata={
            "source": "Produktlinie",
            "mapping": "placeholder_Produktlinie",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Bedingungen: str = dataclasses.field(
        metadata={
            "source": "Bedingungen",
            "mapping": "placeholder_Bedingungen",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Typ_2: str = dataclasses.field(
        metadata={
            "source": "VM_Typ_2",
            "mapping": "placeholder_VM_Typ_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_Nr: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Nr",
            "mapping": "placeholder_Gesellschaft_Nr",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_Name_1: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Name_1",
            "mapping": "placeholder_Gesellschaft_Name_1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_Name_2: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Name_2",
            "mapping": "placeholder_Gesellschaft_Name_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    ID_Gesellschaft: str = dataclasses.field(
        metadata={
            "source": "ID_Gesellschaft",
            "mapping": "placeholder_ID_Gesellschaft",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    ID_Vertrag: str = dataclasses.field(
        metadata={
            "source": "ID_Vertrag",
            "mapping": "placeholder_ID_Vertrag",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    ID_Schaden: str = dataclasses.field(
        metadata={
            "source": "ID_Schaden",
            "mapping": "placeholder_ID_Schaden",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_ID: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_ID",
            "mapping": "placeholder_Gesellschaft_ID",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VERS_ID: str = dataclasses.field(
        metadata={
            "source": "VERS_ID",
            "mapping": "placeholder_VERS_ID",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    TEILSTRING_AGENTURNR: str = dataclasses.field(
        metadata={
            "source": "TEILSTRING_AGENTURNR",
            "mapping": "placeholder_TEILSTRING_AGENTURNR",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    PLZ: str = dataclasses.field(
        metadata={
            "source": "PLZ",
            "mapping": "placeholder_PLZ",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
