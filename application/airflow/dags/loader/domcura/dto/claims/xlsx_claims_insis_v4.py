#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore

from loader.domcura.dto.claims import DomcuraXLSXClaimsInsisDTOV3


@dataclass(init=False, repr=False)
class DomcuraXLSXClaimsInsisDTOV4(DomcuraXLSXClaimsInsisDTOV3):
    claim_closure_date: str = dataclasses.field(
        metadata={
            "source": "Schliessdatum",
            "mapping": "claim_closure_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closure date of a claim, when the last payment settles",
        }
    )

    cost_payments_before_handover: str = dataclasses.field(
        metadata={
            "source": "Payments Domcura (Costs)",
            "mapping": "cost_payments_before_handover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payments done by <PERSON><PERSON>ra on costs before outsourcing the claim",
        }
    )

    indemnity_payments_before_handover: str = dataclasses.field(
        metadata={
            "source": "Payments Domcura (Indemnity)",
            "mapping": "indemnity_payments_before_handover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payments done by Domcura on indemnity before outsourcing the claim",
        }
    )
