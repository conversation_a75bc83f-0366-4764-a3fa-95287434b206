#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore

from loader.domcura.dto.claims import DomcuraXLSXClaimsInsisDTOV2


@dataclass(init=False, repr=False)
class DomcuraXLSXClaimsInsisDTOV3(DomcuraXLSXClaimsInsisDTOV2):
    policy_id_insis: str = dataclasses.field(
        metadata={
            "source": "Claims Policy ID",
            "mapping": "policy_id_insis",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
