#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraXLSXClaimsInsisDTO:
    policy_id_domcura: str = dataclasses.field(
        metadata={
            "source": "Policy ID Domcura",
            "mapping": "policy_id_domcura",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The policy the claim is for",
        }
    )
    claim_id_insis: str = dataclasses.field(
        metadata={
            "source": "Claims ID INSIS",
            "mapping": "claim_id_insis",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The id for the claim in INSIS",
        }
    )
    claim_id_van_ameyde: str = dataclasses.field(
        metadata={
            "source": "Claims ID VA",
            "mapping": "claim_id_van_ameyde",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The id for the claim from Van Ameyde",
        }
    )
    claim_id_domcura: str = dataclasses.field(
        metadata={
            "source": "Claim ID Domcura",
            "mapping": "claim_id_domcura",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The id for the claim from Domcura",
        }
    )
    policy_holder_name: str = dataclasses.field(
        metadata={
            "source": "Name",
            "mapping": "policy_holder_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The name of the policy holder",
        }
    )
    risk_address: str = dataclasses.field(
        metadata={
            "source": "Risiko",
            "mapping": "risk_address",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The address of the insured property",
        }
    )
    claim_notification_date: str = dataclasses.field(
        metadata={
            "source": "Schadentag",
            "mapping": "claim_notification_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim notification date / date of loss",
        }
    )
    national_catastrophe: str = dataclasses.field(
        metadata={
            "source": "NAT CAT              (Bernd 14.07 - 17.7)      ( Volker 18.06 - 29.06)",
            "mapping": "national_catastrophe",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Whether the claim was part of a specific national catastrophe",
        }
    )
    claim_registration_date: str = dataclasses.field(
        metadata={
            "source": "Meldetag bei uns",
            "mapping": "claim_registration_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim registration date / date of registration",
        }
    )
    claim_registration_year: str = dataclasses.field(
        metadata={
            "source": "Meldejahr",
            "mapping": "claim_registration_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    claim_status: str = dataclasses.field(
        metadata={
            "source": "Status",
            "mapping": "claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The current status of the claim",
        }
    )
    claim_type: str = dataclasses.field(
        metadata={
            "source": "Schadenart",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of claim",
        }
    )
    cause_type: str = dataclasses.field(
        metadata={
            "source": "Schadenursache",
            "mapping": "cause_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cause of damage/ reason of claim",
        }
    )
    cover: str = dataclasses.field(
        metadata={
            "source": "Gefahr",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover",
        }
    )
    cost_reserve: str = dataclasses.field(
        metadata={
            "source": "Cost reserve",
            "mapping": "cost_reserve",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    indemnity_reserve: str = dataclasses.field(
        metadata={
            "source": "Indemnity reserve",
            "mapping": "indemnity_reserve",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    paid_costs: str = dataclasses.field(
        metadata={
            "source": "paid costs",
            "mapping": "paid_costs",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    paid_indemnity: str = dataclasses.field(
        metadata={
            "source": "paid indemnity",
            "mapping": "paid_indemnity",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    incurred: str = dataclasses.field(
        metadata={
            "source": "Incurred",
            "mapping": "incurred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    payments_before_handover: str = dataclasses.field(
        metadata={
            "source": "Payments from Domcura (before they handover)",
            "mapping": "payments_before_handover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    comments: str = dataclasses.field(
        metadata={
            "source": "Comment / last update",
            "mapping": "comments",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
    insis_notes: str = dataclasses.field(
        metadata={
            "source": "INSIS",
            "mapping": "insis_notes",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
