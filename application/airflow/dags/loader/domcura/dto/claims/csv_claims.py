#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Float, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraCSVClaimsDTO:
    Gefahr: str = dataclasses.field(
        metadata={
            "source": "Gefahr",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placeholder",
        }
    )

    Produkt: str = dataclasses.field(
        metadata={
            "source": "Produkt",
            "mapping": "product",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Sparte: str = dataclasses.field(
        metadata={
            "source": "Sparte",
            "mapping": "placeholder_sparte",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
    Anteil: str = dataclasses.field(
        metadata={
            "source": "Anteil",
            "mapping": "placeholder_anteil",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Agenturnummer: str = dataclasses.field(
        metadata={
            "source": "Agenturnummer",
            "mapping": "placeholder_agenturnummer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    S_Eingang: str = dataclasses.field(
        metadata={
            "source": "S_Eingang",
            "mapping": "placeholder_s_eigang",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    S_Ausgang: str = dataclasses.field(
        metadata={
            "source": "S_Ausgang",
            "mapping": "placeholder_s_ausgang",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Schliessdatum: str = dataclasses.field(
        metadata={
            "source": "Schliessdatum",
            "mapping": "placeholder_schliessdatum",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Deckungsentsch: str = dataclasses.field(
        metadata={
            "source": "Deckungsentsch",
            "mapping": "placeholder_deckungsentsch",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VN_Name: str = dataclasses.field(
        metadata={
            "source": "VN_Name",
            "mapping": "placeholder_VN_Name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Nr: str = dataclasses.field(
        metadata={
            "source": "VM_Nr",
            "mapping": "placeholder_VM_Nr",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Name: str = dataclasses.field(
        metadata={
            "source": "VM_Name",
            "mapping": "placeholder_VM_Name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Typ: str = dataclasses.field(
        metadata={
            "source": "VM_Typ",
            "mapping": "placeholder_VM_Typ",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Reg_Ford: str = dataclasses.field(
        metadata={
            "source": "Reg_Ford",
            "mapping": "placeholder_Reg_Ford",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Reg_Ausgleich: str = dataclasses.field(
        metadata={
            "source": "Reg_Ausgleich",
            "mapping": "placeholder_Reg_Ausgleich",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Kostenentsch: str = dataclasses.field(
        metadata={
            "source": "Kostenentsch",
            "mapping": "placeholder_Kostenentsch",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Entschaedigung: str = dataclasses.field(
        metadata={
            "source": "Entschaedigung",
            "mapping": "placeholder_Kostenentsch",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Mandant: str = dataclasses.field(
        metadata={
            "source": "Mandant",
            "mapping": "placeholder_Mandant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Reserve: str = dataclasses.field(
        metadata={
            "source": "Reserve",
            "mapping": "placeholder_Reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Gezahlt: str = dataclasses.field(
        metadata={
            "source": "Gezahlt",
            "mapping": "placeholder_Gezahlt",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Gesamt: str = dataclasses.field(
        metadata={
            "source": "Gesamt",
            "mapping": "placeholder_Gesamt",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Gesamt_VU: str = dataclasses.field(
        metadata={
            "source": "Gesamt_VU",
            "mapping": "placeholder_Gesamt_VU",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    VN_Vollname: str = dataclasses.field(
        metadata={
            "source": "VN_Vollname",
            "mapping": "placeholder_VN_Vollname",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VN_Vorname: str = dataclasses.field(
        metadata={
            "source": "VN_Vorname",
            "mapping": "placeholder_VN_Vorname",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Zahlungen: str = dataclasses.field(
        metadata={
            "source": "Zahlungen",
            "mapping": "placeholder_Zahlungen",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Reservekosten: str = dataclasses.field(
        metadata={
            "source": "Reservekosten",
            "mapping": "placeholder_Reservekosten",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "Placheholder",
        }
    )

    Verursachertyp: str = dataclasses.field(
        metadata={
            "source": "Verursachertyp",
            "mapping": "placeholder_Verursachertyp",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Anspruchsteller: str = dataclasses.field(
        metadata={
            "source": "Anspruchsteller",
            "mapping": "placeholder_Anspruchsteller",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Prod_Nr: str = dataclasses.field(
        metadata={
            "source": "Prod_Nr",
            "mapping": "placeholder_Prod_Nr",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Produktlinie: str = dataclasses.field(
        metadata={
            "source": "Produktlinie",
            "mapping": "placeholder_Produktlinie",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Bedingungen: str = dataclasses.field(
        metadata={
            "source": "Bedingungen",
            "mapping": "placeholder_Bedingungen",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VM_Typ_2: str = dataclasses.field(
        metadata={
            "source": "VM_Typ_2",
            "mapping": "placeholder_VM_Typ_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_Nr: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Nr",
            "mapping": "placeholder_Gesellschaft_Nr",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_Name_1: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Name_1",
            "mapping": "placeholder_Gesellschaft_Name_1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_Name_2: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_Name_2",
            "mapping": "placeholder_Gesellschaft_Name_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    ID_Gesellschaft: str = dataclasses.field(
        metadata={
            "source": "ID_Gesellschaft",
            "mapping": "placeholder_ID_Gesellschaft",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    ID_Vertrag: str = dataclasses.field(
        metadata={
            "source": "ID_Vertrag",
            "mapping": "placeholder_ID_Vertrag",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    ID_Schaden: str = dataclasses.field(
        metadata={
            "source": "ID_Schaden",
            "mapping": "placeholder_ID_Schaden",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    Gesellschaft_ID: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft_ID",
            "mapping": "placeholder_Gesellschaft_ID",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    VERS_ID: str = dataclasses.field(
        metadata={
            "source": "VERS_ID",
            "mapping": "placeholder_VERS_ID",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    TEILSTRING_AGENTURNR: str = dataclasses.field(
        metadata={
            "source": "TEILSTRING_AGENTURNR",
            "mapping": "placeholder_TEILSTRING_AGENTURNR",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )

    PLZ: str = dataclasses.field(
        metadata={
            "source": "PLZ",
            "mapping": "placeholder_PLZ",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Placheholder",
        }
    )
