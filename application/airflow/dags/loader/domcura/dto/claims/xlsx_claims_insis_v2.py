#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore

from loader.domcura.dto.claims import DomcuraXLSXClaimsInsisDTO


@dataclass(init=False, repr=False)
class DomcuraXLSXClaimsInsisDTOV2(DomcuraXLSXClaimsInsisDTO):
    recourse_received: str = dataclasses.field(
        metadata={
            "source": "Recourse received",
            "mapping": "recourse_received",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "",
        }
    )
