#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import DateTime, Float, String  # type: ignore


@dataclass(init=False, repr=False)
class DomcuraXLSXPremiumDTO:
    VSNR: str = dataclasses.field(
        metadata={
            "source": "VSNR",
            "mapping": "policy_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance policy number",
        }
    )
    VN_Name1: str = dataclasses.field(
        metadata={
            "source": "VN_Name1",
            "mapping": "last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Family name of the policyholder",
        }
    )
    VUH: str = dataclasses.field(
        metadata={
            "source": "VUH",
            "mapping": "unknown",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown",
        }
    )
    Gesellschaft: str = dataclasses.field(
        metadata={
            "source": "Gesellschaft",
            "mapping": "insurer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurer",
        }
    )
    GESNR: str = dataclasses.field(
        metadata={
            "source": "GESNR",
            "mapping": "insurer_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurer Number",
        }
    )
    Agentur: str = dataclasses.field(
        metadata={
            "source": "Agentur",
            "mapping": "unknown4",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurer related",
        }
    )
    Produkt: str = dataclasses.field(
        metadata={
            "source": "Produkt",
            "mapping": "product_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Product Name",
        }
    )
    Sparte: str = dataclasses.field(
        metadata={
            "source": "Sparte",
            "mapping": "insured_object",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "~Insured object",
        }
    )
    Produktlinie: str = dataclasses.field(
        metadata={
            "source": "Produktlinie",
            "mapping": "product_line",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Product Line",
        }
    )
    Gefahr: str = dataclasses.field(
        metadata={
            "source": "Gefahr",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Risk type = Cover",
        }
    )
    OPNR: str = dataclasses.field(
        metadata={
            "source": "OPNR",
            "mapping": "unknown8",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown8",
        }
    )
    Buchtext: str = dataclasses.field(
        metadata={
            "source": "Buchtext",
            "mapping": "unknown8A",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Booktext",
        }
    )
    Buchart: str = dataclasses.field(
        metadata={
            "source": "Buchart",
            "mapping": "unknown9",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Booktype",
        }
    )
    von: str = dataclasses.field(
        metadata={
            "source": "von",
            "mapping": "from",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Invoice for the period starting on",
        }
    )
    bis: str = dataclasses.field(
        metadata={
            "source": "bis",
            "mapping": "to",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": DateTime,
            "description": "Invoice for the period ending on",
        }
    )
    Anteil: str = dataclasses.field(
        metadata={
            "source": "Anteil",
            "mapping": "unknown10",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Share of insurance",
        }
    )
    Netto: str = dataclasses.field(
        metadata={
            "source": "Netto",
            "mapping": "unknown11",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Risk premium net",
        }
    )
    VST: str = dataclasses.field(
        metadata={
            "source": "VST",
            "mapping": "unknown12",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Risk insurance tax",
        }
    )
    Brutto: str = dataclasses.field(
        metadata={
            "source": "Brutto",
            "mapping": "unknown13",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Risk premium gross",
        }
    )
    Courtage: str = dataclasses.field(
        metadata={
            "source": "Courtage",
            "mapping": "unknown14",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "iptiQ's part",
        }
    )
    Abrechnungsbetrag: str = dataclasses.field(
        metadata={
            "source": "Abrechnungsbetrag",
            "mapping": "unknown15",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "What is paid to iptiQ",
        }
    )
    VUA: str = dataclasses.field(
        metadata={
            "source": "VUA",
            "mapping": "unknown15",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "unknown9",
        }
    )
