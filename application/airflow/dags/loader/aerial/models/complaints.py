from loader.aerial.dto.complaints import AerialXLSXComplaintsDTO
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos

attr_dict = generate_table_model_from_dtos(
    "aerial_complaints", [AerialXLSXComplaintsDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
AerialComplaints = type("AerialComplaints", (Base,), attr_dict)
