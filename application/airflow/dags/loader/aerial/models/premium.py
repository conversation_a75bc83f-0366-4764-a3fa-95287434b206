from loader.aerial.dto.premium import AerialXLSXPremiumDTO
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos

attr_dict = generate_table_model_from_dtos(
    "aerial_premium", [AerialXLSXPremiumDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
AerialPremium = type("AerialPremium", (Base,), attr_dict)
