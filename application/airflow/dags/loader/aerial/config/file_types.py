from loader.aerial.factory import *

file_types = [
    {
        "name": "aerial_premium_2022",
        "factory": AerialXLSXToDataFrameFactory,
        "regexp": r"[\d]{2}-IPTIQ-[A-Z]+-2022.*\.xlsx",
        "category": "premium",
    },
    {
        "name": "aerial_premium_2023_to_july",
        "factory": AerialXLSXToDataFrameFactory,
        "regexp": r"0[1-7]-IPTIQ-[A-Z]+-2023.*\.xlsx",
        "category": "premium",
    },
    {
        "name": "aerial_premium",
        "factory": AerialXLSXToDataFrameFactoryV2,
        "regexp": r"[\d]{2}-IPTIQ-[A-Z]+-[\d]{4}.*\.xlsx",
        "category": "premium",
    },
    {
        "name": "aerial_claims",
        "factory": AerialClaimsXLSXToDataFrameFactory,
        # placeholder for aerial claims regex
        "regexp": r"[\d]{2}-Claims-IPTIQ-.*-[\d]{4}.xlsx",
        "category": "claims",
    },
    {
        "name": "aerial_complaints",
        "factory": AerialComplaintsXLSXToDataFrameFactory,
        "regexp": r"[\d]{4}_[\d]{2}-Complaints_IPTIQ.*.xlsx",
        "category": "complaints",
    },
]
