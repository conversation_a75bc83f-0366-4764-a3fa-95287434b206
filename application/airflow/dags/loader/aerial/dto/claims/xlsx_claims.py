import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class AerialXLSXClaimsDTO:
    policy_number: str = dataclasses.field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    claim_number: str = dataclasses.field(
        metadata={
            "source": "Claim reference",
            "mapping": "claim_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each claim",
        }
    )

    occurrence_date: str = dataclasses.field(
        metadata={
            "source": "Occurrence_date",
            "mapping": "occurence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim Event date - When the event = cause took place",
        }
    )

    postcode_occurrence: str = dataclasses.field(
        metadata={
            "source": "Postcode_occurence",
            "mapping": "postcode_occurence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - Postcode",
        }
    )

    city_occurrence: str = dataclasses.field(
        metadata={
            "source": "City_occurence",
            "mapping": "city_occurence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - City",
        }
    )

    prov_occurrence: str = dataclasses.field(
        metadata={
            "source": "Prov_occurrence",
            "mapping": "prov_occurrence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Province where the damage (accident) occurred",
        }
    )

    date_notified_to_aerial: str = dataclasses.field(
        metadata={
            "source": "Date notified to AERIAL",
            "mapping": "date_notified_to_aerial",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date when the damage (accident) is notified to Aerial",
        }
    )

    opening_date: str = dataclasses.field(
        metadata={
            "source": "Opening_date",
            "mapping": "opening_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim opening date",
        }
    )

    last_review_date: str = dataclasses.field(
        metadata={
            "source": "Last_review",
            "mapping": "last_review_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim last review date",
        }
    )

    insured: str = dataclasses.field(
        metadata={
            "source": "Insured",
            "mapping": "insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the insured",
        }
    )

    insured_city: str = dataclasses.field(
        metadata={
            "source": "Insured_city",
            "mapping": "insured_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the insured",
        }
    )

    insured_province: str = dataclasses.field(
        metadata={
            "source": "Insured_Province",
            "mapping": "insured_province",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Province of the insured",
        }
    )

    class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class_of_business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II Class of business, listed as a text field e.g. Motor Third Party Liability",
        }
    )

    claimant: str = dataclasses.field(
        metadata={
            "source": "Claimant",
            "mapping": "claimant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant",
        }
    )

    claimant_nif: str = dataclasses.field(
        metadata={
            "source": "NIF of the Claimant",
            "mapping": "claimant_nif",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant's national ID",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy begin date",
        }
    )

    policy_end_date: str = dataclasses.field(
        metadata={
            "source": "Policy end date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy end date",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year should be the year of the Policy begin date (as no renewal)",
        }
    )

    date_complaint_received: str = dataclasses.field(
        metadata={
            "source": "Date_complaint_received",
            "mapping": "date_complaint_received",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date complaint was received",
        }
    )

    claim_status_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_start_of_the_month",
            "mapping": "claim_status_at_start_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status at the start of the month",
        }
    )

    closing_date: str = dataclasses.field(
        metadata={
            "source": "Closing_date",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing date",
        }
    )

    vehicle_registration_number: str = dataclasses.field(
        metadata={
            "source": "Car plate",
            "mapping": "vehicle_registration_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car plate",
        }
    )

    claim_type: str = dataclasses.field(
        metadata={
            "source": "Opened_claim_type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of the peril/non-peril",
        }
    )

    claim_description: str = dataclasses.field(
        metadata={
            "source": "Description of the claim",
            "mapping": "claim_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of the claim",
        }
    )

    Presence_bi: str = dataclasses.field(
        metadata={
            "source": "Presence_Bodily Injury",
            "mapping": "presence_bi",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Bodily Injury",
        }
    )

    presence_pd: str = dataclasses.field(
        metadata={
            "source": "Presence_Property Damage",
            "mapping": "presence_pd",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Property Damage (question)",
        }
    )

    claim_status_at_end_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_end_of_the_month",
            "mapping": "claim_status_at_end_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status at the end of the month",
        }
    )

    reserved_pd_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Reserved_PD_at_start_of_the_month",
            "mapping": "reserved_pd_at_start_of_the_month",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to property damages at the start of the month",
        }
    )

    reserved_bi_at_start_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Reserved_BI_at_start_of_the_month",
            "mapping": "reserved_bi_at_start_of_the_month",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to bodily injury at the start of the month ",
        }
    )

    paid_pd: str = dataclasses.field(
        metadata={
            "source": "Paid_PD",
            "mapping": "paid_pd",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount related to property damage at the start of the month",
        }
    )

    paid_bi: str = dataclasses.field(
        metadata={
            "source": "Paid_BI",
            "mapping": "paid_bi",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount related to Body injury at the start of the month",
        }
    )

    reserved_pd: str = dataclasses.field(
        metadata={
            "source": "Reserved_PD",
            "mapping": "reserved_pd",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to Personal damages during the month",
        }
    )

    reserved_bi: str = dataclasses.field(
        metadata={
            "source": "Reserved_BI",
            "mapping": "reserved_bi",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount related to Body injury during the month",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "Recovered",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovered amount",
        }
    )

    recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Recovery_reserve",
            "mapping": "recovery_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount in case a recovery opportunity is identified",
        }
    )

    claim_assessment_reserves: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_reserves",
            "mapping": "claim_assessment_reserves",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount for claim assessment costs",
        }
    )

    claim_assessment_paid: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for claim assessment costs",
        }
    )

    date_claim_amount_agreed: str = dataclasses.field(
        metadata={
            "source": "Date_Claim_Amount_Agreed",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for claim assessment costs",
        }
    )

    date_claims_paid_final: str = dataclasses.field(
        metadata={
            "source": "Date_Claims_Paid_(Final)",
            "mapping": "date_claims_paid_final",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Final date of claims payment",
        }
    )

    paid_bi_prev: str = dataclasses.field(
        metadata={
            "source": "Paid_BI_prev",
            "mapping": "paid_bi_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid amount for Bodily Injury previously",
        }
    )

    paid_pd_prev: str = dataclasses.field(
        metadata={
            "source": "Paid_PD_prev",
            "mapping": "paid_pd_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Paid amount for Property Damage previously",
        }
    )

    claim_assessment_paid_prev: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid_prev",
            "mapping": "claim_assessment_paid_prev",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the amount previously paid for claim assessment",
        }
    )

    total_incurred: str = dataclasses.field(
        metadata={
            "source": "Total_incurred",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )

    claims_referred: str = dataclasses.field(
        metadata={
            "source": "Claims_referred?",
            "mapping": "claims_referred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claims referred (question)",
        }
    )

    litigation: str = dataclasses.field(
        metadata={
            "source": "Litigation",
            "mapping": "litigation",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Litigation",
        }
    )

    next_hearing: str = dataclasses.field(
        metadata={
            "source": "Next_hearing",
            "mapping": "next_hearing",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Next hearing",
        }
    )
