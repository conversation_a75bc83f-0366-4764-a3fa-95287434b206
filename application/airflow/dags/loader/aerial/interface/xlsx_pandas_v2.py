from typing import Callable, Optional

from dateutil.parser import parse, parserinfo

from loader.core.xlsx_pandas import XLSXPandasInterface


class AerialXLSXPandasInterfaceV2(XLSXPandasInterface):
    def __init__(self, dto, header: Optional[int] = 0):
        self._header = header
        self._decimal = ","
        self._thousands = "."
        self._skipfooterint = 0
        self._skiprows = 0
        self._sheet_names = [0]
        self._dto = dto()

    @classmethod
    def date_parser(cls) -> Callable:
        day_first = parserinfo(dayfirst=False, yearfirst=False)

        return lambda x: parse(str(x), parserinfo=day_first) if str(x) != "nan" else None
