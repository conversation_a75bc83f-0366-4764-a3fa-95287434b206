from loader.aerial.dto.premium import AerialXLSXPremiumDTO
from loader.aerial.interface import AerialXLSXPandasInterface
from loader.core.xlsx_pandas import XLSXToDataFrameFactory


class AerialXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = AerialXLSXPandasInterface(dto=AerialXLSXPremiumDTO)

    @property
    def na_values(self):
        return ["NEANT", ""]
