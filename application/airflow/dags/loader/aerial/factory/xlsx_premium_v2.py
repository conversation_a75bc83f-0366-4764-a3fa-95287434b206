from loader.aerial.dto.premium import AerialXLSXPremiumDTO
from loader.aerial.interface import AerialXLSXPandasInterfaceV2
from loader.aerial.factory import AerialXLSXToDataFrameFactory


class AerialXLSXToDataFrameFactoryV2(AerialXLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = AerialXLSXPandasInterfaceV2(dto=AerialXLSXPremiumDTO)
