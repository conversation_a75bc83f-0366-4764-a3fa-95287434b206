from loader.aerial.dto.complaints import AerialXLSXComplaintsDTO
from loader.aerial.interface import AerialXLSXPandasInterface
from loader.core.xlsx_pandas import XLSXToDataFrameFactory


class AerialComplaintsXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = AerialXLSXPandasInterface(dto=AerialXLSXComplaintsDTO)
