click==8.1.7;python_version == '3.10' and sys_platform == 'linux'
black==24.3.0;python_version == '3.10' and sys_platform == 'linux'
pyflakes==2.2.0;python_version == '3.10' and sys_platform == 'linux'
mypy==1.9.0;python_version == '3.10' and sys_platform == 'linux'
pycodestyle==2.6.0;python_version == '3.10' and sys_platform == 'linux'
openpyxl==3.1.2;python_version == '3.10' and sys_platform == 'linux'
coverage==7.4.4;python_version == '3.10' and sys_platform == 'linux'
xlsxwriter==1.3.7;python_version == '3.10' and sys_platform == 'linux'
pytest==7.4.4;python_version == '3.10' and sys_platform == 'linux'
pytest-mock==3.14.0;python_version == '3.10' and sys_platform == 'linux'
responses==0.25.0;python_version == '3.10' and sys_platform == 'linux'
moto==5.0.5;python_version == '3.10' and sys_platform == 'linux'
pyarrow==14.0.2;python_version == '3.10' and sys_platform == 'linux'
pysftp<=0.2.9;python_version == '3.10' and sys_platform == 'linux'
awswrangler==3.4.1;python_version == '3.10' and sys_platform == 'linux'
mock==4.0.3;python_version == '3.10' and sys_platform == 'linux'
pytest-lazy-fixture==0.6.3;python_version == '3.10' and sys_platform == 'linux'
