import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config, pool

from alembic import context

path = os.path.join(os.path.dirname(os.path.realpath(__file__)), os.pardir, os.pardir)
sys.path.append(path)

from loader.alembic_utils.alembic_config import Base, DB_NAME, DB_METADATA_SCHEMA, DEFAULT_DB_LANDING_SCHEMA  # noqa
from loader.alembic_utils.alembic_sqlalchemy_utils import create_sql_alchemy_url, create_schema_if_not_exist  # noqa
from loader.core import models  # noqa
from loader.aerial import models  # noqa
from loader.domcura import models  # noqa
from loader.hector import models  # noqa
from loader.prima import models  # noqa
from loader.solera import models  # noqa
from loader.toni import models  # noqa
from loader.tuio import models  # noqa
from loader.gallen import models  # noqa

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python §logging.
# This line sets up loggers basically.
fileConfig(config.config_file_name)

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.
alembic_config = config.get_section(config.config_ini_section)
alembic_config["sqlalchemy.url"] = create_sql_alchemy_url()


def include_name(name, type_, parent_names):
    if type_ == "schema":
        return name in {"mga", "mgalanding"}
    else:
        return True


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = alembic_config["sqlalchemy.url"]
    context.configure(
        url=url,
        target_metadata=Base.metadata,
        literal_binds=True,
        version_table_schema=DB_METADATA_SCHEMA,
        dialect_opts={"paramstyle": "named"},
        include_schemas=True,
        include_name=include_name,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        alembic_config,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    # create schema if not exist
    create_schema_if_not_exist(connectable, DB_NAME, DEFAULT_DB_LANDING_SCHEMA)
    create_schema_if_not_exist(connectable, DB_NAME, DB_METADATA_SCHEMA)

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=Base.metadata,
            version_table_schema=DB_METADATA_SCHEMA,
            compare_type=True,
            include_schemas=True,
            include_name=include_name,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
