"""add solera_insured_objects table

Revision ID: ad49d6ebbb3a
Revises: 076856ffba52
Create Date: 2024-01-16 18:08:49.083386

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "ad49d6ebbb3a"
down_revision = "076856ffba52"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "solera_insured_objects",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("id_xo", sa.String(), nullable=True),
        sa.Column("id_polis", sa.String(), nullable=True),
        sa.Column("serial_number", sa.String(), nullable=True),
        sa.Column("license_plate", sa.String(), nullable=True),
        sa.Column("reporting_code", sa.String(), nullable=True),
        sa.Column("vehicle_brand", sa.String(), nullable=True),
        sa.Column("vehicle_model", sa.String(), nullable=True),
        sa.Column("construction_year", sa.Integer(), nullable=True),
        sa.Column("type_of_fuel", sa.String(), nullable=True),
        sa.Column("weight_in_kg", sa.String(), nullable=True),
        sa.Column("annual_mileage", sa.Integer(), nullable=True),
        sa.Column("use_description", sa.String(), nullable=True),
        sa.Column("building_object_code", sa.String(), nullable=True),
        sa.Column("wall_type_code", sa.String(), nullable=True),
        sa.Column("roof_type_code", sa.String(), nullable=True),
        sa.Column("floor_type_code", sa.String(), nullable=True),
        sa.Column("state_of_maintenance", sa.String(), nullable=True),
        sa.Column("zoning_description_code", sa.Float(), nullable=True),
        sa.Column("zoning_description", sa.String(), nullable=True),
        sa.Column("capacity_coded", sa.String(), nullable=True),
        sa.Column("capacity_description", sa.String(), nullable=True),
        sa.Column("initial_value", sa.Integer(), nullable=True),
        sa.Column("current_value", sa.Float(), nullable=True),
        sa.Column("tax_inlcuded", sa.String(), nullable=True),
        sa.Column("house_number", sa.String(), nullable=True),
        sa.Column("house_number_addition", sa.String(), nullable=True),
        sa.Column("postal_code", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("solera_insured_objects", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
