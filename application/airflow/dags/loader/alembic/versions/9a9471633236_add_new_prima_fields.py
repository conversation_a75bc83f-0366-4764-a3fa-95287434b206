"""Add new Prima fields

Revision ID: 9a9471633236
Revises: 7de86e862e3c
Create Date: 2024-02-17 22:44:46.616784

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '9a9471633236'
down_revision = '7de86e862e3c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'prima_claims',
        sa.Column('vehicle_type', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'prima_premium',
        sa.Column('form_type', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prima_premium', 'form_type', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('prima_claims', 'vehicle_type', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
