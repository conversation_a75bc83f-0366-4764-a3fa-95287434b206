"""baseline

Revision ID: 1aed8dc5d622
Revises:
Create Date: 2020-06-19 16:35:49.156850

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "1aed8dc5d622"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "crodino_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("agent_policy_number", sa.String(), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("direct_or_reinsurance", sa.String(), nullable=True),
        sa.Column("nature_of_contract", sa.String(), nullable=True),
        sa.Column("location_of_underwriting", sa.String(), nullable=True),
        sa.Column("location_of_risk", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.Integer(), nullable=True),
        sa.Column("great_lakes_policy_sequence_number", sa.String(), nullable=True),
        sa.Column("period_start_date", sa.DateTime(), nullable=True),
        sa.Column("period_end_date", sa.DateTime(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("transaction_currency", sa.String(), nullable=True),
        sa.Column("gross_premium", sa.Float(), nullable=True),
        sa.Column("ipt_rate", sa.String(), nullable=True),
        sa.Column("ipt_amount", sa.Float(), nullable=True),
        sa.Column("brokerage", sa.String(), nullable=True),
        sa.Column("agency_commission", sa.Float(), nullable=True),
        sa.Column("provisional_profit_commission", sa.Float(), nullable=True),
        sa.Column("tax_deducted", sa.String(), nullable=True),
        sa.Column("terrorism_premium", sa.String(), nullable=True),
        sa.Column("net_balance_due_to_gluk", sa.Float(), nullable=True),
        sa.Column("ipt_territory", sa.String(), nullable=True),
        sa.Column("targa", sa.String(), nullable=True),
        sa.Column("data_emissione", sa.DateTime(), nullable=True),
        sa.Column("data_incasso", sa.DateTime(), nullable=True),
        sa.Column("imposta_ssn", sa.String(), nullable=True),
        sa.Column("imposta_ssn_eur", sa.Float(), nullable=True),
        sa.Column("imposta_cvt", sa.String(), nullable=True),
        sa.Column("imposta_cvt_eur", sa.Float(), nullable=True),
        sa.Column("imposta_antiracket", sa.String(), nullable=True),
        sa.Column("imposta_antiracket_eur", sa.Float(), nullable=True),
        sa.Column("premio_totale", sa.Float(), nullable=True),
        sa.Column("provvigione", sa.String(), nullable=True),
        sa.Column("provvigione_eur", sa.Float(), nullable=True),
        sa.Column("insured_address", sa.String(), nullable=True),
        sa.Column("comune", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("cod_fiscale", sa.String(), nullable=True),
        sa.Column("descrizione_garanzia", sa.String(), nullable=True),
        sa.Column("riassicurazione_premi", sa.Float(), nullable=True),
        sa.Column("instalment", sa.Integer(), nullable=True),
        sa.Column("veicolo", sa.String(), nullable=True),
        sa.Column("tipologia_garanzia_acquistata", sa.String(), nullable=True),
        sa.Column("frequenza", sa.String(), nullable=True),
        sa.Column("parent_code", sa.String(), nullable=True),
        sa.Column("data_emissione_sost", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.create_table(
        "domcura_efh_rh",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("bauart", sa.String(), nullable=True),
        sa.Column("dach", sa.String(), nullable=True),
        sa.Column("vsnr", sa.String(), nullable=True),
        sa.Column("geburtstag", sa.DateTime(), nullable=True),
        sa.Column("anrede", sa.String(), nullable=True),
        sa.Column("vn_name1", sa.String(), nullable=True),
        sa.Column("vn_name2", sa.String(), nullable=True),
        sa.Column("vn_plz", sa.String(), nullable=True),
        sa.Column("vn_ort", sa.String(), nullable=True),
        sa.Column("vn_strasse", sa.String(), nullable=True),
        sa.Column("produktname", sa.String(), nullable=True),
        sa.Column("zahlweise", sa.String(), nullable=True),
        sa.Column("vertragsstatus", sa.String(), nullable=True),
        sa.Column("vertragsbeginn", sa.DateTime(), nullable=True),
        sa.Column("hauptfaelligkeit", sa.String(), nullable=True),
        sa.Column("akt_vertragsablauf", sa.DateTime(), nullable=True),
        sa.Column("stornogrund", sa.String(), nullable=True),
        sa.Column("selbstbehalt", sa.String(), nullable=True),
        sa.Column("jahresnetto", sa.Float(), nullable=True),
        sa.Column("jahresbrutto", sa.Float(), nullable=True),
        sa.Column("gesellschaft_name", sa.String(), nullable=True),
        sa.Column("agenturnummer", sa.String(), nullable=True),
        sa.Column("bedingungen", sa.String(), nullable=True),
        sa.Column("klauseln1", sa.String(), nullable=True),
        sa.Column("klauseln2", sa.String(), nullable=True),
        sa.Column("risiko", sa.String(), nullable=True),
        sa.Column("wohnflaeche", sa.Integer(), nullable=True),
        sa.Column("baujahr", sa.Integer(), nullable=True),
        sa.Column("gebaeudealteralterrabatt", sa.Float(), nullable=True),
        sa.Column("bauartklasse", sa.String(), nullable=True),
        sa.Column("tarifzone", sa.Integer(), nullable=True),
        sa.Column("zuers_zone", sa.Integer(), nullable=True),
        sa.Column("gefahr", sa.String(), nullable=True),
        sa.Column("gf_status", sa.String(), nullable=True),
        sa.Column("gf_einschluss", sa.DateTime(), nullable=True),
        sa.Column("gf_ausschluss", sa.DateTime(), nullable=True),
        sa.Column("gf_netto_vn", sa.Float(), nullable=True),
        sa.Column("vers_steuer", sa.Float(), nullable=True),
        sa.Column("gf_brutto_vn", sa.Float(), nullable=True),
        sa.Column("vorversicherer", sa.String(), nullable=True),
        sa.Column("vorvertragsnummer", sa.String(), nullable=True),
        sa.Column("anzahl_vorschaeden", sa.String(), nullable=True),
        sa.Column("vorschadenhoehe", sa.String(), nullable=True),
        sa.Column("nutzung", sa.String(), nullable=True),
        sa.Column("vm_nr", sa.String(), nullable=True),
        sa.Column("beginn_vu", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.create_table(
        "domcura_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("vsnr", sa.String(), nullable=True),
        sa.Column("vn_name1", sa.String(), nullable=True),
        sa.Column("vuh", sa.String(), nullable=True),
        sa.Column("gesellschaft", sa.String(), nullable=True),
        sa.Column("gesnr", sa.String(), nullable=True),
        sa.Column("agentur", sa.String(), nullable=True),
        sa.Column("produkt", sa.String(), nullable=True),
        sa.Column("sparte", sa.String(), nullable=True),
        sa.Column("produktlinie", sa.String(), nullable=True),
        sa.Column("gefahr", sa.String(), nullable=True),
        sa.Column("opnr", sa.String(), nullable=True),
        sa.Column("buchtext", sa.String(), nullable=True),
        sa.Column("buchart", sa.String(), nullable=True),
        sa.Column("von", sa.DateTime(), nullable=True),
        sa.Column("bis", sa.DateTime(), nullable=True),
        sa.Column("anteil", sa.String(), nullable=True),
        sa.Column("netto", sa.Float(), nullable=True),
        sa.Column("vst", sa.Float(), nullable=True),
        sa.Column("brutto", sa.Float(), nullable=True),
        sa.Column("courtage", sa.Float(), nullable=True),
        sa.Column("abrechnungsbetrag", sa.Float(), nullable=True),
        sa.Column("vua", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.create_table(
        "files_processed",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("bucket_name", sa.String(), nullable=True),
        sa.Column("key", sa.String(), nullable=True),
        sa.Column("file_hash", sa.String(), nullable=True),
        sa.Column(
            "processing_notification",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("processed", sa.Boolean(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("updated_on", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_by", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("files_processed", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_table("domcura_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_table("domcura_efh_rh", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_table("crodino_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
