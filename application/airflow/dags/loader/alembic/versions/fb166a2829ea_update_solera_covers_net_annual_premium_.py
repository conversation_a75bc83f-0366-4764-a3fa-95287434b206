"""update solera_covers net_annual_premium column

Revision ID: fb166a2829ea
Revises: ad49d6ebbb3a
Create Date: 2024-01-18 13:33:25.806410

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "fb166a2829ea"
down_revision = "ad49d6ebbb3a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "solera_covers",
        "net_annual_premium",
        type_=sa.Float(),
        postgresql_using="net_annual_premium::DOUBLE PRECISION",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "solera_covers",
        "net_annual_premium",
        type_=sa.String(),
        postgresql_using="net_annual_premium::VARCHAR",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
