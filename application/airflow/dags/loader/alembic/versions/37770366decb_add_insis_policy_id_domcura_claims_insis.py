"""add insis policy id domcura claims insis

Revision ID: 37770366decb
Revises: 848bda25b371
Create Date: 2023-06-09 16:28:57.569372

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "37770366decb"
down_revision = "848bda25b371"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "domcura_claims_insis",
        sa.Column("policy_id_insis", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("domcura_claims_insis", "policy_id_insis", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
