"""add Dom<PERSON>ra claims above threshold columns

Revision ID: 5876c907f5f9
Revises: 1ad388e3f1ea
Create Date: 2023-01-19 13:09:34.979586

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "5876c907f5f9"
down_revision = "1ad388e3f1ea"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "domcura_claims",
        sa.Column("insis_policy_reference", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "domcura_claims",
        sa.Column("insis_claim_reference", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "domcura_claims", sa.Column("va_claim_reference", sa.String(), nullable=True), schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("domcura_claims", "va_claim_reference", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("domcura_claims", "insis_claim_reference", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("domcura_claims", "insis_policy_reference", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
