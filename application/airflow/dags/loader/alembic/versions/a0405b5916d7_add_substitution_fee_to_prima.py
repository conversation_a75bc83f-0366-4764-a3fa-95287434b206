"""Add substitution fee to Prima

Revision ID: a0405b5916d7
Revises: cb8628e71a32
Create Date: 2024-05-16 12:51:39.370512

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'a0405b5916d7'
down_revision = 'cb8628e71a32'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'prima_premium',
        sa.Column('substitution_fee', sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prima_premium', 'substitution_fee', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
