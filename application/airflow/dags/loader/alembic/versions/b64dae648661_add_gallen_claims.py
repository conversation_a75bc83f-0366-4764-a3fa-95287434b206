"""add gallen_claims

Revision ID: b64dae648661
Revises: adfa5693b9bc
Create Date: 2024-09-17 10:26:19.062915

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'b64dae648661'
down_revision = 'adfa5693b9bc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "gallen_claims",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("claim_number", sa.String(), nullable=True),
        sa.Column("tpa_claim_number", sa.String(), nullable=True),
        sa.Column("occurrence_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("postcode_loss", sa.String(), nullable=True),
        sa.Column("city_occurence", sa.String(), nullable=True),
        sa.Column("prov_occurrence", sa.String(), nullable=True),
        sa.Column("date_notified", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("opening_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("last_review", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("date_claims_paid", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("date_of_first_request", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("insured", sa.String(), nullable=True),
        sa.Column("insured_city", sa.String(), nullable=True),
        sa.Column("insured_province", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.String(), nullable=True),
        sa.Column("cover", sa.String(), nullable=True),
        sa.Column("policy_start_date", sa.DateTime(), nullable=True),
        sa.Column("policy_end_date", sa.DateTime(), nullable=True),
        sa.Column("period_start_date", sa.DateTime(), nullable=True),
        sa.Column("period_end_date", sa.DateTime(), nullable=True),
        sa.Column("underwriting_year", sa.String(), nullable=True),
        sa.Column("date_complaint_received", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("closing_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("opened_claim_type", sa.String(), nullable=True),
        sa.Column("claim_type", sa.String(), nullable=True),
        sa.Column("claim_description", sa.String(), nullable=True),
        sa.Column("claim_status_start_of_month", sa.String(), nullable=True),
        sa.Column("claim_status_end_of_month", sa.String(), nullable=True),
        sa.Column("paid", sa.Float(), nullable=True),
        sa.Column("claim_assessment_paid", sa.Float(), nullable=True),
        sa.Column("recovered", sa.Float(), nullable=True),
        sa.Column("reserved", sa.Float(), nullable=True),
        sa.Column("claim_assessment_reserves", sa.Float(), nullable=True),
        sa.Column("recovery_reserve", sa.Float(), nullable=True),
        sa.Column("claim_assessment_paid_prev", sa.Float(), nullable=True),
        sa.Column("recovered_prev", sa.Float(), nullable=True),
        sa.Column("reserved_prev", sa.Float(), nullable=True),
        sa.Column("claim_assessment_reserves_prev", sa.Float(), nullable=True),
        sa.Column("recovery_reserve_prev", sa.Float(), nullable=True),
        sa.Column("total_incurred", sa.Float(), nullable=True),
        sa.Column("claims_referred", sa.String(), nullable=True),
        sa.Column("litigation", sa.String(), nullable=True),
        sa.Column("recovery_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("claim_denied_reason", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("gallen_claims", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
