"""add aerial claims

Revision ID: 7e5b6148dc17
Revises: 3639675ffee4
Create Date: 2023-07-07 10:40:21.893021

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "7e5b6148dc17"
down_revision = "3639675ffee4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "aerial_claims",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("claim_number", sa.String(), nullable=True),
        sa.Column("occurrence_date", sa.Date(), nullable=True),
        sa.Column("postcode_occurrence", sa.String(), nullable=True),
        sa.Column("city_occurrence", sa.String(), nullable=True),
        sa.Column("prov_occurrence", sa.String(), nullable=True),
        sa.Column("date_notified_to_aerial", sa.Date(), nullable=True),
        sa.Column("opening_date", sa.Date(), nullable=True),
        sa.Column("last_review_date", sa.Date(), nullable=True),
        sa.Column("insured", sa.String(), nullable=True),
        sa.Column("insured_city", sa.String(), nullable=True),
        sa.Column("insured_province", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.String(), nullable=True),
        sa.Column("claimant", sa.String(), nullable=True),
        sa.Column("claimant_nif", sa.String(), nullable=True),
        sa.Column("policy_start_date", sa.Date(), nullable=True),
        sa.Column("policy_end_date", sa.Date(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("date_complaint_received", sa.Date(), nullable=True),
        sa.Column("claim_status_at_start_of_the_month", sa.String(), nullable=True),
        sa.Column("closing_date", sa.Date(), nullable=True),
        sa.Column("vehicle_registration_number", sa.String(), nullable=True),
        sa.Column("claim_type", sa.String(), nullable=True),
        sa.Column("claim_description", sa.String(), nullable=True),
        sa.Column("presence_bi", sa.String(), nullable=True),
        sa.Column("presence_pd", sa.String(), nullable=True),
        sa.Column("claim_status_at_end_of_the_month", sa.String(), nullable=True),
        sa.Column("reserved_pd_at_start_of_the_month", sa.Float(), nullable=True),
        sa.Column("reserved_bi_at_start_of_the_month", sa.Float(), nullable=True),
        sa.Column("paid_pd", sa.Float(), nullable=True),
        sa.Column("paid_bi", sa.Float(), nullable=True),
        sa.Column("reserved_pd", sa.Float(), nullable=True),
        sa.Column("reserved_bi", sa.Float(), nullable=True),
        sa.Column("recovered", sa.Float(), nullable=True),
        sa.Column("recovery_reserve", sa.Float(), nullable=True),
        sa.Column("claim_assessment_reserves", sa.Float(), nullable=True),
        sa.Column("claim_assessment_paid", sa.Float(), nullable=True),
        sa.Column("date_claim_amount_agreed", sa.Float(), nullable=True),
        sa.Column("date_claims_paid_final", sa.Date(), nullable=True),
        sa.Column("paid_bi_prev", sa.Float(), nullable=True),
        sa.Column("paid_pd_prev", sa.Float(), nullable=True),
        sa.Column("claim_assessment_paid_prev", sa.Float(), nullable=True),
        sa.Column("total_incurred", sa.Float(), nullable=True),
        sa.Column("claims_referred", sa.String(), nullable=True),
        sa.Column("litigation", sa.String(), nullable=True),
        sa.Column("next_hearing", sa.Date(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("aerial_claims", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
