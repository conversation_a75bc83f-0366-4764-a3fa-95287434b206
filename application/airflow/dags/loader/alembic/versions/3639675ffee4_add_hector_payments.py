"""add hector payments

Revision ID: 3639675ffee4
Revises: f3713b7bea50
Create Date: 2023-07-06 14:56:46.751963

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "3639675ffee4"
down_revision = "f3713b7bea50"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "hector_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("company_name", sa.String(), nullable=True),
        sa.Column("name_policyholder", sa.String(), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("booking_date", sa.DATE(), nullable=True),
        sa.Column("instalment", sa.Integer(), autoincrement=False, nullable=True),
        sa.Column("payment_year", sa.Integer(), autoincrement=False, nullable=True),
        sa.Column("underwriting_year", sa.Integer(), autoincrement=False, nullable=True),
        sa.Column("payment_period_start", sa.DATE(), nullable=True),
        sa.Column("payment_period_end", sa.DATE(), nullable=True),
        sa.Column("invoice_or_credit_note", sa.String(), nullable=True),
        sa.Column("net_premium", sa.Float(), nullable=True),
        sa.Column("gross_written_premium", sa.Float(), nullable=True),
        sa.Column("commission", sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("hector_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
