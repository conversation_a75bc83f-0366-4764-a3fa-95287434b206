"""update hector_policy column types

Revision ID: 7de86e862e3c
Revises: ae5eb973c200
Create Date: 2024-02-15 11:43:05.650505

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '7de86e862e3c'
down_revision = '96b918ad75f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "hector_policies",
        "cancellation_of_previous_insurer",
        type_=sa.String(),
        postgresql_using="cancellation_of_previous_insurer::VARCHAR",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "age_of_youngest_driver",
        type_=sa.String(),
        postgresql_using="cancellation_of_previous_insurer::VARCHAR",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "age_of_policyholder",
        type_=sa.String(),
        postgresql_using="cancellation_of_previous_insurer::VARCHAR",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "vehicle_power",
        type_=sa.String(),
        postgresql_using="cancellation_of_previous_insurer::VARCHAR",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "vehicle_power_sum",
        type_=sa.String(),
        postgresql_using="cancellation_of_previous_insurer::VARCHAR",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "hector_policies",
        "cancellation_of_previous_insurer",
        type_=sa.Integer(),
        postgresql_using="cancellation_of_previous_insurer::INTEGER",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "age_of_youngest_driver",
        type_=sa.Integer(),
        postgresql_using="age_of_youngest_driver::INTEGER",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "age_of_policyholder",
        type_=sa.Integer(),
        postgresql_using="age_of_policyholder::INTEGER",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "vehicle_power",
        type_=sa.Integer(),
        postgresql_using="vehicle_power::INTEGER",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "vehicle_power_sum",
        type_=sa.Integer(),
        postgresql_using="vehicle_power_sum::INTEGER",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
