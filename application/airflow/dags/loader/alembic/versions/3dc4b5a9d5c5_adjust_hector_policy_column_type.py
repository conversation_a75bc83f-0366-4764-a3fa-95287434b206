"""adjust hector policy column type

Revision ID: 3dc4b5a9d5c5
Revises: f2bbe520096d
Create Date: 2024-11-07 09:50:00.832353

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '3dc4b5a9d5c5'
down_revision = 'f2bbe520096d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "hector_policies",
        "vehicle_sold_year",
        type_=sa.String(),
        postgresql_using="vehicle_sold_year::VARCHAR",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "hector_policies",
        "vehicle_sold_year",
        type_=sa.Integer(),
        postgresql_using="vehicle_sold_year::INTEGER",
        nullable=True,
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
