"""adding prima suspension reactivations

Revision ID: 1ab4f08f09b2
Revises: 67238ee86fdb
Create Date: 2020-11-30 12:56:03.873104

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "1ab4f08f09b2"
down_revision = "9b249baac87b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "prima_suspensions_reactivations",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("action_date", sa.DateTime(), nullable=True),
        sa.Column("effective_date", sa.DateTime(), nullable=True),
        sa.Column("expiration_date", sa.DateTime(), nullable=True),
        sa.Column("insurance_code", sa.String(), nullable=True),
        sa.Column("action_type", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("prima_suspensions_reactivations", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
