"""switching to prima premium

Revision ID: 7a1c5f4b5e20
Revises: 7b42ab2af2df
Create Date: 2020-07-09 21:54:15.883977

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "7a1c5f4b5e20"
down_revision = "7b42ab2af2df"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "prima_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("agent_policy_number", sa.String(), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("direct_or_reinsurance", sa.String(), nullable=True),
        sa.Column("nature_of_contract", sa.String(), nullable=True),
        sa.Column("location_of_underwriting", sa.String(), nullable=True),
        sa.Column("location_of_risk", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.Integer(), nullable=True),
        sa.Column("great_lakes_policy_sequence_number", sa.String(), nullable=True),
        sa.Column("period_start_date", sa.DateTime(), nullable=True),
        sa.Column("period_end_date", sa.DateTime(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("transaction_currency", sa.String(), nullable=True),
        sa.Column("gross_premium", sa.Float(), nullable=True),
        sa.Column("ipt_rate", sa.String(), nullable=True),
        sa.Column("ipt_amount", sa.Float(), nullable=True),
        sa.Column("brokerage", sa.String(), nullable=True),
        sa.Column("agency_commission", sa.Float(), nullable=True),
        sa.Column("provisional_profit_commission", sa.Float(), nullable=True),
        sa.Column("tax_deducted", sa.String(), nullable=True),
        sa.Column("terrorism_premium", sa.String(), nullable=True),
        sa.Column("net_balance_due_to_gluk", sa.Float(), nullable=True),
        sa.Column("ipt_territory", sa.String(), nullable=True),
        sa.Column("targa", sa.String(), nullable=True),
        sa.Column("data_emissione", sa.DateTime(), nullable=True),
        sa.Column("data_incasso", sa.DateTime(), nullable=True),
        sa.Column("imposta_ssn", sa.String(), nullable=True),
        sa.Column("imposta_ssn_eur", sa.Float(), nullable=True),
        sa.Column("imposta_cvt", sa.String(), nullable=True),
        sa.Column("imposta_cvt_eur", sa.Float(), nullable=True),
        sa.Column("imposta_antiracket", sa.String(), nullable=True),
        sa.Column("imposta_antiracket_eur", sa.Float(), nullable=True),
        sa.Column("premio_totale", sa.Float(), nullable=True),
        sa.Column("provvigione", sa.String(), nullable=True),
        sa.Column("provvigione_eur", sa.Float(), nullable=True),
        sa.Column("insured_address", sa.String(), nullable=True),
        sa.Column("comune", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("cod_fiscale", sa.String(), nullable=True),
        sa.Column("descrizione_garanzia", sa.String(), nullable=True),
        sa.Column("riassicurazione_premi", sa.String(), nullable=True),
        sa.Column("instalment", sa.Integer(), nullable=True),
        sa.Column("veicolo", sa.String(), nullable=True),
        sa.Column("tipologia_garanzia_acquistata", sa.String(), nullable=True),
        sa.Column("frequenza", sa.String(), nullable=True),
        sa.Column("parent_code", sa.String(), nullable=True),
        sa.Column("data_emissione_sost", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.create_table(
        "domcura_s",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("gefahr", sa.String(), nullable=True),
        sa.Column("produkt", sa.String(), nullable=True),
        sa.Column("sparte", sa.String(), nullable=True),
        sa.Column("anteil", sa.String(), nullable=True),
        sa.Column("agenturnummer", sa.String(), nullable=True),
        sa.Column("s_eingang", sa.String(), nullable=True),
        sa.Column("s_ausgang", sa.String(), nullable=True),
        sa.Column("schliessdatum", sa.String(), nullable=True),
        sa.Column("deckungsentsch", sa.String(), nullable=True),
        sa.Column("vn_name", sa.String(), nullable=True),
        sa.Column("vm_nr", sa.String(), nullable=True),
        sa.Column("vm_name", sa.String(), nullable=True),
        sa.Column("vm_typ", sa.String(), nullable=True),
        sa.Column("reg_ford", sa.Float(), nullable=True),
        sa.Column("reg_ausgleich", sa.Float(), nullable=True),
        sa.Column("kostenentsch", sa.Float(), nullable=True),
        sa.Column("entschaedigung", sa.Float(), nullable=True),
        sa.Column("mandant", sa.String(), nullable=True),
        sa.Column("reserve", sa.Float(), nullable=True),
        sa.Column("gezahlt", sa.Float(), nullable=True),
        sa.Column("gesamt", sa.Float(), nullable=True),
        sa.Column("gesamt_vu", sa.Float(), nullable=True),
        sa.Column("vn_vollname", sa.String(), nullable=True),
        sa.Column("vn_vorname", sa.String(), nullable=True),
        sa.Column("zahlungen", sa.Float(), nullable=True),
        sa.Column("reservekosten", sa.Float(), nullable=True),
        sa.Column("verursachertyp", sa.String(), nullable=True),
        sa.Column("anspruchsteller", sa.String(), nullable=True),
        sa.Column("prod_nr", sa.String(), nullable=True),
        sa.Column("produktlinie", sa.String(), nullable=True),
        sa.Column("bedingungen", sa.String(), nullable=True),
        sa.Column("vm_typ_2", sa.String(), nullable=True),
        sa.Column("gesellschaft_nr", sa.String(), nullable=True),
        sa.Column("gesellschaft_name_1", sa.String(), nullable=True),
        sa.Column("gesellschaft_name_2", sa.String(), nullable=True),
        sa.Column("id_gesellschaft", sa.String(), nullable=True),
        sa.Column("id_vertrag", sa.String(), nullable=True),
        sa.Column("id_schaden", sa.String(), nullable=True),
        sa.Column("gesellschaft_id", sa.String(), nullable=True),
        sa.Column("vers_id", sa.String(), nullable=True),
        sa.Column("teilstring_agenturnr", sa.String(), nullable=True),
        sa.Column("plz", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("prima_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_table("domcura_s", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
