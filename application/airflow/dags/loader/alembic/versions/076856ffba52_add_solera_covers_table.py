"""add solera_covers table

Revision ID: 076856ffba52
Revises: aea57dfb4a46
Create Date: 2024-01-15 19:29:01.104400

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "076856ffba52"
down_revision = "aea57dfb4a46"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "solera_covers",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("id_dekking", sa.String(), nullable=True),
        sa.Column("id_polis", sa.String(), nullable=True),
        sa.Column("cover_object_code", sa.String(), nullable=True),
        sa.Column("version_number", sa.Integer(), nullable=True),
        sa.Column("cover_code", sa.String(), nullable=True),
        sa.Column("encoded_cover_code", sa.String(), nullable=True),
        sa.Column("cover_type", sa.String(), nullable=True),
        sa.Column("cover_description", sa.String(), nullable=True),
        sa.Column("iptiq_branch", sa.String(), nullable=True),
        sa.Column("iptiq_mapping_code", sa.String(), nullable=True),
        sa.Column("insured_sum", sa.Float(), nullable=True),
        sa.Column("deductible", sa.Float(), nullable=True),
        sa.Column("net_annual_premium", sa.String(), nullable=True),
        sa.Column("premium_percentage", sa.Float(), nullable=True),
        sa.Column("bonus_malus_step", sa.Float(), nullable=True),
        sa.Column("no_claim_step", sa.Float(), nullable=True),
        sa.Column("bonus_malus_percentage", sa.Float(), nullable=True),
        sa.Column("no_claim_step_percentage", sa.String(), nullable=True),
        sa.Column("renewal_commission_percentage", sa.Float(), nullable=True),
        sa.Column("proxy_commission_percentage", sa.Float(), nullable=True),
        sa.Column("group_discount_percentage", sa.Float(), nullable=True),
        sa.Column("package_discount_percentage", sa.Float(), nullable=True),
        sa.Column("premium_per_thousand", sa.Float(), nullable=True),
        sa.Column("insured_amount_a", sa.Float(), nullable=True),
        sa.Column("insured_amount_b", sa.Float(), nullable=True),
        sa.Column("insured_number_employees", sa.Float(), nullable=True),
        sa.Column("insured_payroll", sa.Float(), nullable=True),
        sa.Column("wages_costs_percentage", sa.Float(), nullable=True),
        sa.Column("no_claim_protection", sa.String(), nullable=True),
        sa.Column("insured_sales_amount", sa.Float(), nullable=True),
        sa.Column("payment_period_1", sa.Float(), nullable=True),
        sa.Column("payment_period_2", sa.Float(), nullable=True),
        sa.Column("payment_period_3", sa.Float(), nullable=True),
        sa.Column("payment_period_4", sa.Float(), nullable=True),
        sa.Column("payment_percentage_period_1", sa.Float(), nullable=True),
        sa.Column("payment_percentage_period_2", sa.Float(), nullable=True),
        sa.Column("payment_percentage_period_3", sa.Float(), nullable=True),
        sa.Column("payment_percentage_period_4", sa.Float(), nullable=True),
        sa.Column("amount_deviation", sa.Float(), nullable=True),
        sa.Column("percentage_deviation", sa.Float(), nullable=True),
        sa.Column("step_deviation", sa.Float(), nullable=True),
        sa.Column("percentage_step_deviation", sa.Float(), nullable=True),
        sa.Column("correction_amount", sa.Float(), nullable=True),
        sa.Column("years_before_correction", sa.Float(), nullable=True),
        sa.Column("method_premium_calculation", sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("solera_covers", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
