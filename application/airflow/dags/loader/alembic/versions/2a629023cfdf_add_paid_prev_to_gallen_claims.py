"""Add paid_prev to gallen claims

Revision ID: 2a629023cfdf
Revises: 3dc4b5a9d5c5
Create Date: 2024-11-18 11:39:01.260087

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '2a629023cfdf'
down_revision = '3dc4b5a9d5c5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "gallen_claims",
        sa.Column("paid_prev", sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("gallen_claims", "paid_prev", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
