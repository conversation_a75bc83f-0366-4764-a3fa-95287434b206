"""adding source_created_on, chksum to fileprocessed

Revision ID: 7b42ab2af2df
Revises: 1aed8dc5d622
Create Date: 2020-06-19 16:36:34.716890

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "7b42ab2af2df"
down_revision = "1aed8dc5d622"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "files_processed",
        sa.Column("chksum", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "files_processed",
        sa.Column("source_created_on", sa.DateTime(timezone=True), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("files_processed", "chksum", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("files_processed", "source_created_on", schema=DEFAULT_DB_LANDING_SCHEMA)

    # ### end Alembic commands ###
