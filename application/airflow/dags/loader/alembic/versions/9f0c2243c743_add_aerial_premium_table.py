"""Add Aerial premium table

Revision ID: 9f0c2243c743
Revises: e3172bf99329
Create Date: 2022-10-04 10:24:41.572825

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "9f0c2243c743"
down_revision = "6ee3756aa858"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "aerial_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("policy_type", sa.String(), nullable=True),
        sa.Column("location_of_underwriting", sa.String(), nullable=True),
        sa.Column("location_of_risk", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.String(), nullable=True),
        sa.Column("cover", sa.String(), nullable=True),
        sa.Column("insured_value_pd", sa.Float(), nullable=True),
        sa.Column("insured_value_bi", sa.Float(), nullable=True),
        sa.Column("policy_start_date", sa.DateTime(), nullable=True),
        sa.Column("policy_end_date", sa.DateTime(), nullable=True),
        sa.Column("issue_date", sa.DateTime(), nullable=True),
        sa.Column("payment_date", sa.DateTime(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("transaction_currency", sa.String(), nullable=True),
        sa.Column("gross_written_premium", sa.Float(), nullable=True),
        sa.Column("ipt_rate", sa.Float(), nullable=True),
        sa.Column("ipt", sa.Float(), nullable=True),
        sa.Column("surcharge_tax_rate", sa.Float(), nullable=True),
        sa.Column("surcharge_tax", sa.Float(), nullable=True),
        sa.Column("clea_rate", sa.Float(), nullable=True),
        sa.Column("clea", sa.Float(), nullable=True),
        sa.Column("national_guarantee_fund_rate", sa.Float(), nullable=True),
        sa.Column("national_guarantee_fund", sa.Float(), nullable=True),
        sa.Column("ofesauto", sa.Float(), nullable=True),
        sa.Column("total_underwritten", sa.Float(), nullable=True),
        sa.Column("commission", sa.Float(), nullable=True),
        sa.Column("net_balance_due_to_iptiq", sa.Float(), nullable=True),
        sa.Column("ipt_territory", sa.String(), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("insured_nif", sa.String(), nullable=True),
        sa.Column("vehicle_owner_name", sa.String(), nullable=True),
        sa.Column("vehicle_owner_nif", sa.String(), nullable=True),
        sa.Column("main_driver_name", sa.String(), nullable=True),
        sa.Column("main_driver_nif", sa.String(), nullable=True),
        sa.Column("main_driver_birthdate", sa.Date(), nullable=True),
        sa.Column("driving_licence_number", sa.String(), nullable=True),
        sa.Column("driving_licence_date", sa.Date(), nullable=True),
        sa.Column("driving_licence_country", sa.String(), nullable=True),
        sa.Column("underwriting_questions", sa.String(), nullable=True),
        sa.Column("vehicle_registration_number", sa.String(), nullable=True),
        sa.Column("vehicle_chassis_number", sa.String(), nullable=True),
        sa.Column("vehicle_make", sa.String(), nullable=True),
        sa.Column("vehicle_model", sa.String(), nullable=True),
        sa.Column("loaded_weight", sa.Integer(), nullable=True),
        sa.Column("number_of_seats", sa.Integer(), nullable=True),
        sa.Column("registration_date", sa.Date(), nullable=True),
        sa.Column("auction_value", sa.Float(), nullable=True),
        sa.Column("actual_value", sa.Float(), nullable=True),
        sa.Column("vehicle_power", sa.Integer(), nullable=True),
        sa.Column("vehicle_type", sa.String(), nullable=True),
        sa.Column("vehicle_category", sa.String(), nullable=True),
        sa.Column("main_driving_region", sa.String(), nullable=True),
        sa.Column("street", sa.String(), nullable=True),
        sa.Column("city", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("installment", sa.String(), nullable=True),
        sa.Column("payment_frequency", sa.String(), nullable=True),
        sa.Column("bonus_malus", sa.String(), nullable=True),
        sa.Column("other_vehicle_data", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("aerial_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
