"""added cause to prima complaints

Revision ID: 8eae826a63b9
Revises: 1ab4f08f09b2
Create Date: 2020-12-08 16:25:37.457624

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "8eae826a63b9"
down_revision = "1ab4f08f09b2"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "prima_complaints",
        sa.Column("complaint_cause", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    op.drop_column(
        "prima_complaints",
        "complaint_cause",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
