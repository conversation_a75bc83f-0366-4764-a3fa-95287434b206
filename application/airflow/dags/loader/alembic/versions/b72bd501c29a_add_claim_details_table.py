"""Add claim details table

Revision ID: b72bd501c29a
Revises: a4b552209474
Create Date: 2024-03-26 19:19:48.892763

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'b72bd501c29a'
down_revision = 'a4b552209474'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'solera_claim_details',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('id_claim_detail', sa.String(), nullable=True),
        sa.Column('id_claim', sa.String(), nullable=True),
        sa.Column('transaction_type', sa.String(), nullable=True),
        sa.Column('total_incurred', sa.Float(), nullable=True),
        sa.Column('recovered', sa.Float(), nullable=True),
        sa.Column('reserved', sa.Float(), nullable=True),
        sa.Column('branch_code', sa.String(), nullable=True),
        sa.Column('branch_desc', sa.String(), nullable=True),
        sa.Column('sub_branch_code', sa.String(), nullable=True),
        sa.Column('sub_branch_desc', sa.String(), nullable=True),
        sa.Column('cover_type', sa.String(), nullable=True),
        sa.Column('cover_description', sa.String(), nullable=True),
        sa.Column('iptiq_branch_code', sa.String(), nullable=True),
        sa.Column('iptiq_mapping_code', sa.String(), nullable=True),
        sa.Column('proxy_code', sa.String(), nullable=True),
        sa.Column('pool_number', sa.String(), nullable=True),
        sa.Column('pool_share', sa.String(), nullable=True),
        sa.Column('insurer_company_code', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('solera_claim_details', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
