"""Added column canale to prima premium

Revision ID: 6ee3756aa858
Revises: 3120651e456c
Create Date: 2022-03-17 12:57:33.889401

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "6ee3756aa858"
down_revision = "3120651e456c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("prima_premium", "sale_channel", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "prima_premium",
        sa.Column("sale_channel", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
