"""new_columns_in_prima_bdx

Revision ID: 3120651e456c
Revises: b845a1e904c1
Create Date: 2022-02-16 12:44:29.600329

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "3120651e456c"
down_revision = "b845a1e904c1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "prima_premium",
        sa.Column("parent_code_renewal", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("sale_channel", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("prima_premium", "parent_code_renewal", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("prima_premium", "sale_channel", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
