"""adding prima household

Revision ID: 37cdcee5e63e
Revises: 8eae826a63b9
Create Date: 2021-03-04 03:28:56.208340

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "37cdcee5e63e"
down_revision = "8eae826a63b9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "prima_premium",
        sa.Column("type_of_building", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("use", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("square_meters", sa.Integer(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("property_city", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("property_zipcode", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("floor", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column(
            "construction_year_or_latest_significative_renew",
            sa.String(),
            nullable=True,
        ),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("coliving_adults", sa.Integer(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("coliving_minors", sa.Integer(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("past_incidents", sa.Integer(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("condominium_type", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("house_type", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    columns_to_drop = [
        "type_of_building",
        "use",
        "square_meters",
        "property_city",
        "property_zipcode",
        "floor",
        "construction_year_or_latest_significative_renew",
        "coliving_adults",
        "coliving_minors",
        "past_incidents",
        "condominium_type",
        "house_type",
    ]
    for column_name in columns_to_drop:
        op.drop_column("prima_premium", column_name, schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
