"""added the complaint table

Revision ID: 275762684ec3
Revises: 7a1c5f4b5e20
Create Date: 2020-09-14 14:01:50.860910

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "275762684ec3"
down_revision = "7a1c5f4b5e20"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "domcura_complaint",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("source_id", sa.String(), nullable=True),
        sa.Column("versicherungsschein", sa.String(), nullable=True),
        sa.Column("sparte", sa.String(), nullable=True),
        sa.Column("mandant", sa.String(), nullable=True),
        sa.Column("abteilung", sa.String(), nullable=True),
        sa.Column("zugangsweg", sa.String(), nullable=True),
        sa.Column("bestellter_vertreter", sa.String(), nullable=True),
        sa.Column("name_vn", sa.String(), nullable=True),
        sa.Column("beschwerdegrund", sa.String(), nullable=True),
        sa.Column("erlaeuterung_zum_beschwerdegrund", sa.String(), nullable=True),
        sa.Column("eingangdatum", sa.String(), nullable=True),
        sa.Column("ausgangsdatum", sa.String(), nullable=True),
        sa.Column("duaer_der_bearbeitung", sa.String(), nullable=True),
        sa.Column("bemerkung_zur_beschwerde", sa.String(), nullable=True),
        sa.Column("bearbeitungsergebnis", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("domcura_complaint", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
