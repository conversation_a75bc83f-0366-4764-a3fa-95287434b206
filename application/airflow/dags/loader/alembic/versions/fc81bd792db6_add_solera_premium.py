"""add_solera_premium

Revision ID: fc81bd792db6
Revises: 7e5b6148dc17
Create Date: 2023-07-13 15:54:37.181410

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "fc81bd792db6"
down_revision = "7e5b6148dc17"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "solera_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("id_premium", sa.String(), nullable=True),
        sa.Column("contract_id", sa.String(), nullable=True),
        sa.Column("transaction_type", sa.String(), nullable=True),
        sa.Column("booking_date", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("closed_commission_amount", sa.Float(), nullable=True),
        sa.Column("ongoing_commission_amount", sa.Float(), nullable=True),
        sa.Column("gross_written_premium", sa.Float(), nullable=True),
        sa.Column("total_incurred", sa.Float(), nullable=True),
        sa.Column("recovered", sa.Float(), nullable=True),
        sa.Column("reserved", sa.Float(), nullable=True),
        sa.Column("frequency", sa.Integer(), nullable=True),
        sa.Column("payment_period_start", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("payment_period_end", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("branch_code", sa.String(), nullable=True),
        sa.Column("branch_desc", sa.String(), nullable=True),
        sa.Column("sub_branch_code", sa.String(), nullable=True),
        sa.Column("sub_branch_desc", sa.String(), nullable=True),
        sa.Column("cover_type", sa.String(), nullable=True),
        sa.Column("cover_description", sa.String(), nullable=True),
        sa.Column("proxy_code", sa.String(), nullable=True),
        sa.Column("contract_number", sa.String(), nullable=True),
        sa.Column("underwriting_year", sa.String(), nullable=True),
        sa.Column("license_number", sa.String(), nullable=True),
        sa.Column("internal_key", sa.String(), nullable=True),
        sa.Column("identification_number", sa.String(), nullable=True),
        sa.Column("agent_claim_number", sa.String(), nullable=True),
        sa.Column("tpa_number", sa.String(), nullable=True),
        sa.Column("occurrence_date", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("direct_claim_handling", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("solera_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
