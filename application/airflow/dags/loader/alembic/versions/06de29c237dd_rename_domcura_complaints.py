"""rename domcura complaints

Revision ID: 06de29c237dd
Revises: b36f2950f461
Create Date: 2021-10-29 10:14:33.987924

"""
from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "06de29c237dd"
down_revision = "b36f2950f461"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        table_name="domcura_complaint",
        column_name="versicherungsschein",
        new_column_name="policy_claim_number",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="sparte",
        new_column_name="insured_object",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="mandant",
        new_column_name="insurer",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="abteilung",
        new_column_name="department",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="zugangsweg",
        new_column_name="channel",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="bestellter_vertreter",
        new_column_name="appointed_representative",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="name_vn",
        new_column_name="insured_name",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="beschwerdegrund",
        new_column_name="complaint_cause",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="erlaeuterung_zum_beschwerdegrund",
        new_column_name="explanation_complaint_cause",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="eingangdatum",
        new_column_name="starting_date",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="ausgangsdatum",
        new_column_name="closed_date",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="duaer_der_bearbeitung",
        new_column_name="processing_duration",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="bemerkung_zur_beschwerde",
        new_column_name="complaint_note",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaint",
        column_name="bearbeitungsergebnis",
        new_column_name="processing_result",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )

    op.rename_table(
        old_table_name="domcura_complaint", new_table_name="domcura_complaints", schema=DEFAULT_DB_LANDING_SCHEMA
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        table_name="domcura_complaints",
        column_name="policy_claim_number",
        new_column_name="versicherungsschein",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="insured_object",
        new_column_name="sparte",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="insurer",
        new_column_name="mandant",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="department",
        new_column_name="abteilung",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="channel",
        new_column_name="zugangsweg",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="appointed_representative",
        new_column_name="bestellter_vertreter",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="insured_name",
        new_column_name="name_vn",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="complaint_cause",
        new_column_name="beschwerdegrund",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="explanation_complaint_cause",
        new_column_name="erlaeuterung_zum_beschwerdegrund",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="starting_date",
        new_column_name="eingangdatum",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="closed_date",
        new_column_name="ausgangsdatum",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="processing_duration",
        new_column_name="duaer_der_bearbeitung",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="complaint_note",
        new_column_name="bemerkung_zur_beschwerde",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        table_name="domcura_complaints",
        column_name="processing_result",
        new_column_name="bearbeitungsergebnis",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.rename_table(
        old_table_name="domcura_complaints",
        new_table_name="domcura_complaint",
        schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
