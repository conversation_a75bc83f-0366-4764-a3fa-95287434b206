"""Ad<PERSON> <PERSON>laints

Revision ID: ad2a4a213ed6
Revises: bf58326fb16b
Create Date: 2023-10-24 13:07:24.125382

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'ad2a4a213ed6'
down_revision = 'bf58326fb16b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "hector_complaints",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), autoincrement=False, nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("complaint_reference", sa.String(), autoincrement=False, nullable=True),
        sa.Column("creation_year", sa.Integer(), autoincrement=False, nullable=True),
        sa.Column("complaint_code", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_handling_code", sa.String(), autoincrement=False, nullable=True),
        sa.Column("reception_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("product_type", sa.String(), autoincrement=False, nullable=True),
        sa.Column("ref_policy", sa.String(), autoincrement=False, nullable=True),
        sa.Column("sector", sa.String(), autoincrement=False, nullable=True),
        sa.Column("name_policyholder", sa.String(), autoincrement=False, nullable=True),
        sa.Column("address_policyholder", sa.String(), autoincrement=False, nullable=True),
        sa.Column("type_policyholder", sa.String(), autoincrement=False, nullable=True),
        sa.Column("geographic_area_policyholder", sa.String(), autoincrement=False, nullable=True),
        sa.Column("name_complainer", sa.String(), autoincrement=False, nullable=True),
        sa.Column("address_complainer", sa.String(), autoincrement=False, nullable=True),
        sa.Column("type_complainer", sa.String(), autoincrement=False, nullable=True),
        sa.Column("closure_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("result", sa.String(), autoincrement=False, nullable=True),
        sa.Column("judicial_authority_intervention", sa.String(), autoincrement=False, nullable=True),
        sa.Column("monetary_value", sa.String(), autoincrement=False, nullable=True),
        sa.Column("duration_till_closure", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_subject", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_cause", sa.String(), autoincrement=False, nullable=True),
        sa.Column("handled_by_ivass", sa.String(), autoincrement=False, nullable=True),
        sa.Column("ref_complaint_ivass", sa.String(), autoincrement=False, nullable=True),
        sa.Column("ref_claim", sa.String(), autoincrement=False, nullable=True),
        sa.Column("response", sa.String(), autoincrement=False, nullable=True),
        sa.Column("transferred_judicial_authority", sa.String(), autoincrement=False, nullable=True),
        sa.Column("reopened", sa.String(), autoincrement=False, nullable=True),
        sa.Column("payout_reason", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_compensation_currency", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_compensation_amount", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_redressal_payment_currency", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_redressal_payment_amount", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_root_cause", sa.String(), autoincrement=False, nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    # ### commands auto generated by Alembic ###
    op.drop_table("hector_complaints", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
