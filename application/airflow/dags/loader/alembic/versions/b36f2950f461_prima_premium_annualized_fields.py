"""Prima Premium Annualized fields

Revision ID: b36f2950f461
Revises: 23e3b9fbfef4
Create Date: 2021-08-16 11:22:09.990666

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "b36f2950f461"
down_revision = "23e3b9fbfef4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column(
        "prima_premium",
        sa.Column("valid_until", sa.DateTime(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("annual_gross_premium", sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "prima_premium",
        sa.Column("annual_ipt_amount", sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("prima_premium", "valid_until", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("prima_premium", "annual_gross_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("prima_premium", "annual_ipt_amount", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
