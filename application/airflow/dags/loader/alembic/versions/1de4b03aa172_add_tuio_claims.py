"""add tuio claims

Revision ID: 1de4b03aa172
Revises: 389c9c74fa1a
Create Date: 2023-12-05 13:42:23.742695

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '1de4b03aa172'
down_revision = '389c9c74fa1a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.create_table(
        'tuio_claims',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('policy_reference', sa.String(), nullable=True),
        sa.Column('claim_reference', sa.String(), nullable=True),
        sa.Column('occurrence_date', sa.DateTime(), nullable=True),
        sa.Column('occurrence_postcode', sa.String(), nullable=True),
        sa.Column('occurrence_city', sa.String(), nullable=True),
        sa.Column('occurrence_province', sa.String(), nullable=True),
        sa.Column('insured_postcode', sa.String(), nullable=True),
        sa.Column('notification_date', sa.DateTime(), nullable=True),
        sa.Column('opening_date', sa.DateTime(), nullable=True),
        sa.Column('last_review', sa.DateTime(), nullable=True),
        sa.Column('claim_amount_agreed_date', sa.DateTime(), nullable=True),
        sa.Column('claim_paid_date', sa.DateTime(), nullable=True),
        sa.Column('first_request_date', sa.DateTime(), nullable=True),
        sa.Column('insured', sa.String(), nullable=True),
        sa.Column('insured_city', sa.String(), nullable=True),
        sa.Column('insured_province', sa.String(), nullable=True),
        sa.Column('class_of_business', sa.String(), nullable=True),
        sa.Column('coverage', sa.String(), nullable=True),
        sa.Column('claimant', sa.String(), nullable=True),
        sa.Column('claimant_documentation_number', sa.String(), nullable=True),
        sa.Column('policy_start_date', sa.DateTime(), nullable=True),
        sa.Column('policy_end_date', sa.DateTime(), nullable=True),
        sa.Column('period_start_date', sa.DateTime(), nullable=True),
        sa.Column('period_end_date', sa.DateTime(), nullable=True),
        sa.Column('underwriting_year', sa.Integer(), nullable=True),
        sa.Column('complaint_received_date', sa.DateTime(), nullable=True),
        sa.Column('claim_closing_date', sa.DateTime(), nullable=True),
        sa.Column('opened_claim_type', sa.String(), nullable=True),
        sa.Column('claim_type', sa.String(), nullable=True),
        sa.Column('claim_description', sa.String(), nullable=True),
        sa.Column('previous_claim_status', sa.String(), nullable=True),
        sa.Column('claim_status', sa.String(), nullable=True),
        sa.Column('paid', sa.Float(), nullable=True),
        sa.Column('claim_assessment_paid', sa.Float(), nullable=True),
        sa.Column('recovered', sa.Float(), nullable=True),
        sa.Column('reserve', sa.Float(), nullable=True),
        sa.Column('claim_assessment_reserve', sa.Float(), nullable=True),
        sa.Column('recovery_reserve', sa.Float(), nullable=True),
        sa.Column('total_incurred', sa.String(), nullable=True),
        sa.Column('claim_referred', sa.String(), nullable=True),
        sa.Column('litigation', sa.String(), nullable=True),
        sa.Column('recovery_date', sa.DateTime(), nullable=True),
        sa.Column('denial_reason', sa.String(), nullable=True),
        sa.Column('deductible', sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tuio_claims', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
