"""add columns to solera_policies

Revision ID: f6816bd55901
Revises: 1de4b03aa172
Create Date: 2024-01-11 22:42:15.653048

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'f6816bd55901'
down_revision = '1de4b03aa172'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "solera_policies",
        sa.Column("underwriting_year_pool", sa.Integer(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "solera_policies",
        sa.Column("contract_package_number", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "solera_policies",
        sa.Column("contract_blanket_number", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "solera_policies",
        sa.Column("identification_number", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "solera_policies",
        sa.Column("entity_type", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "solera_policies",
        sa.Column("policyholder_occupation_code", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "solera_policies",
        sa.Column("main_driver_birthdate", sa.Date(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "solera_policies",
        sa.Column("nature_of_business_code", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("solera_policies", "underwriting_year_pool", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "contract_package_number", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "contract_blanket_number", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "identification_number", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "entity_type", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "policyholder_occupation_code", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "main_driver_birthdate", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("solera_policies", "nature_of_business_code", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
