"""add claim_id column to gallen_sanctions

Revision ID: f8790f23f07b
Revises: 04c56d37f972
Create Date: 2025-06-12 10:18:04.886102

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'f8790f23f07b'
down_revision = '04c56d37f972'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "gallen_sanctions",
        sa.Column("claim_id", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("gallen_sanctions", "claim_id", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
