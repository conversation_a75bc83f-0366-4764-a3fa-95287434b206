"""add solera policies

Revision ID: 17e0747c9a50
Revises: 37770366decb
Create Date: 2023-06-26 17:34:11.332573

"""
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "17e0747c9a50"
down_revision = "37770366decb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic ###
    op.create_table(
        "solera_policies",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("id_polis", sa.String(), nullable=True),
        sa.Column("contract_id", sa.String(), nullable=True),
        sa.Column("contract_number", sa.String(), nullable=True),
        sa.Column("internal_key", sa.String(), nullable=True),
        sa.Column("branch_code", sa.String(), nullable=True),
        sa.Column("branch_desc", sa.String(), nullable=True),
        sa.Column("sub_branch_code", sa.String(), nullable=True),
        sa.Column("sub_branch_desc", sa.String(), nullable=True),
        sa.Column("proxy_code", sa.String(), autoincrement=False, nullable=True),
        sa.Column("policy_start_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("policy_duration", sa.String(), nullable=True),
        sa.Column("period_end_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("frequency", sa.String(), nullable=True),
        sa.Column("date_last_modified", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("policy_end_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("policy_status", sa.String(), nullable=True),
        sa.Column("premium_yearly_net", sa.Float(), nullable=True),
        sa.Column("is_part_of_package", sa.String(), nullable=True),
        sa.Column("is_coinsured", sa.String(), nullable=True),
        sa.Column("is_part_of_distribution", sa.String(), nullable=True),
        sa.Column("nature_of_contract_code", sa.String(), nullable=True),
        sa.Column("nature_of_contract", sa.String(), nullable=True),
        sa.Column("contract_version_number", sa.String(), nullable=True),
        sa.Column("company_code", sa.String(), nullable=True),
        sa.Column("n_of_claim_free_years", sa.String(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), autoincrement=False, nullable=True),
        sa.Column("license_number", sa.String(), nullable=True),
        sa.Column("gender", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("date_of_birth", sa.DATE(), nullable=True),
        sa.Column("policyholder_occupation", sa.String(), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("employer_number", sa.String(), nullable=True),
        sa.Column("cea_code", sa.String(), nullable=True),
        sa.Column("secondary_sbi_code", sa.String(), nullable=True),
        sa.Column("sbi_code", sa.String(), nullable=True),
        sa.Column("business_nature", sa.String(), nullable=True),
        sa.Column("bik_code", sa.String(), nullable=True),
        sa.Column("side_activities", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic ###
    op.drop_table("solera_policies", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
