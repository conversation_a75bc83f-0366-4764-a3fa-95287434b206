"""create table prima complaints jira

Revision ID: f6636b96c3e9
Revises: cb8628e71a32
Create Date: 2024-05-31 12:23:17.842783

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'f6636b96c3e9'
down_revision = '2067209d9b06'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'prima_complaints_jira',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('jira_ticket', sa.String(), nullable=True),
        sa.Column('lob', sa.String(), nullable=True),
        sa.Column('subject', sa.String(), nullable=True),
        sa.Column('progressive_number', sa.String(), nullable=True),
        sa.Column('complainer_name', sa.String(), nullable=True),
        sa.Column('complainer_geographic_area', sa.String(), nullable=True),
        sa.Column('complainer_address', sa.String(), nullable=True),
        sa.Column('complainer_type', sa.String(), nullable=True),
        sa.Column('proponent_name', sa.String(), nullable=True),
        sa.Column('proponent_address', sa.String(), nullable=True),
        sa.Column('proponent_type', sa.String(), nullable=True),
        sa.Column('reception_date', sa.String(), nullable=True),
        sa.Column('closure_date', sa.String(), nullable=True),
        sa.Column('company_area', sa.String(), nullable=True),
        sa.Column('sector', sa.String(), nullable=True),
        sa.Column('monetary_value', sa.String(), nullable=True),
        sa.Column('handled_by_ivass', sa.String(), nullable=True),
        sa.Column('external_protocol_number', sa.String(), nullable=True),
        sa.Column('product_type', sa.String(), nullable=True),
        sa.Column('transferred_judicial_authority', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('updated_at', sa.String(), nullable=True),
        sa.Column('author', sa.String(), nullable=True),
        sa.Column('assignee', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    op.drop_table('prima_complaints_jira', schema=DEFAULT_DB_LANDING_SCHEMA)
