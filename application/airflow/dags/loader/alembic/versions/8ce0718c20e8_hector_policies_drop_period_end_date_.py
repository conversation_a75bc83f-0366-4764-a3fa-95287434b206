"""hector policies drop period end date column

Revision ID: 8ce0718c20e8
Revises: 2f26e0bf4d1e
Create Date: 2023-10-17 16:38:00.251137

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '8ce0718c20e8'
down_revision = '2f26e0bf4d1e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("hector_policies", "period_end_date", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.alter_column("hector_policies", "insured_value_others", type_=sa.String(), schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hector_policies",
        sa.Column("period_end_date", sa.DATE(), autoincrement=False, nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "insured_value_others",
        type_=sa.Float(),
        postgresql_using="NULL",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
