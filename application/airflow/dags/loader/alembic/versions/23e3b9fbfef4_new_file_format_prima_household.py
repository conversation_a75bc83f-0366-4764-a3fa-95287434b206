"""new file format prima household

Revision ID: 23e3b9fbfef4
Revises: df5286780847
Create Date: 2021-05-17 11:50:14.111014

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "23e3b9fbfef4"
down_revision = "df5286780847"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    columns_to_add = [
        sa.Column("limit_value", sa.String(), nullable=True),
        sa.Column("deductible_value", sa.String(), nullable=True),
        sa.Column("source", sa.String(), nullable=True),
        sa.Column("discount_10pct", sa.String(), nullable=True),
        sa.Column("product_type", sa.String(), nullable=True),
    ]
    for column_to_add in columns_to_add:
        op.add_column(
            "prima_premium",
            column_to_add,
            schema=DEFAULT_DB_LANDING_SCHEMA,
        )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    columns_to_drop = [
        "limit_value",
        "deductible_value",
        "source",
        "discount_10pct",
        "product_type",
    ]
    for col_name in columns_to_drop:
        op.drop_column("prima_premium", col_name, schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
