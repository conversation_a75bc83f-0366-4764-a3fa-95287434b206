"""Remove crodino_premium

Revision ID: 0b4e1853d175
Revises: c7dfa5d62c7b
Create Date: 2023-02-13 17:26:49.235238

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "0b4e1853d175"
down_revision = "c7dfa5d62c7b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("crodino_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "crodino_premium",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("agent_policy_number", sa.String(), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("direct_or_reinsurance", sa.String(), nullable=True),
        sa.Column("nature_of_contract", sa.String(), nullable=True),
        sa.Column("location_of_underwriting", sa.String(), nullable=True),
        sa.Column("location_of_risk", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.Integer(), nullable=True),
        sa.Column("great_lakes_policy_sequence_number", sa.String(), nullable=True),
        sa.Column("period_start_date", sa.DateTime(), nullable=True),
        sa.Column("period_end_date", sa.DateTime(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("transaction_currency", sa.String(), nullable=True),
        sa.Column("gross_premium", sa.Float(), nullable=True),
        sa.Column("ipt_rate", sa.String(), nullable=True),
        sa.Column("ipt_amount", sa.Float(), nullable=True),
        sa.Column("brokerage", sa.String(), nullable=True),
        sa.Column("agency_commission", sa.Float(), nullable=True),
        sa.Column("provisional_profit_commission", sa.Float(), nullable=True),
        sa.Column("tax_deducted", sa.String(), nullable=True),
        sa.Column("terrorism_premium", sa.String(), nullable=True),
        sa.Column("net_balance_due_to_gluk", sa.Float(), nullable=True),
        sa.Column("ipt_territory", sa.String(), nullable=True),
        sa.Column("targa", sa.String(), nullable=True),
        sa.Column("data_emissione", sa.DateTime(), nullable=True),
        sa.Column("data_incasso", sa.DateTime(), nullable=True),
        sa.Column("imposta_ssn", sa.String(), nullable=True),
        sa.Column("imposta_ssn_eur", sa.Float(), nullable=True),
        sa.Column("imposta_cvt", sa.String(), nullable=True),
        sa.Column("imposta_cvt_eur", sa.Float(), nullable=True),
        sa.Column("imposta_antiracket", sa.String(), nullable=True),
        sa.Column("imposta_antiracket_eur", sa.Float(), nullable=True),
        sa.Column("premio_totale", sa.Float(), nullable=True),
        sa.Column("provvigione", sa.String(), nullable=True),
        sa.Column("provvigione_eur", sa.Float(), nullable=True),
        sa.Column("insured_address", sa.String(), nullable=True),
        sa.Column("comune", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("cod_fiscale", sa.String(), nullable=True),
        sa.Column("descrizione_garanzia", sa.String(), nullable=True),
        sa.Column("riassicurazione_premi", sa.Float(), nullable=True),
        sa.Column("instalment", sa.Integer(), nullable=True),
        sa.Column("veicolo", sa.String(), nullable=True),
        sa.Column("tipologia_garanzia_acquistata", sa.String(), nullable=True),
        sa.Column("frequenza", sa.String(), nullable=True),
        sa.Column("parent_code", sa.String(), nullable=True),
        sa.Column("data_emissione_sost", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
