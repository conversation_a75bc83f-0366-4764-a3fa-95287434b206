"""add reserved prev to toni claims

Revision ID: 5f45972292d3
Revises: cd8beb1eb3d1
Create Date: 2024-04-25 14:04:28.263355

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '5f45972292d3'
down_revision = 'cd8beb1eb3d1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('toni_claims', sa.Column('reserved_prev', sa.Float(), nullable=True),
                  schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('toni_claims', 'reserved_prev', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
