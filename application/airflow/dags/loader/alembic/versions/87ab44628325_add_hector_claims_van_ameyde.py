"""add hector_claims_van_ameyde

Revision ID: 87ab44628325
Revises: 5f45972292d3
Create Date: 2024-05-01 16:58:48.775302

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '87ab44628325'
down_revision = '5f45972292d3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'hector_claims_van_ameyde',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('underwriting_year', sa.Integer(), nullable=True),
        sa.Column('policy_type', sa.String(), nullable=True),
        sa.Column('claim_type', sa.String(), nullable=True),
        sa.Column('claim_id_hector', sa.String(), nullable=True),
        sa.Column('claim_id_van_ameyde', sa.String(), nullable=True),
        sa.Column('date_of_loss', sa.Date(), nullable=True),
        sa.Column('date_of_notification', sa.Date(), nullable=True),
        sa.Column('country_of_event', sa.String(), nullable=True),
        sa.Column('cause_type', sa.String(), nullable=True),
        sa.Column('policy_identifier', sa.String(), nullable=True),
        sa.Column('claim_origin', sa.String(), nullable=True),
        sa.Column('calamity', sa.String(), nullable=True),
        sa.Column('event_summary', sa.String(), nullable=True),
        sa.Column('policy_holder', sa.String(), nullable=True),
        sa.Column('risk_type', sa.String(), nullable=True),
        sa.Column('accident_code_type', sa.String(), nullable=True),
        sa.Column('city', sa.String(), nullable=True),
        sa.Column('street', sa.String(), nullable=True),
        sa.Column('house_number', sa.String(), nullable=True),
        sa.Column('post_code', sa.String(), nullable=True),
        sa.Column('country', sa.String(), nullable=True),
        sa.Column('liability', sa.String(), nullable=True),
        sa.Column('claim_status', sa.String(), nullable=True),
        sa.Column('date_of_closure', sa.Date(), nullable=True),
        sa.Column('date_of_last_review', sa.Date(), nullable=True),
        sa.Column('policy_excess', sa.Float(), nullable=True),
        sa.Column('reserve_pd', sa.Float(), nullable=True),
        sa.Column('reserve_bi', sa.Float(), nullable=True),
        sa.Column('reserve_external', sa.Float(), nullable=True),
        sa.Column('reserve_other', sa.Float(), nullable=True),
        sa.Column('paid_pd', sa.Float(), nullable=True),
        sa.Column('paid_bi', sa.Float(), nullable=True),
        sa.Column('paid_external', sa.Float(), nullable=True),
        sa.Column('paid_other', sa.Float(), nullable=True),
        sa.Column('recovery_reserve', sa.Float(), nullable=True),
        sa.Column('recovered', sa.Float(), nullable=True),
        sa.Column('deductible_reserve', sa.Float(), nullable=True),
        sa.Column('deducted', sa.Float(), nullable=True),
        sa.Column('total_paid', sa.Float(), nullable=True),
        sa.Column('total_reserve', sa.Float(), nullable=True),
        sa.Column('total_cost', sa.Float(), nullable=True),
        sa.Column('claim_handling_fee', sa.Float(), nullable=True),
        sa.Column('currency', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('hector_claims_van_ameyde', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
