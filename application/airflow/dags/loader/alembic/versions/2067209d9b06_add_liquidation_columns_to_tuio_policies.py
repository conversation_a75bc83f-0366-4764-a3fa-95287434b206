"""add liquidation columns to tuio policies

Revision ID: 2067209d9b06
Revises: a0405b5916d7
Create Date: 2024-05-27 14:20:39.380957

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '2067209d9b06'
down_revision = 'a0405b5916d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'tuio_policies', sa.Column('positive_liquidation', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_policies', sa.Column('negative_liquidation', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tuio_policies', 'negative_liquidation', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_policies', 'positive_liquidation', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
