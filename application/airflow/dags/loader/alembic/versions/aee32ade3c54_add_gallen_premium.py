"""add gallen_premium

Revision ID: aee32ade3c54
Revises: b72bd501c29a
Create Date: 2024-04-04 09:59:43.604899

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'aee32ade3c54'
down_revision = 'b72bd501c29a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'gallen_premium',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('policy_number', sa.String(), nullable=True),
        sa.Column('invoice_id', sa.String(), nullable=True),
        sa.Column('policy_status', sa.String(), nullable=True),
        sa.Column('policy_type', sa.String(), nullable=True),
        sa.Column('insured_risk', sa.String(), nullable=True),
        sa.Column('rental_period', sa.Integer(), nullable=True),
        sa.Column('cover_water', sa.String(), nullable=True),
        sa.Column('cover_lights', sa.String(), nullable=True),
        sa.Column('cover_gas', sa.String(), nullable=True),
        sa.Column('cover_lock', sa.String(), nullable=True),
        sa.Column('cover_community_fee', sa.String(), nullable=True),
        sa.Column('rental_cover', sa.Float(), nullable=True),
        sa.Column('policy_start_date', sa.Date(), autoincrement=False, nullable=True),
        sa.Column('policy_end_date', sa.Date(), autoincrement=False, nullable=True),
        sa.Column('policy_cancellation_date', sa.Date(), autoincrement=False, nullable=True),
        sa.Column('ph_tax_id', sa.String(), nullable=True),
        sa.Column('ph_name', sa.String(), nullable=True),
        sa.Column('ph_last_name', sa.String(), nullable=True),
        sa.Column('premium_with_taxes', sa.Float(), nullable=True),
        sa.Column('consorcio', sa.Float(), nullable=True),
        sa.Column('ipt_amount', sa.Float(), nullable=True),
        sa.Column('premium_without_taxes', sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("gallen_premium", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
