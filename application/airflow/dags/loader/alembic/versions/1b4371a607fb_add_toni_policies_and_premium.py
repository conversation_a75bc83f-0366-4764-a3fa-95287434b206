"""add toni policies and premium

Revision ID: 1b4371a607fb
Revises: 33da4e4fe1e6
Create Date: 2024-03-04 17:18:33.461821

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '1b4371a607fb'
down_revision = '33da4e4fe1e6'
branch_labels = None
depends_on = None


def upgrade():

    op.create_table(
        'toni_policies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('policy_number', sa.String(), nullable=True),
        sa.Column('nature_of_contract', sa.String(), nullable=True),
        sa.Column('location_of_underwriting', sa.String(), nullable=True),
        sa.Column('location_of_risk', sa.String(), nullable=True),
        sa.Column('class_of_business', sa.String(), nullable=True),
        sa.Column('cover', sa.String(), nullable=True),
        sa.Column('cover_type', sa.String(), nullable=True),
        sa.Column('bundle', sa.String(), nullable=True),
        sa.Column('deductible_amount', sa.Float(), nullable=True),
        sa.Column('insured_value', sa.String(), nullable=True),
        sa.Column('policy_start_date', sa.DateTime(), nullable=True),
        sa.Column('policy_end_date', sa.DateTime(), nullable=True),
        sa.Column('period_start_date', sa.DateTime(), nullable=True),
        sa.Column('period_end_date', sa.DateTime(), nullable=True),
        sa.Column('issuance_date', sa.DateTime(), nullable=True),
        sa.Column('payment_date', sa.DateTime(), nullable=True),
        sa.Column('underwriting_year', sa.Integer(), nullable=True),
        sa.Column('transaction_currency', sa.String(), nullable=True),
        sa.Column('premium_without_taxes', sa.Float(), nullable=True),
        sa.Column('ipt_rate', sa.String(), nullable=True),
        sa.Column('ipt_amount', sa.Float(), nullable=True),
        sa.Column('swiss_accident_prevention_tax', sa.Float(), nullable=True),
        sa.Column('swiss_ngf', sa.Float(), nullable=True),
        sa.Column('swiss_nbi', sa.Float(), nullable=True),
        sa.Column('premium_with_taxes', sa.Float(), nullable=True),
        sa.Column('commission_mga', sa.Float(), nullable=True),
        sa.Column('commission_distributor', sa.Float(), nullable=True),
        sa.Column('installment_fee', sa.Float(), nullable=True),
        sa.Column('net_balance_due_iptiq', sa.Float(), nullable=True),
        sa.Column('annual_premium_without_taxes', sa.Float(), nullable=True),
        sa.Column('annual_tax_amount', sa.Float(), nullable=True),
        sa.Column('ph_country', sa.String(), nullable=True),
        sa.Column('ph_canton', sa.String(), nullable=True),
        sa.Column('ph_first_name', sa.String(), nullable=True),
        sa.Column('ph_last_name', sa.String(), nullable=True),
        sa.Column('ph_dob', sa.Date(), nullable=True),
        sa.Column('ph_address_street', sa.String(), nullable=True),
        sa.Column('ph_address_number', sa.String(), nullable=True),
        sa.Column('ph_address_postcode', sa.String(), nullable=True),
        sa.Column('ph_address_city', sa.String(), nullable=True),
        sa.Column('ph_nationality', sa.String(), nullable=True),
        sa.Column('ph_residency_status', sa.String(), nullable=True),
        sa.Column('vehicle_owner_first_name', sa.String(), nullable=True),
        sa.Column('vehicle_owner_last_name', sa.String(), nullable=True),
        sa.Column('vehicle_user_first_name', sa.String(), nullable=True),
        sa.Column('vehicle_user_last_name', sa.String(), nullable=True),
        sa.Column('vehicle_user_dob', sa.Date(), nullable=True),
        sa.Column('vehicle_user_gender', sa.String(), nullable=True),
        sa.Column('vehicle_user_address_street', sa.String(), nullable=True),
        sa.Column('vehicle_user_address_number', sa.String(), nullable=True),
        sa.Column('vehicle_user_address_postcode', sa.String(), nullable=True),
        sa.Column('vehicle_user_address_city', sa.String(), nullable=True),
        sa.Column('vehicle_user_address_country', sa.String(), nullable=True),
        sa.Column('driving_licence_date', sa.Date(), nullable=True),
        sa.Column('uw_claims_mtpl', sa.String(), nullable=True),
        sa.Column('uw_claims_casco', sa.String(), nullable=True),
        sa.Column('licence_plate_complete', sa.String(), nullable=True),
        sa.Column('licence_plate_canton', sa.String(), nullable=True),
        sa.Column('vehicle_brand', sa.String(), nullable=True),
        sa.Column('vehicle_model', sa.String(), nullable=True),
        sa.Column('vehicle_registration_date', sa.Date(), nullable=True),
        sa.Column('vehicle_catalogue_value', sa.String(), nullable=True),
        sa.Column('vehicle_accessories_value', sa.String(), nullable=True),
        sa.Column('vehicle_horse_power', sa.String(), nullable=True),
        sa.Column('vehicle_type_class', sa.String(), nullable=True),
        sa.Column('vehicle_ccm', sa.String(), nullable=True),
        sa.Column('vehicle_weight', sa.String(), nullable=True),
        sa.Column('vehicle_fuel', sa.String(), nullable=True),
        sa.Column('vehicle_category', sa.String(), nullable=True),
        sa.Column('vehicle_yearly_mileage', sa.String(), nullable=True),
        sa.Column('vehicle_intended_use', sa.String(), nullable=True),
        sa.Column('is_vehicle_leased', sa.String(), nullable=True),
        sa.Column('vehicle_num_young_drivers', sa.String(), nullable=True),
        sa.Column('payment_installment', sa.String(), nullable=True),
        sa.Column('payment_invoice_id', sa.String(), nullable=True),
        sa.Column('payment_frequency', sa.String(), nullable=True),
        sa.Column('distribution_channel', sa.String(), nullable=True),
        sa.Column('distribution_channel_1', sa.String(), nullable=True),
        sa.Column('distribution_channel_2', sa.String(), nullable=True),
        sa.Column('distribution_channel_3', sa.String(), nullable=True),
        sa.Column('bonus_malus_main_driver', sa.String(), nullable=True),
        sa.Column('cancellation_reason', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():

    op.drop_table('toni_policies', schema=DEFAULT_DB_LANDING_SCHEMA)
