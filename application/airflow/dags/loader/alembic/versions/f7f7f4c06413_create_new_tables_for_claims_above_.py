"""Create new tables for claims above threshold

Revision ID: f7f7f4c06413
Revises: 0b4e1853d175
Create Date: 2023-03-16 10:10:53.649641

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "f7f7f4c06413"
down_revision = "0b4e1853d175"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "domcura_claims_insis",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_id_domcura", sa.String(), nullable=True),
        sa.Column("claim_id_insis", sa.String(), nullable=True),
        sa.Column("claim_id_van_ameyde", sa.String(), nullable=True),
        sa.Column("claim_id_domcura", sa.String(), nullable=True),
        sa.Column("policy_holder_name", sa.String(), nullable=True),
        sa.Column("risk_address", sa.String(), nullable=True),
        sa.Column("claim_notification_date", sa.String(), nullable=True),
        sa.Column("national_catastrophe", sa.String(), nullable=True),
        sa.Column("claim_registration_date", sa.String(), nullable=True),
        sa.Column("claim_registration_year", sa.String(), nullable=True),
        sa.Column("claim_status", sa.String(), nullable=True),
        sa.Column("claim_type", sa.String(), nullable=True),
        sa.Column("cause_type", sa.String(), nullable=True),
        sa.Column("cover", sa.String(), nullable=True),
        sa.Column("cost_reserve", sa.String(), nullable=True),
        sa.Column("indemnity_reserve", sa.String(), nullable=True),
        sa.Column("paid_costs", sa.String(), nullable=True),
        sa.Column("paid_indemnity", sa.String(), nullable=True),
        sa.Column("incurred", sa.String(), nullable=True),
        sa.Column("recourse_received", sa.String(), nullable=True),
        sa.Column("payments_before_handover", sa.String(), nullable=True),
        sa.Column("comments", sa.String(), nullable=True),
        sa.Column("insis_notes", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.create_table(
        "domcura_claims_van_ameyde",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("policy_type", sa.String(), nullable=True),
        sa.Column("claim_type", sa.String(), nullable=True),
        sa.Column("claim_id_domcura", sa.String(), nullable=True),
        sa.Column("claim_id_van_ameyde", sa.String(), nullable=True),
        sa.Column("date_of_loss", sa.Date(), nullable=True),
        sa.Column("date_of_notification", sa.Date(), nullable=True),
        sa.Column("country_of_event", sa.String(), nullable=True),
        sa.Column("cause_type", sa.String(), nullable=True),
        sa.Column("policy_identifier", sa.String(), nullable=True),
        sa.Column("contact_type", sa.String(), nullable=True),
        sa.Column("calamity", sa.String(), nullable=True),
        sa.Column("event_summary", sa.String(), nullable=True),
        sa.Column("policy_holder", sa.String(), nullable=True),
        sa.Column("insured_object", sa.String(), nullable=True),
        sa.Column("accident_code_type", sa.String(), nullable=True),
        sa.Column("city", sa.String(), nullable=True),
        sa.Column("street", sa.String(), nullable=True),
        sa.Column("house_number", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("country", sa.String(), nullable=True),
        sa.Column("liability", sa.String(), nullable=True),
        sa.Column("claim_status", sa.String(), nullable=True),
        sa.Column("date_of_closure", sa.Date(), nullable=True),
        sa.Column("date_of_last_review", sa.Date(), nullable=True),
        sa.Column("policy_excess", sa.Float(), nullable=True),
        sa.Column("reserve_pd", sa.Float(), nullable=True),
        sa.Column("reserve_bi", sa.Float(), nullable=True),
        sa.Column("reserve_external", sa.Float(), nullable=True),
        sa.Column("reserve_other", sa.Float(), nullable=True),
        sa.Column("paid_pd", sa.Float(), nullable=True),
        sa.Column("paid_bi", sa.Float(), nullable=True),
        sa.Column("paid_external", sa.Float(), nullable=True),
        sa.Column("paid_other", sa.Float(), nullable=True),
        sa.Column("recovery_reserve", sa.Float(), nullable=True),
        sa.Column("recovered", sa.Float(), nullable=True),
        sa.Column("deductible_reserve", sa.Float(), nullable=True),
        sa.Column("deducted", sa.Float(), nullable=True),
        sa.Column("total_paid", sa.Float(), nullable=True),
        sa.Column("total_reserve", sa.Float(), nullable=True),
        sa.Column("total_cost", sa.Float(), nullable=True),
        sa.Column("claim_handling_fee", sa.Float(), nullable=True),
        sa.Column("currency", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.drop_column("domcura_claims", "va_claim_reference", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("domcura_claims", "insis_claim_reference", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("domcura_claims", "insis_policy_reference", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column(
        "domcura_claims",
        sa.Column("insis_policy_reference", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "domcura_claims",
        sa.Column("insis_claim_reference", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "domcura_claims", sa.Column("va_claim_reference", sa.String(), nullable=True), schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.drop_table("domcura_claims_van_ameyde", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_table("domcura_claims_insis", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
