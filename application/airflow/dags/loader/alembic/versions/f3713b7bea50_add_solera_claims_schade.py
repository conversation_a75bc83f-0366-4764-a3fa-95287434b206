"""add_solera_claims_schade

Revision ID: f3713b7bea50
Revises: 12c90edc1932
Create Date: 2023-06-29 16:07:59.595361

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "f3713b7bea50"
down_revision = "12c90edc1932"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "solera_claims",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("id_claim", sa.String(), nullable=True),
        sa.Column("contract_id", sa.String(), nullable=True),
        sa.Column("agent_claim_number", sa.String(), nullable=True),
        sa.Column("tpa_number", sa.String(), nullable=True),
        sa.Column("occurrence_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("date_notified_to_solera", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("debt_damage", sa.String(), nullable=True),
        sa.Column("claim_description", sa.String(), nullable=True),
        sa.Column("closing_date", sa.DATE(), nullable=True),
        sa.Column("claim_status", sa.String(), nullable=True),
        sa.Column("direct_claim_handling", sa.String(), nullable=True),
        sa.Column("contract_number", sa.String(), nullable=True),
        sa.Column("internal_key", sa.String(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), autoincrement=False, nullable=True),
        sa.Column("license_number", sa.String(), nullable=True),
        sa.Column("identification_number", sa.String(), nullable=True),
        sa.Column("adn_damage_code", sa.String(), nullable=True),
        sa.Column("cis_damage_code", sa.String(), nullable=True),
        sa.Column("cause_type", sa.String(), nullable=True),
        sa.Column("cause_description", sa.String(), nullable=True),
        sa.Column("date_of_birth", sa.DATE(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("solera_claims", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
