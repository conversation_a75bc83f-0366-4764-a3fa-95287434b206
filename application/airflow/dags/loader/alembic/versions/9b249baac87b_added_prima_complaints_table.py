"""added prima complaints table

Revision ID: 9b249baac87b
Revises: 67238ee86fdb
Create Date: 2020-11-30 13:49:30.874685

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "9b249baac87b"
down_revision = "67238ee86fdb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "prima_complaints",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("progressive_number", sa.Integer(), nullable=True),
        sa.Column("issue_number", sa.String(), nullable=True),
        sa.Column("creation_year", sa.Integer(), nullable=True),
        sa.Column("complaint_code", sa.String(), nullable=True),
        sa.Column("complaint_handling_code", sa.String(), nullable=True),
        sa.Column("reception_date", sa.String(), nullable=True),
        sa.Column("product_type", sa.String(), nullable=True),
        sa.Column("sector", sa.String(), nullable=True),
        sa.Column("name_policyholder", sa.String(), nullable=True),
        sa.Column("address_policyholder", sa.String(), nullable=True),
        sa.Column("type_policyholder", sa.String(), nullable=True),
        sa.Column("geographic_area_policyholder", sa.String(), nullable=True),
        sa.Column("name_complainer", sa.String(), nullable=True),
        sa.Column("address_complainer", sa.String(), nullable=True),
        sa.Column("type_complainer", sa.String(), nullable=True),
        sa.Column("closure_date", sa.String(), nullable=True),
        sa.Column("result", sa.String(), nullable=True),
        sa.Column("judicial_authority_intervention", sa.String(), nullable=True),
        sa.Column("monetary_value", sa.String(), nullable=True),
        sa.Column("duration_till_closure", sa.String(), nullable=True),
        sa.Column("complaint_subject", sa.String(), nullable=True),
        sa.Column("handled_by_ivass", sa.String(), nullable=True),
        sa.Column("ref_complaint_ivass", sa.String(), nullable=True),
        sa.Column("ref_claim", sa.String(), nullable=True),
        sa.Column("response", sa.String(), nullable=True),
        sa.Column("transferred_judicial_authority", sa.String(), nullable=True),
        sa.Column("reopened", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("prima_complaints", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
