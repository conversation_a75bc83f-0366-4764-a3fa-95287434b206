"""add hector policies

Revision ID: abd6f984ed69
Revises: c8fa59e58b63
Create Date: 2023-09-06 17:11:46.563433

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "abd6f984ed69"
down_revision = "c8fa59e58b63"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "hector_policies",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("policy_type", sa.String(), nullable=True),
        sa.Column("location_of_underwriting", sa.String(), nullable=True),
        sa.Column("location_of_risk", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.String(), nullable=True),
        sa.Column("cover", sa.String(), nullable=True),
        sa.Column("package", sa.String(), nullable=True),
        sa.Column("insured_value_pd", sa.String(), nullable=True),
        sa.Column("insured_value_bi", sa.String(), nullable=True),
        sa.Column("insured_value_others", sa.Float(), nullable=True),
        sa.Column("policy_start_date", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("period_start_date", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("policy_end_date", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("period_end_date", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("transaction_currency", sa.String(), nullable=True),
        sa.Column("gross_written_premium", sa.Float(), nullable=True),
        sa.Column("ipt_rate", sa.Float(), nullable=True),
        sa.Column("ipt", sa.Float(), nullable=True),
        sa.Column("total_underwritten", sa.Float(), nullable=True),
        sa.Column("commission", sa.Float(), nullable=True),
        sa.Column("commission_rate", sa.Float(), nullable=True),
        sa.Column("net_balance_due_to_iptiq", sa.Float(), nullable=True),
        sa.Column("annual_gross_written_premium", sa.Float(), nullable=True),
        sa.Column("annual_tax_amount", sa.Float(), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("vehicle_owner_name", sa.String(), nullable=True),
        sa.Column("main_driver_name", sa.String(), nullable=True),
        sa.Column("main_driver_birthdate", sa.Date(), nullable=True, autoincrement=False),
        sa.Column("street", sa.String(), nullable=True),
        sa.Column("city", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("installment", sa.String(), nullable=True),
        sa.Column("payment_frequency", sa.String(), nullable=True),
        sa.Column("distribution_channel", sa.String(), nullable=True),
        sa.Column("number_of_previous_claims", sa.Integer(), nullable=True),
        sa.Column("cancellation_of_previous_insurer", sa.Integer(), nullable=True),
        sa.Column("annual_milage", sa.Float(), nullable=True),
        sa.Column("age_of_youngest_driver", sa.Integer(), nullable=True),
        sa.Column("age_of_policyholder", sa.Integer(), nullable=True),
        sa.Column("gdv_mtpl_regional_class", sa.String(), nullable=True),
        sa.Column("gdv_lim_mod_regional_class", sa.String(), nullable=True),
        sa.Column("gdv_mod_regional_class", sa.String(), nullable=True),
        sa.Column("gdv_type_class", sa.String(), nullable=True),
        sa.Column("hector_regional_class", sa.String(), nullable=True),
        sa.Column("vehicle_registration_number", sa.String(), nullable=True),
        sa.Column("vehicle_hsn_tsn", sa.String(), nullable=True),
        sa.Column("vehicle_brand", sa.String(), nullable=True),
        sa.Column("vehicle_power", sa.Integer(), nullable=True),
        sa.Column("vehicle_power_sum", sa.Integer(), nullable=True),
        sa.Column("vehicle_category", sa.String(), nullable=True),
        sa.Column("vehicle_sold_year", sa.Integer(), nullable=True),
        sa.Column("deductible_amount", sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("hector_policies", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
