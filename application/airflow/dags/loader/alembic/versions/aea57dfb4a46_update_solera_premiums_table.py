"""update solera premiums table

Revision ID: aea57dfb4a46
Revises: f6816bd55901
Create Date: 2024-01-15 11:50:18.035829

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "aea57dfb4a46"
down_revision = "f6816bd55901"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "solera_premium",
        sa.Column("insurer_commission_amount", sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "solera_premium",
        sa.Column("iptiq_branch_code", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "solera_premium",
        sa.Column("iptiq_mapping_code", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "solera_premium",
        sa.Column("policy_number", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "solera_premium",
        sa.Column("package_number", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )

    op.drop_column(
        "solera_premium",
        "direct_claim_handling", schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.drop_column(
        "solera_premium",
        "underwriting_year",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "solera_premium",
        sa.Column("underwriting_year", sa.VARCHAR(), autoincrement=False, nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "solera_premium",
        sa.Column("direct_claim_handling", sa.VARCHAR(), autoincrement=False, nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )

    op.drop_column(
        "solera_premium",
        "package_number",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.drop_column(
        "solera_premium",
        "policy_number", schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.drop_column(
        "solera_premium",
        "iptiq_mapping_code",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.drop_column(
        "solera_premium",
        "iptiq_branch_code",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.drop_column(
        "solera_premium",
        "insurer_commission_amount",
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###
