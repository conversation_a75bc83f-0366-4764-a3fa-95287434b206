"""create table tuio complaints

Revision ID: 04c56d37f972
Revises: f45d9385bd2c
Create Date: 2025-03-28 13:20:22.268718

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA


# revision identifiers, used by Alembic.
revision = '04c56d37f972'
down_revision = 'f45d9385bd2c'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'tuio_complaints',
        sa.Column('complaint_id', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('url_web', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('received_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column('solved_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column('first_resolution_days', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False,
                  nullable=True),
        sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('claims_id_tentative', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('claim_id', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('policy_id', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('claim_type', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('complaint_summary', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('response_summary', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('type', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('resolution', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('category', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('topic', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('compensation', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('external_procedure', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('settled_in_court', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('caa_category', sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column('chksum', sa.VARCHAR(), autoincrement=False, nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    op.drop_table('tuio_complaints', schema=DEFAULT_DB_LANDING_SCHEMA,)
