"""Change hector period_begin_date to varchar

Revision ID: 389c9c74fa1a
Revises: ad2a4a213ed6
Create Date: 2023-11-09 13:52:24.584473

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '389c9c74fa1a'
down_revision = 'ad2a4a213ed6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("hector_policies", "period_start_date", type_=sa.String(), schema=DEFAULT_DB_LANDING_SCHEMA)
    op.alter_column("hector_policies", "number_of_previous_claims", type_=sa.String(), schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "hector_policies",
        "period_start_date",
        type_=sa.Date(),
        postgresql_using="period_start_date::DATE",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "hector_policies",
        "number_of_previous_claims",
        type_=sa.Integer(),
        postgresql_using="number_of_previous_claims::INTEGER",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
