"""add new tuio columns

Revision ID: 33da4e4fe1e6
Revises: 9a9471633236
Create Date: 2024-02-29 18:20:14.324450

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '33da4e4fe1e6'
down_revision = '9a9471633236'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'tuio_policies',
        sa.Column('policyholder_type', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_claims',
        sa.Column('policy_contract_reference', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tuio_policies', 'policyholder_type', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_claims', 'policy_contract_reference', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
