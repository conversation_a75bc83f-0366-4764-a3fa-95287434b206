"""add old policy number to toni policies

Revision ID: f45d9385bd2c
Revises: 3b6b47cfb85a
Create Date: 2025-02-03 12:06:30.022352

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'f45d9385bd2c'
down_revision = '3b6b47cfb85a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'toni_policies',
        sa.Column('old_policy_number', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('toni_policies', 'old_policy_number', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
