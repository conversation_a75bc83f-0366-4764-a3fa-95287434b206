"""add new columns to hector_premiums

Revision ID: 1891f09c3986
Revises: e1c3bfcb32f9
Create Date: 2024-03-18 12:26:58.590345

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '1891f09c3986'
down_revision = 'e1c3bfcb32f9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hector_premium",
        sa.Column("courtage", sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "hector_premium",
        sa.Column("payment_frequency", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "hector_premium",
        sa.Column("customer_connection", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "hector_premium",
        sa.Column("agent_name", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("hector_premium", "courtage", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("hector_premium", "payment_frequency", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("hector_premium", "customer_connection", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("hector_premium", "agent_name", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
