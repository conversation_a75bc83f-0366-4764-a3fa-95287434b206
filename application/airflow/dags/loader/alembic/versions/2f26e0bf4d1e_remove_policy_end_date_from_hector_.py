"""Remove policy_end_date from hector claims

Revision ID: 2f26e0bf4d1e
Revises: 890fd802c071
Create Date: 2023-10-10 14:42:28.439245

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA
# revision identifiers, used by Alembic.
revision = '2f26e0bf4d1e'
down_revision = '890fd802c071'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("hector_claims", "policy_end_date", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hector_claims",
        sa.Column("policy_end_date", sa.DATE(), autoincrement=False, nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
