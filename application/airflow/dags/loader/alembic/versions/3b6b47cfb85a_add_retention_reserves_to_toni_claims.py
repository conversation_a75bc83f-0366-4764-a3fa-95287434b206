"""add retention reserves to toni claims

Revision ID: 3b6b47cfb85a
Revises: e1dbca3e7881
Create Date: 2025-01-13 11:17:36.171865

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA


# revision identifiers, used by Alembic.
revision = '3b6b47cfb85a'
down_revision = 'e1dbca3e7881'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'toni_claims',
        sa.Column('retention_reserve', sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'toni_claims',
        sa.Column('retention_reserve_prev', sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('toni_claims', 'retention_reserve_prev', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('toni_claims', 'retention_reserve', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
