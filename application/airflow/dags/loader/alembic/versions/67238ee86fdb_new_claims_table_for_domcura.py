"""new claims table for domcura

Revision ID: 67238ee86fdb
Revises: e22fd44056af
Create Date: 2020-09-18 15:04:39.306790

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "67238ee86fdb"
down_revision = "e22fd44056af"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("domcura_s", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.create_table(
        "domcura_claims",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("vsnr", sa.String(), nullable=True),
        sa.Column("risiko", sa.String(), nullable=True),
        sa.Column("schaden_vu", sa.String(), nullable=True),
        sa.Column("schadennr_eigen", sa.String(), nullable=True),
        sa.Column("schadentag", sa.String(), nullable=True),
        sa.Column("meldetag", sa.String(), nullable=True),
        sa.Column("status", sa.String(), nullable=True),
        sa.Column("schadenart", sa.String(), nullable=True),
        sa.Column("schadenursache", sa.String(), nullable=True),
        sa.Column("gefahr", sa.String(), nullable=True),
        sa.Column("produkt", sa.String(), nullable=True),
        sa.Column("sparte", sa.String(), nullable=True),
        sa.Column("anteil", sa.String(), nullable=True),
        sa.Column("agenturnummer", sa.String(), nullable=True),
        sa.Column("s_eingang", sa.String(), nullable=True),
        sa.Column("s_ausgang", sa.String(), nullable=True),
        sa.Column("schliessdatum", sa.String(), nullable=True),
        sa.Column("deckungsentsch", sa.String(), nullable=True),
        sa.Column("vn_name", sa.String(), nullable=True),
        sa.Column("vm_nr", sa.String(), nullable=True),
        sa.Column("vm_name", sa.String(), nullable=True),
        sa.Column("vm_typ", sa.String(), nullable=True),
        sa.Column("reg_ford", sa.Float(), nullable=True),
        sa.Column("reg_ausgleich", sa.Float(), nullable=True),
        sa.Column("kostenentsch", sa.Float(), nullable=True),
        sa.Column("entschaedigung", sa.Float(), nullable=True),
        sa.Column("mandant", sa.String(), nullable=True),
        sa.Column("reserve", sa.Float(), nullable=True),
        sa.Column("gezahlt", sa.Float(), nullable=True),
        sa.Column("gesamt", sa.Float(), nullable=True),
        sa.Column("gesamt_vu", sa.Float(), nullable=True),
        sa.Column("vn_vollname", sa.String(), nullable=True),
        sa.Column("vn_vorname", sa.String(), nullable=True),
        sa.Column("zahlungen", sa.Float(), nullable=True),
        sa.Column("reservekosten", sa.Float(), nullable=True),
        sa.Column("verursachertyp", sa.String(), nullable=True),
        sa.Column("anspruchsteller", sa.String(), nullable=True),
        sa.Column("prod_nr", sa.String(), nullable=True),
        sa.Column("produktlinie", sa.String(), nullable=True),
        sa.Column("bedingungen", sa.String(), nullable=True),
        sa.Column("vm_typ_2", sa.String(), nullable=True),
        sa.Column("gesellschaft_nr", sa.String(), nullable=True),
        sa.Column("gesellschaft_name_1", sa.String(), nullable=True),
        sa.Column("gesellschaft_name_2", sa.String(), nullable=True),
        sa.Column("id_gesellschaft", sa.String(), nullable=True),
        sa.Column("id_vertrag", sa.String(), nullable=True),
        sa.Column("id_schaden", sa.String(), nullable=True),
        sa.Column("gesellschaft_id", sa.String(), nullable=True),
        sa.Column("vers_id", sa.String(), nullable=True),
        sa.Column("teilstring_agenturnr", sa.String(), nullable=True),
        sa.Column("plz", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("domcura_claims", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.create_table(
        "domcura_s",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column(
            "created_on",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("gefahr", sa.String(), nullable=True),
        sa.Column("produkt", sa.String(), nullable=True),
        sa.Column("sparte", sa.String(), nullable=True),
        sa.Column("anteil", sa.String(), nullable=True),
        sa.Column("agenturnummer", sa.String(), nullable=True),
        sa.Column("s_eingang", sa.String(), nullable=True),
        sa.Column("s_ausgang", sa.String(), nullable=True),
        sa.Column("schliessdatum", sa.String(), nullable=True),
        sa.Column("deckungsentsch", sa.String(), nullable=True),
        sa.Column("vn_name", sa.String(), nullable=True),
        sa.Column("vm_nr", sa.String(), nullable=True),
        sa.Column("vm_name", sa.String(), nullable=True),
        sa.Column("vm_typ", sa.String(), nullable=True),
        sa.Column("reg_ford", sa.Float(), nullable=True),
        sa.Column("reg_ausgleich", sa.Float(), nullable=True),
        sa.Column("kostenentsch", sa.Float(), nullable=True),
        sa.Column("entschaedigung", sa.Float(), nullable=True),
        sa.Column("mandant", sa.String(), nullable=True),
        sa.Column("reserve", sa.Float(), nullable=True),
        sa.Column("gezahlt", sa.Float(), nullable=True),
        sa.Column("gesamt", sa.Float(), nullable=True),
        sa.Column("gesamt_vu", sa.Float(), nullable=True),
        sa.Column("vn_vollname", sa.String(), nullable=True),
        sa.Column("vn_vorname", sa.String(), nullable=True),
        sa.Column("zahlungen", sa.Float(), nullable=True),
        sa.Column("reservekosten", sa.Float(), nullable=True),
        sa.Column("verursachertyp", sa.String(), nullable=True),
        sa.Column("anspruchsteller", sa.String(), nullable=True),
        sa.Column("prod_nr", sa.String(), nullable=True),
        sa.Column("produktlinie", sa.String(), nullable=True),
        sa.Column("bedingungen", sa.String(), nullable=True),
        sa.Column("vm_typ_2", sa.String(), nullable=True),
        sa.Column("gesellschaft_nr", sa.String(), nullable=True),
        sa.Column("gesellschaft_name_1", sa.String(), nullable=True),
        sa.Column("gesellschaft_name_2", sa.String(), nullable=True),
        sa.Column("id_gesellschaft", sa.String(), nullable=True),
        sa.Column("id_vertrag", sa.String(), nullable=True),
        sa.Column("id_schaden", sa.String(), nullable=True),
        sa.Column("gesellschaft_id", sa.String(), nullable=True),
        sa.Column("vers_id", sa.String(), nullable=True),
        sa.Column("teilstring_agenturnr", sa.String(), nullable=True),
        sa.Column("plz", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
