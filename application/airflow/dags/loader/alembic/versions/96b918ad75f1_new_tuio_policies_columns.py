"""new tuio policies columns

Revision ID: 96b918ad75f1
Revises: ae5eb973c200
Create Date: 2024-02-14 10:34:48.646045

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '96b918ad75f1'
down_revision = 'ae5eb973c200'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'tuio_policies',
        sa.Column('valuable_item_amount', sa.Float(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_policies',
        sa.Column('house_occupation', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_policies',
        sa.Column('contract_start_date', sa.DateTime(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_policies',
        sa.Column('collection_group_status', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_policies',
        sa.Column('collection_id', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        'tuio_policies',
        sa.Column('payment_confirmation_date', sa.DateTime(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tuio_policies', 'payment_confirmation_date', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_policies', 'collection_id', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_policies', 'collection_group_status', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_policies', 'contract_start_date', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_policies', 'house_occupation', schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column('tuio_policies', 'valuable_item_amount', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
