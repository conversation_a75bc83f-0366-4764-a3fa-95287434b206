"""Add columns hector sanctions

Revision ID: 890fd802c071
Revises: 53c8d1f1e818
Create Date: 2023-09-25 16:40:30.385183

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA
# revision identifiers, used by Alembic.
revision = '890fd802c071'
down_revision = '53c8d1f1e818'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hector_sanctions", sa.Column("bvd_id", sa.String(), nullable=True), schema=DEFAULT_DB_LANDING_SCHEMA
    )
    op.add_column(
        "hector_sanctions", sa.Column("national_id", sa.String(), nullable=True), schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("hector_sanctions", "bvd_id", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("hector_sanctions", "national_id", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
