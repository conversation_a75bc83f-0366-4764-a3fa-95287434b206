"""Add Prima Spain policies

Revision ID: 1ad388e3f1ea
Revises: 67a85c7d3361
Create Date: 2022-11-29 15:25:30.573055

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "1ad388e3f1ea"
down_revision = "67a85c7d3361"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "prima_premium_spain",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("policy_type", sa.String(), nullable=True),
        sa.Column("location_of_underwriting", sa.String(), nullable=True),
        sa.Column("location_of_risk", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.String(), nullable=True),
        sa.Column("cover", sa.String(), nullable=True),
        sa.Column("cover_level", sa.String(), nullable=True),
        sa.Column("insured_value_pd", sa.String(), nullable=True),
        sa.Column("insured_value_bi", sa.String(), nullable=True),
        sa.Column("insured_value_other", sa.String(), nullable=True),
        sa.Column("insured_value_mod", sa.String(), nullable=True),
        sa.Column("policy_start_date", sa.Date(), nullable=True),
        sa.Column("policy_end_date", sa.Date(), nullable=True),
        sa.Column("period_start_date", sa.Date(), nullable=True),
        sa.Column("period_end_date", sa.Date(), nullable=True),
        sa.Column("issue_date", sa.Date(), nullable=True),
        sa.Column("payment_date", sa.Date(), nullable=True),
        sa.Column("underwriting_year", sa.Integer(), nullable=True),
        sa.Column("transaction_currency", sa.String(), nullable=True),
        sa.Column("gross_written_premium", sa.Float(), nullable=True),
        sa.Column("ipt_rate", sa.Float(), nullable=True),
        sa.Column("ipt", sa.Float(), nullable=True),
        sa.Column("surcharge_tax_rate", sa.Float(), nullable=True),
        sa.Column("surcharge_tax", sa.Float(), nullable=True),
        sa.Column("clea_rate", sa.Float(), nullable=True),
        sa.Column("clea", sa.Float(), nullable=True),
        sa.Column("national_guarantee_fund_rate", sa.Float(), nullable=True),
        sa.Column("national_guarantee_fund", sa.Float(), nullable=True),
        sa.Column("ofesauto", sa.Float(), nullable=True),
        sa.Column("total_underwritten", sa.Float(), nullable=True),
        sa.Column("commission", sa.Float(), nullable=True),
        sa.Column("commission_rate", sa.Float(), nullable=True),
        sa.Column("net_balance_due_to_iptiq", sa.Float(), nullable=True),
        sa.Column("annual_gross_written_premium", sa.Float(), nullable=True),
        sa.Column("annual_ipt", sa.Float(), nullable=True),
        sa.Column("ipt_territory", sa.String(), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("insured_nif", sa.String(), nullable=True),
        sa.Column("vehicle_owner_name", sa.String(), nullable=True),
        sa.Column("vehicle_owner_nif", sa.String(), nullable=True),
        sa.Column("main_driver_name", sa.String(), nullable=True),
        sa.Column("main_driver_nif", sa.String(), nullable=True),
        sa.Column("main_driver_birthdate", sa.Date(), nullable=True),
        sa.Column("driving_licence_date", sa.Date(), nullable=True),
        sa.Column("driving_licence_country", sa.String(), nullable=True),
        sa.Column("residency_country", sa.String(), nullable=True),
        sa.Column("street", sa.String(), nullable=True),
        sa.Column("city", sa.String(), nullable=True),
        sa.Column("post_code", sa.String(), nullable=True),
        sa.Column("vehicle_registration_number", sa.String(), nullable=True),
        sa.Column("vehicle_make", sa.String(), nullable=True),
        sa.Column("vehicle_model", sa.String(), nullable=True),
        sa.Column("registration_date", sa.Date(), nullable=True),
        sa.Column("actual_value", sa.Float(), nullable=True),
        sa.Column("vehicle_power", sa.Integer(), nullable=True),
        sa.Column("fuel_type", sa.String(), nullable=True),
        sa.Column("vehicle_category", sa.String(), nullable=True),
        sa.Column("main_driving_region", sa.String(), nullable=True),
        sa.Column("installment", sa.String(), nullable=True),
        sa.Column("payment_frequency", sa.String(), nullable=True),
        sa.Column("distribution_channel", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("prima_premium_spain", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
