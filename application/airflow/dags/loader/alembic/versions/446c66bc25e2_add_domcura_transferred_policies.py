"""Add Domcura transferred policies

Revision ID: 446c66bc25e2
Revises: f8790f23f07b
Create Date: 2025-06-27 13:18:48.544506

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA


# revision identifiers, used by Alembic.
revision = '446c66bc25e2'
down_revision = 'f8790f23f07b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'domcura_transferred_policies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chksum', sa.String(), nullable=True),
        sa.Column('created_on', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('policy_id', sa.String(), nullable=True),
        sa.Column('counter', sa.String(), nullable=True),
        sa.Column('generic_due_date', sa.String(), nullable=True),
        sa.Column('transfer_date', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('domcura_transferred_policies', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
