"""add solera financial table

Revision ID: ae5eb973c200
Revises: fb166a2829ea
Create Date: 2024-01-18 16:30:19.142347

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "ae5eb973c200"
down_revision = "fb166a2829ea"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "solera_financial",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("contract_id", sa.String(), nullable=True),
        sa.Column("version_number", sa.Integer(), nullable=True),
        sa.Column("sender_reference", sa.String(), nullable=True),
        sa.Column("application_name", sa.String(), nullable=True),
        sa.Column("application_version", sa.String(), nullable=True),
        sa.Column("reporting_period_from", sa.Date(), nullable=True),
        sa.Column("reporting_period_to", sa.Date(), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("additional_name", sa.String(), nullable=True),
        sa.Column("telephone", sa.String(), nullable=True),
        sa.Column("company_code", sa.String(), nullable=True),
        sa.Column("identification_number", sa.String(), nullable=True),
        sa.Column("insurer_name", sa.String(), nullable=True),
        sa.Column("insurer_additional_name", sa.String(), nullable=True),
        sa.Column("insurer_telephone", sa.String(), nullable=True),
        sa.Column("insurer_company_code", sa.String(), nullable=True),
        sa.Column("insurer_identification_number", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("solera_financial", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
