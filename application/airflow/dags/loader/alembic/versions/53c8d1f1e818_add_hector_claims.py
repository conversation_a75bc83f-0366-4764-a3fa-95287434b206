"""add_hector_claims

Revision ID: 53c8d1f1e818
Revises: abd6f984ed69
Create Date: 2023-09-07 18:38:08.390287

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "53c8d1f1e818"
down_revision = "abd6f984ed69"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "hector_claims",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=True),
        sa.Column("claim_number", sa.String(), nullable=True),
        sa.Column("tpa_number", sa.String(), nullable=True),
        sa.Column("occurrence_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("postcode_occurrence", sa.String(), nullable=True),
        sa.Column("city_occurrence", sa.String(), nullable=True),
        sa.Column("date_notified_to_hector", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("opening_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("last_review_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("insured", sa.String(), nullable=True),
        sa.Column("insured_city", sa.String(), nullable=True),
        sa.Column("class_of_business", sa.String(), nullable=True),
        sa.Column("cover", sa.String(), nullable=True),
        sa.Column("claimant", sa.String(), nullable=True),
        sa.Column("policy_start_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("policy_end_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("closing_date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column("vehicle_registration_number", sa.String(), nullable=True),
        sa.Column("claim_type", sa.String(), nullable=True),
        sa.Column("claim_description", sa.String(), nullable=True),
        sa.Column("presence_pd", sa.String(), nullable=True),
        sa.Column("claim_status_at_end_of_the_month", sa.String(), nullable=True),
        sa.Column("paid_amount", sa.Float(), nullable=True),
        sa.Column("claim_assessment_paid", sa.Float(), nullable=True),
        sa.Column("recovered", sa.Float(), nullable=True),
        sa.Column("reserved", sa.Float(), nullable=True),
        sa.Column("recovery_reserve", sa.Float(), nullable=True),
        sa.Column("total_incurred", sa.Float(), nullable=True),
        sa.Column("claims_referred", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    # ### commands auto generated by Alembic ###
    op.drop_table("hector_claims", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
