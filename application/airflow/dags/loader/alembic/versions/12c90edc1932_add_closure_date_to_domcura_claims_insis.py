"""add closure date to domcura claims insis

Revision ID: 12c90edc1932
Revises: 17e0747c9a50
Create Date: 2023-06-27 11:43:06.864277

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "12c90edc1932"
down_revision = "17e0747c9a50"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "domcura_claims_insis",
        sa.Column("claim_closure_date", sa.Date(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("domcura_claims_insis", "claim_closure_date", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
