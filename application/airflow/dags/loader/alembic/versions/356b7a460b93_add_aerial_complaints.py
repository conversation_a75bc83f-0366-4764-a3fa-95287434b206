"""add aerial complaints

Revision ID: 356b7a460b93
Revises: 8ce0718c20e8
Create Date: 2023-10-19 12:56:03.632282

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA


# revision identifiers, used by Alembic.
revision = "356b7a460b93"
down_revision = "8ce0718c20e8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "aerial_complaints",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chksum", sa.String(), autoincrement=False, nullable=True),
        sa.Column("created_on", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("complaint_reference", sa.String(), autoincrement=False, nullable=True),
        sa.Column("n_complaint_references", sa.String(), autoincrement=False, nullable=True),
        sa.Column("creation_year", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_code", sa.String(), autoincrement=False, nullable=True),
        sa.Column("reception_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("product_type", sa.String(), autoincrement=False, nullable=True),
        sa.Column("sector", sa.String(), autoincrement=False, nullable=True),
        sa.Column("name_policyholder", sa.String(), autoincrement=False, nullable=True),
        sa.Column("address", sa.String(), autoincrement=False, nullable=True),
        sa.Column("channel", sa.String(), autoincrement=False, nullable=True),
        sa.Column("geographic_area_policyholder", sa.String(), autoincrement=False, nullable=True),
        sa.Column("name_complainer", sa.String(), autoincrement=False, nullable=True),
        sa.Column("address_complainer", sa.String(), autoincrement=False, nullable=True),
        sa.Column("type_complainer", sa.String(), autoincrement=False, nullable=True),
        sa.Column("closure_date", sa.Date(), autoincrement=False, nullable=True),
        sa.Column("status", sa.String(), autoincrement=False, nullable=True),
        sa.Column("judicial_authority_intervention", sa.String(), autoincrement=False, nullable=True),
        sa.Column("monetary_value", sa.String(), autoincrement=False, nullable=True),
        sa.Column("duration_till_closure", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_subject", sa.String(), autoincrement=False, nullable=True),
        sa.Column("ref_policy", sa.String(), autoincrement=False, nullable=True),
        sa.Column("ref_claim", sa.String(), autoincrement=False, nullable=True),
        sa.Column("response", sa.String(), autoincrement=False, nullable=True),
        sa.Column("transferred_judicial_authority", sa.String(), autoincrement=False, nullable=True),
        sa.Column("reopened", sa.String(), autoincrement=False, nullable=True),
        sa.Column("complaint_cause", sa.String(), autoincrement=False, nullable=True),
        sa.Column("elapsed_days", sa.String(), autoincrement=False, nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )


def downgrade():
    # ### commands auto generated by Alembic ###
    op.drop_table("aerial_complaints", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
