"""Add product to prima complaints

Revision ID: 69ff235cb01b
Revises: 6a4b741c2d06
Create Date: 2024-03-10 12:03:00.300753

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '69ff235cb01b'
down_revision = '6a4b741c2d06'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'prima_complaints',
        sa.Column('product', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prima_complaints', 'product', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
