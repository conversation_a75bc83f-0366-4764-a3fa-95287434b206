"""add transaction seq number to toni policies

Revision ID: 6a4b741c2d06
Revises: d8af636f05c6
Create Date: 2024-03-06 10:00:42.709326

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = '6a4b741c2d06'
down_revision = 'd8af636f05c6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'toni_policies',
        sa.Column('transaction_sequence_number', sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('toni_policies', 'transaction_sequence_number',
                   schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
