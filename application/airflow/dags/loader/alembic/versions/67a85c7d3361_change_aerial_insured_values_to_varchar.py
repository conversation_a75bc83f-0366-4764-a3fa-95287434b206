"""Change Aerial insured values to varchar

Revision ID: 67a85c7d3361
Revises: 9f0c2243c743
Create Date: 2022-11-24 13:06:59.545530

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "67a85c7d3361"
down_revision = "9f0c2243c743"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("aerial_premium", "insured_value_pd", type_=sa.String(), schema=DEFAULT_DB_LANDING_SCHEMA)
    op.alter_column("aerial_premium", "insured_value_bi", type_=sa.String(), schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "aerial_premium",
        "insured_value_pd",
        type_=sa.Float(),
        postgresql_using="NULL",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.alter_column(
        "aerial_premium",
        "insured_value_bi",
        type_=sa.Float(),
        postgresql_using="NULL",
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###
