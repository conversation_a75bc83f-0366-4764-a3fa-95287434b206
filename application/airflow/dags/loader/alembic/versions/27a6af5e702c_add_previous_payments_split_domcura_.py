"""add previous payments split domcura claims insis

Revision ID: 27a6af5e702c
Revises: fc81bd792db6
Create Date: 2023-07-20 11:04:44.976171

"""
import sqlalchemy as sa

from alembic import op
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = "27a6af5e702c"
down_revision = "fc81bd792db6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "domcura_claims_insis",
        sa.Column("cost_payments_before_handover", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    op.add_column(
        "domcura_claims_insis",
        sa.Column("indemnity_payments_before_handover", sa.String(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("domcura_claims_insis", "cost_payments_before_handover", schema=DEFAULT_DB_LANDING_SCHEMA)
    op.drop_column("domcura_claims_insis", "indemnity_payments_before_handover", schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
