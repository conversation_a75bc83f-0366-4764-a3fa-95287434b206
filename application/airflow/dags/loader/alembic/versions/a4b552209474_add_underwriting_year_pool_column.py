"""Add underwriting_year_pool column to solera claims

Revision ID: a4b552209474
Revises: 1891f09c3986
Create Date: 2024-03-26 16:59:48.985518

"""
from alembic import op
import sqlalchemy as sa
from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA

# revision identifiers, used by Alembic.
revision = 'a4b552209474'
down_revision = '1891f09c3986'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'solera_claims',
        sa.Column('underwriting_year_pool', sa.Integer(), nullable=True),
        schema=DEFAULT_DB_LANDING_SCHEMA
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('solera_claims', 'underwriting_year_pool', schema=DEFAULT_DB_LANDING_SCHEMA)
    # ### end Alembic commands ###
