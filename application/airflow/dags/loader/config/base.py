import logging
import os

import boto3
from sqlalchemy.ext.declarative import declarative_base

# from logstash_formatter import LogstashFormatterV1

APP_NAME = "Test Application"

numeric_level = getattr(logging, os.environ.get("LOGLEVEL", "INFO").upper(), None)
logging.basicConfig(level=numeric_level)

logger = logging.getLogger()
# logger.handlers[0].setFormatter(LogstashFormatterV1(fmt='{"extra":{"app":"mga-loader"}}'))

ROOT_DIR = os.path.join(
    os.path.dirname(os.path.realpath(__file__)),
    os.pardir,
)

AWS_REGION = "eu-central-1"
Base = declarative_base()

NOTIFICATION_TRESHOLD_MINS = 10

FILE_IGNORE_LIST = {
    "IPTIQ_EFH_RH_2020_06.csv",
    "IPTIQ_EFH_RH_2020_07_02.csv",
    "IPTIQ_S_2020_07_07.csv",
    "IPTIQ_EFH_RH_2020_12_11.csv",  # adjusted later
    "IPTIQ_EFH_RH_2021_01_29.csv",  # adjusted
    "IPTIQ_EFH_RH_2021_02_05.csv",  # adjusted
    "IPTIQ_EFH_RH_2021_02_19.csv",  # adjusted
    "IPTIQ_EFH_RH_2021_02_26.csv",
    "10_11_October___November_Claims_ES_vshared_170723.xlsx",  # duplicate file
    "2022_11_november_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2022_11_november_claims_es_bdx_report_vshared_25092023.xlsx",   # duplicate file
    "2022_11_november_claims_es_bdx_report_vshared_20092023.xlsx",   # duplicate file
    "12_December_Claims_ES_vshared_180723.xlsx",  # won't load due to column names mismatch, adjusted
    "2022_12_december_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2022_12_december_claims_es_bdx_report_vshared_25092023.xlsx",  # duplicate file
    "01_January_Claims_ES_vshared180723.xlsx",  # won't load due to column names mismatch, adjusted
    "2023_01_january_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2023_01_january_claims_es_bdx_report_vshared_25092023.xlsx",  # duplicate file
    "02_February_Claims_ES_vShared120723.xlsx",  # duplicate file
    "02. February Claims ES vShared120723.xlsx",  # duplicate file
    "02_February_Claims_ES_vShared180723.xlsx",  # won't load due to column names mismatch, adjusted
    "2023_02_february_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2023_02_february_claims_es_bdx_report_vshared_25092023.xlsx",  # duplicate file
    "03_March_Claims_ES_vshared_120723.xlsx",  # duplicate file, most recent one is loaded
    "03_March_Claims_ES_vshared_180723.xlsx",  # duplicate file
    "2023_03_march_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2023_03_march_claims_es_bdx_report_vshared_25092023.xlsx",  # duplicate file
    "04_April_Claims_ES_vshared_120723.xlsx",  # duplicate file
    "04_April_Claims_ES_vshared_180723.xlsx",  # won't load due to date values in float columns, adjusted
    "2023_04_april_claims_es_bdx_report_vshared_20092023.xlsx",  # won't load due to date values in float columns,
    # adjusted
    "2023_04_april_claims_es_bdx_report_vshared_25092023.xlsx",  # duplicate
    "2023_04_april_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate
    "05_May_Claims_ES_vshared_19072023.xlsx",  # won't load due to column names mismatch, adjusted
    "2023_05_may_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2023_05_may_claims_es_bdx_report_vshared_25092023.xlsx",  # duplicate file
    "06__June_CLAIM_ES_BDX_report_vshared_180723.xlsx",  # duplicate file
    "2023_06_june_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "06__June_CLAIM_ES_BDX_report_vshared_180723.xlsx",  # duplicate file
    "07. July CLAIM ES BDX report vshared 180823.xlsx",  # duplicate file
    "07__July_CLAIM_ES_BDX_report_vshared24082023.xlsx",  # duplicate file
    "07. July CLAIM ES BDX report vshared 010923.xlsx",  # duplicate file
    "2023_07_july_claims_es_bdx_report_vshared_08092023.xlsx",  # duplicate file
    "2023_07_july_claims_es_bdx_report_vshared_14092023.xlsx",  # duplicate file
    "2023_08_august_claims_es_bdx_report_vshared_19092023.xlsx",  # duplicate file
    "2023_08_august_claims_es_bdx_report_vshared_11092023.xlsx",  # won't load due to column names mismatch, adjusted
    "IPTIQ_EFH_RH_2023_07_12.csv",  # The empty file that is put both on SFTP server (on time),
    # and later on the integration bucket (on 2023-09-06). It is deleted from the incoming bucket.
    "20230926_iptiq_individuals-companies_export.csv",   # wrong columns format
    "20230921_iptiq_individuals-companies_export.csv",   # wrong columns format
    "iptiq_individuals-companies_export.csv",    # wrong file name format
    "2023_10_october_claims_es_bdx_report_vshared_10112023.xlsx",   # duplicate file
    "2023_10_october_claims_es_bdx_report_vshared_09112023.xlsx",   # duplicate file
    "08-IPTIQ-AOUT-2023.xlsx",  # duplicate file
    "09-IPTIQ-SEPTEMBRE-2023.xlsx",  # duplicate file
    "20231010-20231017-iptiq_policies.csv",  # Tuio preview policies file
    "2023_11_november_premia_es_vshared_04122023.xlsx",  # duplicate file
    "2024-01_Finance_Report_(für_Auszahlung).xlsx",    # Duplicate file
    "24-02-08_Reporting_PREMIUM_1_24.csv",  # won't load due to having dash in float column
    "20200101-20240201-iptiq_policies.csv",  # Tuio policies file that contains all historical data instead of only Jan
    "20200101-20240201-iptiq_claims.csv",  # Tuio claims file with the wrong name
    "Booking_Report.xlsx",  # Hector file with wrong name
    # We ignore 6 initial Tuio claims BDX to avoid loading new versions of them, as we already received corrected
    # versions with a different name (e.g. 20231001-20231031-iptiq_claims_updated - 20231001-20231031-iptiq_claims.csv)
    "20231001-20231031-iptiq_claims.csv",
    "20231101-20231130-iptiq_claims.csv",
    "20231201-20231231-iptiq_claims.csv",
    "20240101-20240131-iptiq_claims.csv",
    "20240201-20240229-iptiq_claims.csv",
    "20240301-20240331-iptiq_claims.csv",
    # Tuio wrongly put Oct 2024 files at the end of April 2024, when reissuing some previous BDX files
    # "20241001-20241031-iptiq_policies.csv", -> commented in November 2024
    "2024_05_may_qyr_es_vshared_05062024.xlsx",  # First row is empty. The file was replaced by,
    # 2024_05_may_qyr_es_vshared_05062024_V2.xlsx
    "2024_05_may_claims_es_bdx_report_vshared_17062024.xlsx",  # Problematic file due to blank Underwriting year
    "2024_06_june_claims_es_bdx_report_vshared_26072024.xlsx",  # Problematic file due to blank Underwriting year
    "20240903_gallen_payment_screening.xlsx.csv",  # Problematic file due to being csv
    "2024_08_Reporting_Booking_Report_9_24.xlsx",  # it should have 2024_09 to match what we already uploaded
    # Skipping the new version of Tuio claims until proper testing is done
    # (loading the full version 20200101-30000101-iptiq_claims.csv instead)
    "20241001-20241031-iptiq_claims.csv",
    "2024-10_Reporting_CLAIMS_10_24.csv",  # Problematic file due to data issue on recovered column.
    "2024_10_october_claims_es_bdx_report_vshared_18112024.xlsx",  # Problematic file due to blank Underwriting year
    "11. November - Premia IT motor.xlsx",  # substitution fee column missing
    "20241219-20241220-iptiq_policies.csv",  # temporary file for Nov liquidations by eoy -> replaced by actual Dec data
    "202412_iptiQ_policy_premium_bdx.csv",  # Problematic file due to data issue on some columns.
    "12. December - Claims Household.xlsx",  # Problematic file due to data issue on UY column.
    "2025-01_Reporting_CLAIMS_01_25.csv",  # Problematic file due to name issue on paid,reserved,incurred column.
    "2025_01_january_premia_es_vshared_0402025.xlsx",  # Problematic file due to typo issue on cover_level column
    "01. January - Claims Household_old.xlsx",  # Prima Claims -> got a fixed version
    "02. February - Claims Household_old.xlsx",  # Prima Claims -> got a fixed version
    "02. February - Claims DE_old.xlsx",  # Prima Claims -> got a fixed version
    "20250301-30000101-iptiq_claims.csv",  # It included EA claims, for the first time, with missing claims information.
    # It was agreed to remove EA claims from the file and wait for Tuio to correctly populate claims information
    # -> EA claims were manually removed and stored as 20250301-30000101-ea_excluded-iptiq_claims.csv
    "202503_iptiQ_policy_premium_bdx.csv",  # We have received a new corrected file
    "20250310-30000101-iptiq_claims.csv",  # unexpectedly uploaded on March 10th
}

FILE_NEEDS_PREPROCESSING_LIST = {r"IPTIQ_EFH_RH_.*\.csv", r"IPTIQ_S_[\d]{4}_[\d]{2}_[\d]{2}.csv"}

FOLDER_PATHS = {"prima", "domcura", "aerial", "hector", "solera", "tuio", "toni", "gallen"}
FTP_REMOTE_PATH = "/home/<USER>/production/out"

__all__ = ["boto3", "Base"]
