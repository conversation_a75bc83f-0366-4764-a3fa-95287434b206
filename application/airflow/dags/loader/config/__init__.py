import os
import importlib
from typing import Dict

from loader.config.base import *
from loader.config.base import (
    logger,
    NOTIFICATION_TRESHOLD_MINS,
    FILE_IGNORE_LIST,
    FILE_NEEDS_PREPROCESSING_LIST,
    FOLDER_PATHS,
    FTP_REMOTE_PATH,
)
from quantum_data_pipeline.utils.common import connect_to_aws_dev_from_local, DATALAKE_ENVIRONMENT

# Get credentials from proxy to access dev
# Run in airflow repo:
# aws-vault exec iptiq-dev --prompt=osascript --ecs-server -- scripts/aws-vault-proxy-server.sh
connect_to_aws_dev_from_local()

APP_NAME = "[DEV] MGA Loader App"

AWS_ENDPOINT_URL = os.getenv("AWS_ENDPOINT_URL")

DEFAULT_DB_LANDING_SCHEMA = os.getenv("DEFAULT_DB_LANDING_SCHEMA", "mgalanding")

DB_CHUNK_SIZE = os.getenv("DB_CHUNK_SIZE", 1000)

TAG = os.getenv("IPTIQ_TAG_BUCKET", False)
