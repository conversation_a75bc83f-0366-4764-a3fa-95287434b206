from typing import Dict, List

import numpy as np


class PandasInterfaceMixin:
    @property
    def header(self) -> int:
        return self._header

    @property
    def decimal(self) -> str:
        return self._decimal

    @property
    def thousands(self) -> str:
        return self._thousands

    @property
    def skipfooterint(self) -> int:
        return self._skipfooterint

    @property
    def skiprows(self) -> int:
        return self._skiprows

    @property
    def dto(self):
        return self._dto

    def date_parser(self):
        return None

    def get_date_fields(self) -> List[str]:
        fields_with_date = [
            v.metadata.get("source")
            for k, v in self.dto.__dataclass_fields__.items()
            if v.metadata.get("dtype") == np.dtype("M")
        ]
        return fields_with_date

    def get_column_mapping(self) -> Dict[str, str]:
        column_mapping = {k: v.metadata.get("mapping") for k, v in self.dto.__dataclass_fields__.items()}
        return column_mapping

    def get_all_source_key(self) -> Dict:
        nd_dtypes = {v.metadata.get("source"): k for k, v in self.dto.__dataclass_fields__.items()}
        return nd_dtypes

    def get_source_key(self) -> Dict:
        nd_dtypes = {
            v.metadata.get("source"): k
            for k, v in self.dto.__dataclass_fields__.items()
            if v.metadata.get("dtype") not in {np.dtype("M")}
        }
        return nd_dtypes

    def get_nondate_dtypes(self) -> Dict:
        nd_dtypes = {
            v.metadata.get("source"): v.metadata.get("dtype")
            for k, v in self.dto.__dataclass_fields__.items()
            if v.metadata.get("dtype") not in {np.dtype("M")}
        }
        return nd_dtypes
