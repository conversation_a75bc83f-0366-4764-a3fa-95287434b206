from pandas._libs.parsers import STR_NA_VALUES

from loader.core.pandas.pandas_factory_mixin import PandasFactoryMixin
from loader.core.pandas.pandas_interface_mixin import PandasInterfaceMixin

"""
We are excluding 'NA' to be considered as 'Not Available' by default as
it causes valid abreviation such as NA for Napoly to be ingested as NULL values
In case the ingested files leverage NA for 'Not Available', we will make
this explicit
"""
NA_VALUES = {na_value for na_value in STR_NA_VALUES if na_value != "NA"}
