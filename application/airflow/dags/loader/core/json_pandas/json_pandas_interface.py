from typing import NewType

import numpy as np  # type: ignore

from loader.core.pandas import PandasInterfaceMixin


class JSONPandasInterface(PandasInterfaceMixin):
    def __init__(self):
        self._orient = "records"
        self._encoding = "utf-8"
        self._keep_default_dates = False
        self._dto = None

    @property
    def orient(self) -> str:
        return self._orient

    @property
    def encoding(self) -> str:
        return self._encoding

    @property
    def keep_default_dates(self) -> bool:
        return self._keep_default_dates


JSONPandasInterfaceType = NewType("JSONPandasInterfaceType", JSONPandasInterface)
