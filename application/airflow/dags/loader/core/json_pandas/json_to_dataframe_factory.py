from typing import Callable

import pandas as pd  # type: ignore

from loader.core.custom_types import PandasDataFrameType
from loader.core.json_pandas import JSONPandasInterface
from loader.core.pandas import PandasFactoryMixin
from loader.core.utils import remove_umlaut


class JSONToDataFrameFactory(PandasFactoryMixin):
    def __init__(self, json_path: str):
        self._json_path = json_path
        self._interface = JSONPandasInterface()

    @property
    def json_path(self) -> str:
        return self._json_path

    @property
    def interface(self) -> JSONPandasInterface:
        return self._interface

    @property
    def date_parser(self) -> Callable:
        return self.interface.date_parser()

    def get_dataframe(self) -> PandasDataFrameType:
        interface = self.interface
        df = pd.read_json(
            self.json_path,
            orient=interface.orient,
            encoding=interface.encoding,
            convert_dates=False,
            keep_default_dates=interface.keep_default_dates,
            dtype=interface.get_nondate_dtypes(),
        )
        df.columns = [remove_umlaut(col.strip()) for col in df.columns]

        for column in interface.get_date_fields():
            df[column] = df[column].apply(self.date_parser)

        return df
