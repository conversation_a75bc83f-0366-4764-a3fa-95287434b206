import string

from loader.config import logger  # type: ignore

ALLOWED_CHARS = set(string.ascii_lowercase + string.digits + "." + "-_")


class ValidationError(Exception):
    pass


def validate_column_names_characters(df) -> bool:
    """
    Validate that the column names don't contain problematic characters
    """
    if not all(set(c) <= ALLOWED_CHARS for c in df.columns):
        return False
    return True


def validate_columns_dto(df, dto) -> bool:
    """
    Checks that all the elements of the fields of the dto are present in the dataframe
    """
    dto_fields = set([k.lower() for k, v in dto.__dataclass_fields__.items()])
    df_fields = set(k.strip() for k in df.columns)
    difference = dto_fields.difference(df_fields)

    logger.info("dto fields: {}".format(dto_fields))
    logger.info
    ("df fields: {}".format(df_fields))

    inverse_difference = df_fields.difference(dto_fields)
    if inverse_difference != {"chksum"}:  # Checksum is added during the ingestion process
        logger.warning("Columns in dataframe not present in dto: {}".format(inverse_difference))
        return False

    if difference == set():
        return True

    return False


def column_names_validation(df) -> None:
    if not validate_column_names_characters(df):
        raise ValidationError(
            "could not parse all columns for object {} ({})".format(df.s3object_key, df.s3object_e_tag)
        )
    return


def schema_validation(df, dto) -> None:
    if not validate_columns_dto(df, dto):
        raise ValidationError("could not validate schema for object {} ({})".format(df.s3object_key, df.s3object_e_tag))
    return
