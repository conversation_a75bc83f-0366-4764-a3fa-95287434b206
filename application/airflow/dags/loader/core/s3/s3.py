from datetime import date
from typing import Any, Dict, List


def add_or_update_processed_tag(tags) -> Dict:
    process_tag = {"Key": "processed", "Value": str(date.today())}
    filter_tags = [tag for tag in tags["TagSet"] if tag["Key"] != "processed"]
    new_tags = filter_tags + [process_tag]
    return {"TagSet": new_tags}


def get_all_objects_to_process(bucket) -> List:
    all_objects = bucket.objects.all()
    return list(all_objects)


def get_all_objects_in_path(bucket, prefix) -> List:
    objects = bucket.objects.filter(Prefix=prefix)
    return list(objects)


def get_object(bucket, key):
    s3_objects = bucket.objects.filter(Prefix=key)
    s3_objects_list = list(s3_objects)
    obj_len = len(s3_objects_list)
    if obj_len == 1:
        return s3_objects_list[0]
    elif obj_len > 1:
        raise Exception(f"More than one match found for {key}.")
    elif obj_len == 0:
        raise Exception(f"No match found for {key}.")


def read_s3_object(s3_client, bucket, key):
    return s3_client.get_object(Bucket=bucket, Key=key)["Body"].read()


def extract_tag_value_from_tags(tags, tag_key):
    tag_list = [tag["Value"] for tag in tags["TagSet"] if tag["Key"] == tag_key]
    return tag_list[0] if len(tag_list) > 0 else None


def extract_md5_hash_from_tags(tags) -> str:
    """Extracts the MD5 hash from a s3 object tags"""
    return extract_tag_value_from_tags(tags, "MD5")


def extract_mod_time_from_tags(tags) -> int:
    """Extract the date of creation on the FTP"""
    return int(extract_tag_value_from_tags(tags, "MOD_TIME"))


def extract_tag_value_from_s3object(s3_client, s3object, extraction_fn) -> Any:
    tags = s3_client.get_object_tagging(Bucket=s3object.bucket_name, Key=s3object.key)
    extracted_value = extraction_fn(tags)
    return extracted_value


def extract_md5_hash_from_s3object(s3_client, s3object) -> str:
    return extract_tag_value_from_s3object(s3_client, s3object, extract_md5_hash_from_tags)


def extract_mod_time_from_s3object(s3_client, s3object) -> int:
    return extract_tag_value_from_s3object(s3_client, s3object, extract_mod_time_from_tags)


def get_s3_file_path(s3object) -> str:
    return "s3://{}/{}".format(s3object.bucket_name, s3object.key)


def extract_bucket_and_key_from_s3_url(s3_url: str) -> (str, str):
    return s3_url.replace("s3://", "").split("/", 1)
