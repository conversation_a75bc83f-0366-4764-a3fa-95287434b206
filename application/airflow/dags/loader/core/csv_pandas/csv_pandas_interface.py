from typing import NewType

import numpy as np  # type: ignore

from loader.core.pandas import PandasInterfaceMixin


class CSVPandasInterface(PandasInterfaceMixin):
    def __init__(self):
        self._separator = ","
        self._encoding = "utf-8"
        self._header = 0
        self._decimal = "."
        self._thousands = ","
        self._dto = None

    @property
    def separator(self) -> str:
        return self._separator

    @property
    def encoding(self) -> str:
        return self._encoding


CSVPandasInterfaceType = NewType("CSVPandasInterfaceType", CSVPandasInterface)
