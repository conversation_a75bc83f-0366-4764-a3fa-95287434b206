import boto3
import io
import pandas as pd  # type: ignore
import re
import loader.config as config

from loader.core.pandas import PandasFactoryMixin, NA_VALUES
from loader.core.csv_pandas import CSVPandasInterface
from loader.core.custom_types import PandasDataFrameType
from loader.core.s3 import extract_bucket_and_key_from_s3_url
from loader.core.s3.s3 import read_s3_object
from loader.core.utils import remove_umlaut


class CSVToDataFrameFactory(PandasFactoryMixin):
    def __init__(self, csv_path: str):
        self._csv_path = csv_path
        self._interface = CSVPandasInterface()

    @property
    def csv_path(self) -> str:
        return self._csv_path

    @property
    def interface(self) -> CSVPandasInterface:
        return self._interface

    def preprocess_file(self) -> bytes:
        """
        Adds preprocessing to problematic files
        """
        s3_client = boto3.client("s3")
        bucket_name, file_path = extract_bucket_and_key_from_s3_url(self._csv_path)
        bytes_file = read_s3_object(s3_client, bucket_name, file_path)
        preprocessed_bytes_file = re.sub(b"\r(?!\n)", b"", bytes_file)
        return io.BytesIO(preprocessed_bytes_file)

    def needs_preprocessing(self) -> bool:
        """
        Checks if the file needs preprocessing based on filenames/regexes set in config
        """
        if isinstance(self._csv_path, str):
            for file_needs_preprocessing in config.FILE_NEEDS_PREPROCESSING_LIST:
                if re.compile(file_needs_preprocessing).match(self._csv_path.split("/")[-1]):
                    return True

        return False

    def get_dataframe(self) -> PandasDataFrameType:
        interface = self.interface

        if self.needs_preprocessing():
            file = self.preprocess_file()

        else:
            file = self._csv_path

        df = pd.read_csv(
            file,
            sep=interface.separator,
            encoding=interface.encoding,
            header=interface.header,
            parse_dates=interface.get_date_fields(),
            date_parser=interface.date_parser(),
            dtype=interface.get_nondate_dtypes(),
            decimal=interface.decimal,
            thousands=interface.thousands,
            na_values=NA_VALUES,
            keep_default_na=False,
        )
        df.columns = [remove_umlaut(col.strip()) for col in df.columns]
        return df


class CSVToDataFrameFactoryV2(PandasFactoryMixin):
    """V2 to include column renaming"""

    def __init__(self, csv_path: str):
        self._csv_path = csv_path
        self._interface = CSVPandasInterface()

    @property
    def csv_path(self) -> str:
        return self._csv_path

    @property
    def interface(self) -> CSVPandasInterface:
        return self._interface

    def get_dataframe(self) -> PandasDataFrameType:
        interface = self.interface
        df = pd.read_csv(
            self.csv_path,
            sep=interface.separator,
            encoding=interface.encoding,
            header=interface.header,
            parse_dates=interface.get_date_fields(),
            date_parser=interface.date_parser(),
            dtype=interface.get_nondate_dtypes(),
            decimal=interface.decimal,
            thousands=interface.thousands,
            na_values=NA_VALUES,
            keep_default_na=False,
        )

        source_key = interface.get_all_source_key()
        df.columns = [remove_umlaut(col.strip()) for col in df.columns]
        df.columns = [source_key.get(col, col) for col in df.columns]
        return df
