import re
from datetime import datetime
from typing import Dict, List

from sqlalchemy.sql import text  # type: ignore

import loader.config as config
from loader.core.csv_pandas import CSVToDataFrameFactory
from loader.core.custom_types import SqlAlchemySessionType
from loader.core.etl.file_types import file_types, legacy_load
from loader.core.models import FilesProcessed
from loader.core.s3 import (
    add_or_update_processed_tag,
    extract_md5_hash_from_s3object,
    extract_md5_hash_from_tags,
    extract_mod_time_from_s3object,
)
from loader.core.s3_pandas import add_s3_tags_to_df, read_magic_from_s3
from loader.core.sqlalchemy_utils import (
    create_iptiq_postgres_engine,
    create_schema_if_not_exist,
    get_sql_alchemy_session,
)
from loader.core.utils import convert_unix_ts_to_dt, get_folder_and_path
from loader.core.utils.dto import get_field_names_dto, get_sqlalchemy_types_from_dto
from loader.core.validations import column_names_validation, schema_validation
from pipeline.sink import sink_factory

logger = config.logger  # type: ignore


def get_filetype(folder: str, filename: str):
    """
    Extract the file type from approved file names

    :param folder: The folder where the file is stored
    :type folder: str
    :param filename: the filenane that needs to be checked for type
    :type filename: str
    """
    for file_type in file_types.get(folder, {}):
        m = re.match(file_type.get("regexp"), filename)
        if m:
            if file_type.get("category"):
                return file_type.get("category")
            else:
                raise Exception(f"The file type `{file_type.get('name')}` does not have a category.")


def get_all_categories(folder: str):
    """Returns all categories/filetypes in a given folder"""

    categories = [file_type.get("category") for file_type in file_types.get(folder, {})]
    return list(set(categories))


def get_applicable_factory(s3object):
    """
    Determines which factory method to use depending on the s3 object
    Checks if a factory method is provided in a dictory otherwise
    leverages CSVToDataFrameFactory as a default

    :param s3object: the s3 object intended to be uploaded to the db
    """
    folder, leftover_path = get_folder_and_path(s3object)
    # go through the different formats
    for file_type in file_types.get(folder, {}):
        m = re.match(file_type.get("regexp"), leftover_path)
        if m:
            applicable_factory_name = file_type.get("name")
            logger.info(f"Found matching factory: {applicable_factory_name}")
            return file_type.get("factory")
    logger.info("No matching factory found using default")
    return CSVToDataFrameFactory


def lower_case_df_columns(df, **_):
    df.columns = [c.lower() for c in df.columns]
    return df


def add_metadata_columns_to_df(df, s3object, **_):
    df["chksum"] = s3object.e_tag.replace('"', "")
    return df


def add_sql_alchemy_types_to_df_from_dto(df, dto, **_):
    sqlalchemy_types = get_sqlalchemy_types_from_dto(dto)
    # infer missing sql alchemy types as String
    from sqlalchemy import String

    for col in set(df.columns) - set(sqlalchemy_types):
        sqlalchemy_types[col] = String
    df.sqlalchemy_types = sqlalchemy_types
    return df


def check_mapping(columns, dto):
    """ Checks that the columns of the dataframe have been mapped as per DTO specifications """
    errors = ""
    dto_mapping = {x.metadata.get("source"): x.metadata.get("mapping") for x in dto.__dataclass_fields__.values()}
    for column in columns:
        if column not in dto_mapping.values():
            errors += f"'{column}' has not been mapped correctly\n"
    if errors:
        logger.info(f"Columns mapping has not been successful:\n {errors} ")


def convert_s3_to_df(s3_client, s3object):
    """ """
    factory = get_applicable_factory(s3object)
    dto = factory(None).interface.dto

    df = read_magic_from_s3(s3_client, s3object, factory).get_dataframe()
    check_mapping(list(df.columns), dto)
    dto_fields = get_field_names_dto(dto)
    df = df[dto_fields]
    df = add_metadata_columns_to_df(df=df, s3object=s3object)
    df = lower_case_df_columns(df=df)

    df = add_sql_alchemy_types_to_df_from_dto(df=df, dto=dto)
    df = add_s3_tags_to_df(df=df, s3object=s3object)

    column_names_validation(df)
    schema_validation(df, dto)

    return df


def insert_df_to_db(df, db_table_name, s3_client, tags, mode, loader_conf: dict) -> None:
    """
    Insert the content of the data frame into the database and marks the file has processed

    :param df: A pandas dataframe encirhced with s3object_bucket_name and s3object_key properties
    :type df: PandasDataFrameType
    :param db_table_name: the table name to upload to
    :type db_table_name: str
    :param s3_client: An S3 boto client
    :param tags: The tags currently attached to the s3 object
    :param loader_conf: Loader configuration

    """
    if mode == "legacy":
        engine = create_iptiq_postgres_engine(loader_conf=loader_conf)
        create_schema_if_not_exist(engine, loader_conf.get("db_name"), loader_conf.get("db_landing_schema"))

        with engine.begin() as conn:
            # This starts a transaction and tag the file
            logger.info("Insert the data to the following table {}".format(db_table_name))

            df.to_sql(
                name=db_table_name,
                schema=loader_conf.get("db_landing_schema"),  # type: ignore
                if_exists="append",
                con=conn,
                index=False,
                method="multi",
                chunksize=config.DB_CHUNK_SIZE,  # type: ignore
                dtype=df.sqlalchemy_types,
            )

    else:
        sink_conf = {
            "type": "postgres",
            "schema": loader_conf.get("db_landing_schema"),
            "user": loader_conf.get("db_user"),
            "password": loader_conf.get("db_password"),
            "host": loader_conf.get("db_host"),
            "port": loader_conf.get("db_port"),
            "db": loader_conf.get("db_name"),
        }

        with sink_factory(environment=config.DATALAKE_ENVIRONMENT, **sink_conf) as sink:
            sink.create_schema_if_not_exist()
            logger.info("Insert the data to the following table {}".format(db_table_name))

            with sink.engine.begin() as conn:
                from uuid import uuid4

                schema = sink_conf.get("schema")
                tmp_table = f"tmp_{db_table_name.lower()}_{str(uuid4())[:8]}"

                temporary = True

                sink.create_table_from_sqlalchemy_types(
                    config.Base,
                    conn,
                    tmp_table,
                    df.sqlalchemy_types,
                    schema,
                    temporary=temporary,
                )

                reordered_df = sink._reorder_based_on_target(conn, df, tmp_table, schema, temporary=temporary)

                sink._write_data_to_table_fast(
                    conn=conn,
                    data=reordered_df,
                    table=tmp_table,
                    schema=schema,
                    temporary=temporary,
                    chunk_size=config.DB_CHUNK_SIZE,
                )

                sink._insert_into_target_table(
                    conn=conn,
                    target_table=db_table_name,
                    target_schema=schema,
                    columns=reordered_df.columns,
                    source_table=tmp_table,
                    source_schema=schema,
                    temporary=temporary,
                )

    if config.TAG is not False:  # type: ignore
        s3_client.put_object_tagging(
            Bucket=df.s3object_bucket_name,
            Key=df.s3object_key,
            Tagging=add_or_update_processed_tag(tags),
        )


def has_file_been_processed(session: SqlAlchemySessionType, file_hash: str):
    """
    Checks whether a file has been marked as processed

    :param session: An active SQLAlchemy session
    :type session: SqlAlchemySessionType
    :param file_hash: The MD5 hash of the file
    :type file_hash: str
    """
    file = session.query(FilesProcessed).filter_by(file_hash=file_hash).one_or_none()
    file_processed = file.processed if file else False
    logger.info(f"Checking whether the file {file_hash} has been processed: {file_processed}")
    return file_processed


def has_file_processing_notification_within_threshold(session: SqlAlchemySessionType, file_hash: str):
    """
    Checks whether a file has been marked as to be processed
    Within the set threshold

    :param session: An active SQLAlchemy session
    :type session: SqlAlchemySessionType
    :param file_hash: The MD5 hash of the file
    :type file_hash: str
    :param loader_conf: Loader configuration
    :type loader_conf: dict
    """
    file = (
        session.query(FilesProcessed)
        .filter_by(file_hash=file_hash)
        .filter(
            text(
                f"processing_notification + interval '{config.NOTIFICATION_TRESHOLD_MINS}' minute > now()"
                # noqa: E501 type: ignore
            )
        )
        .one_or_none()
    )
    processing_notification = file.processing_notification if file else False
    logger.info(f"Cheking whether the file {file_hash} has a processing notification: {processing_notification}")
    return processing_notification


def register_processing_notification(session: SqlAlchemySessionType, s3object, file_hash: str) -> None:
    """
    Register a file as being in the process of being uploaded to the database

    :param session: An active SQLAlchemy session
    :type session: SqlAlchemySessionType
    :param s3object: The s3 object to mark as processed
    """
    logger.info(f"Registering processing notification for {s3object.bucket_name}/{s3object.key}")
    existing_file = session.query(FilesProcessed).filter_by(file_hash=file_hash).one_or_none()
    file_to_add = (
        existing_file if existing_file else FilesProcessed(file_hash=file_hash, chksum=s3object.e_tag.replace('"', ""))
    )
    file_to_add.bucket_name = s3object.bucket_name
    file_to_add.key = s3object.key
    file_to_add.processing_notification = datetime.utcnow()
    session.add(file_to_add)
    session.commit()


def register_fileload(session: SqlAlchemySessionType, s3_client, s3object, file_hash) -> None:
    """
    Register in the database the processing of the file

    :param session: An active SQLAlchemy session
    :type session: SqlAlchemySessionType
    :param s3object: The s3 object to mark as processed
    """
    logger.info(f"Registering the file upload for {s3object.bucket_name}/{s3object.key}")
    existing_file = session.query(FilesProcessed).filter_by(file_hash=file_hash).one_or_none()
    file_to_add = (
        existing_file if existing_file else FilesProcessed(file_hash=file_hash, chksum=s3object.e_tag.replace('"', ""))
    )
    file_to_add.bucket_name = s3object.bucket_name
    file_to_add.key = s3object.key

    source_mod_time = extract_mod_time_from_s3object(s3_client, s3object)
    file_to_add.source_created_on = convert_unix_ts_to_dt(source_mod_time)
    file_to_add.processed = True
    session.add(file_to_add)
    session.commit()


def handle_upload_s3_to_db(
    session: SqlAlchemySessionType, s3_client, s3object, db_table_name: str, mode, loader_conf: dict
) -> None:
    """
    Loads the data contained in a given s3 object to a database table
    Performs the necessary validation on top of the file

    :param session: An active SQLAlchemy session
    :type session: SqlAlchemySessionType
    :param s3_client: The client to access s3
    :param s3object: The s3 object to upload to the database
    :param db_table_name: The specific table name to upload the data to
    :type db_table_name: str
    :param loader_conf: Loader configuration
    :type loader_conf: dict
    """
    tags = s3_client.get_object_tagging(Bucket=s3object.bucket_name, Key=s3object.key)
    file_hash = extract_md5_hash_from_tags(tags)

    register_processing_notification(session, s3object, file_hash)
    df = convert_s3_to_df(s3_client, s3object)
    logger.debug(f"uploading object {s3object.key} ({s3object.e_tag})")
    insert_df_to_db(df, db_table_name, s3_client, tags, mode, loader_conf)
    register_fileload(session, s3_client, s3object, file_hash)


def check_if_valid_processing(folder, leftover_path):
    if folder not in config.FOLDER_PATHS:
        return False

    if leftover_path.split("/")[-1] in config.FILE_IGNORE_LIST:
        logger.info(f"File is not in the right format: {leftover_path}")
        return False
    return True


def should_file_be_processed(leftover_path, file_type, session, file_hash):
    if file_type and (not has_file_been_processed(session, file_hash)):
        processing_notification = has_file_processing_notification_within_threshold(session, file_hash)
        if processing_notification:
            logger.info(f"File is already being processed: {leftover_path}")
            return False
        return True
    elif file_type:
        logger.info(f"File has already been processed: {leftover_path}")
    return False


def extract_file_hash_from_s3_object(s3_client, s3object):
    file_hash = extract_md5_hash_from_s3object(s3_client, s3object)
    if not file_hash:
        raise Exception(f"Could not find md5 of {s3object}.")
    return file_hash


def load_s3_object(session, s3_client, s3object, loader_conf: dict):
    try:
        folder, leftover_path = get_folder_and_path(s3object)

        if not check_if_valid_processing(folder, leftover_path):
            return None

        file_hash = extract_file_hash_from_s3_object(s3_client, s3object)
        file_type = get_filetype(folder, leftover_path)
        mode = "legacy" if f"{folder}_{file_type}" in legacy_load else "fast"

        if should_file_be_processed(leftover_path, file_type, session, file_hash):
            db_table_name = "{}_{}".format(folder, file_type)
            handle_upload_s3_to_db(session, s3_client, s3object, db_table_name, mode, loader_conf)
        else:
            logger.warning(f"Wrong file type: {folder}/{leftover_path}")
            return None

        return {
            "updated_table": db_table_name,
            "key": s3object.key,
            "file_type": file_type,
        }

    except Exception as e:
        logger.error(
            "error processing file %s (%s)",
            s3object.key,
            s3object.e_tag,
            exc_info=e,
        )
        raise e


def process_s3_object_list(s3_client, s3_objects, loader_conf) -> List[Dict]:
    try:
        objects_to_process = s3_objects
        session = get_sql_alchemy_session(loader_conf)

        load_infos = []
        for s3object in objects_to_process:
            load_info = load_s3_object(session, s3_client, s3object, loader_conf)
            if load_info is not None:
                load_infos.append(load_info)

        return load_infos

    except Exception as e:
        logger.critical("unexpected error: %s", str(e), exc_info=e)
        raise e
