import loader.domcura.config.file_types as domcura
import loader.prima.config.file_types as prima
import loader.aerial.config.file_types as aerial
import loader.hector.config.file_types as hector
import loader.solera.config.file_types as solera
import loader.tuio.config.file_types as tuio
import loader.toni.config.file_types as toni
import loader.gallen.config.file_types as gallen


file_types = {
    "domcura": domcura.file_types,
    "prima": prima.file_types,
    "aerial": aerial.file_types,
    "hector": hector.file_types,
    "solera": solera.file_types,
    "tuio": tuio.file_types,
    "toni": toni.file_types,
    "gallen": gallen.file_types
}

legacy_load = set(domcura.legacy_loads + prima.legacy_loads + tuio.legacy_loads)
