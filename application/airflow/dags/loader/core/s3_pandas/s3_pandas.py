from loader.core.custom_types import PandasDataFrameType
from loader.core.s3 import get_s3_file_path


def s3_get_object(s3_client, s3object):
    return s3_client.get_object(Bucket=s3object.bucket_name, Key=s3object.key)["Body"].read()


def read_magic_from_s3(s3_client, s3object, csv_to_df_factory, **kwargs) -> PandasDataFrameType:
    """
    Reads file object from s3 and converts it to a dataframe

    :param s3_client: The Boto S3 Client to use to access the bucket
    :param s3object: The specific s3 object to download

    :return: DataFrame
    :rtype: PandasDataFrameType
    """
    s3_file_path = get_s3_file_path(s3object)
    df = csv_to_df_factory(s3_file_path)
    return df


def add_s3_tags_to_df(df, s3object) -> PandasDataFrameType:
    df.s3object_bucket_name = s3object.bucket_name
    df.s3object_key = s3object.key
    df.s3object_e_tag = s3object.e_tag
    return df
