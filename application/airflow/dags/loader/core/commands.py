import re

import boto3

import loader.config as config
from loader.core.etl import get_filetype, process_s3_object_list
from loader.core.s3 import get_all_objects_in_path, get_all_objects_to_process, get_object
from loader.core.utils import get_file_name, get_folder_and_path

s3 = boto3.resource("s3", endpoint_url=config.AWS_ENDPOINT_URL or "https://s3.eu-central-1.amazonaws.com")
s3_client = boto3.client("s3", endpoint_url=config.AWS_ENDPOINT_URL or "https://s3.eu-central-1.amazonaws.com")

logger = config.logger
logger.info(config.APP_NAME + " Launching")


def process_load_s3_object(key, loader_conf: dict):
    logger.info(f"Processing file {key}")
    bucket_str = loader_conf.get("bucket")
    bucket = s3.Bucket(bucket_str)
    s3_object = get_object(bucket, key)
    return process_s3_object_list(s3_client, [s3_object], loader_conf)


def process_load_s3_folder(folder, loader_conf: dict, exclude_regex=None):
    """Loads all objects in a folder

    Args:
        folder (str): path in the s3 bucket to load from
        exclude_regex (str): a regex to exclude files from the folder
        loader_conf (dict): Loader configuration

    Returns:
        list of updated tables
    """
    logger.info(f"Processing Folder {folder}")
    bucket_str = loader_conf.get("bucket")
    bucket = s3.Bucket(bucket_str)
    objects_to_process = get_all_objects_in_path(bucket, folder)

    # Filter objects that needs to be excluded
    if exclude_regex is not None:
        filtered_objects = []
        for s3_obj in objects_to_process:
            file_name = get_file_name(s3_obj, folder)
            if re.search(exclude_regex, file_name):
                logger.info(f"Excluding {file_name}")
                continue
            filtered_objects.append(s3_obj)
    else:
        filtered_objects = objects_to_process

    return process_s3_object_list(s3_client, filtered_objects, loader_conf)


def process_load_all(loader_conf: dict):
    bucket_str = loader_conf.get("bucket")
    bucket = s3.Bucket(bucket_str)
    logger.info(f"Loading all files in bucket {bucket}")
    objects_to_process = get_all_objects_to_process(bucket)
    return process_s3_object_list(s3_client, objects_to_process, loader_conf)


def process_load_all_for_table(table_name, loader_conf: dict):
    logger.info(f"Processing Table {table_name}")
    bucket_str = loader_conf.get("bucket")
    bucket = s3.Bucket(bucket_str)
    folder, category = table_name.split("_")[0], "_".join(table_name.split("_")[1:])
    objects_to_process = get_all_objects_in_path(bucket, folder)

    if not objects_to_process:
        logger.info("There are no files to process. Exiting.")

    obj_process = []
    for s3object in objects_to_process:
        folder, leftover_path = get_folder_and_path(s3object)
        if get_filetype(folder, leftover_path) == category:
            obj_process.append(s3object)

    if not obj_process:
        logger.info(f"Could not find a matching file_type in {folder} folder for {category}.")

    return process_s3_object_list(s3_client, obj_process, loader_conf)


def internal_command_handler(loader_conf: dict, command="load", folder=None, exclude_regex=None, file=None, table=None):
    """Runs commands for loader. Currently, the only command is load!

    Args:
        command (str): command to run (currently only 'load')
        folder (str): folder in s3 bucket to load from
        exclude_regex (str): a regex to exclude files from the folder
        file (str): an individual file address in s3 bucket to load
        table (str): the table for which the files should be loaded
        loader_conf (dict): Loader configuration

    Returns:
        list of updated tables
    """

    if command == "load":
        if folder:
            return process_load_s3_folder(folder, loader_conf, exclude_regex)
        elif file:
            return process_load_s3_object(file, loader_conf)
        elif table:
            return process_load_all_for_table(table, loader_conf)
        else:
            return process_load_all(loader_conf)
