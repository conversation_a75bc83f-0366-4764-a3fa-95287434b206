from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Integer, String  # type: ignore
from sqlalchemy.sql import func  # type: ignore
from loader.alembic_utils.alembic_config import Base, DEFAULT_DB_LANDING_SCHEMA  # type: ignore


class FilesProcessed(Base):
    __tablename__ = "files_processed"
    __table_args__ = {"schema": DEFAULT_DB_LANDING_SCHEMA}  # type: ignore

    id = Column(Integer, primary_key=True)

    bucket_name = Column(String)
    key = Column(String)
    file_hash = Column(String)
    chksum = Column(String)

    processing_notification = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean)

    source_created_on = Column(DateTime(timezone=True))

    created_on = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(String)
    updated_on = Column(DateTime(timezone=True), onupdate=func.now())
    updated_by = Column(String)
