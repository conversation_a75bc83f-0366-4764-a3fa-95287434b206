from loader.core.models.file_management import FilesProcessed
from loader.domcura.models import *
from loader.prima.models import *
from loader.aerial.models import *
from loader.hector.models import *
from loader.solera.models import *
from loader.tuio.models import *
from loader.gallen.models import *

__all__ = [
    "FilesProcessed",
    "PrimaPremiumSpain",
    "PrimaClaimsSpain",
    "PrimaComplaintsSpain",
    "DomcuraEfhRh",
    "DomcuraPremium",
    "DomcuraClaims",
    "DomcuraClaimsVanAmeyde",
    "PrimaPremium",
    "PrimaClaims",
    "PrimaSuspensionsReactivations",
    "PrimaComplaints",
    "DomcuraComplaints",
    "AerialPremium",
    "AerialClaims",
    "HectorPolicies",
    "HectorPremium",
    "HectorClaims",
    "HectorSanctions",
    "SoleraClaims",
    "SoleraPolicies",
    "SoleraPremium",
    "TuioPolicies",
    "GallenPolicies",
    "HectorClaimsVanAmeyde"
]
