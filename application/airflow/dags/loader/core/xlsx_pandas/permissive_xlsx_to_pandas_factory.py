import logging
import pandas as pd
import re

from loader.core.custom_types import PandasDataFrameType
from loader.core.pandas import NA_VALUES
from loader.core.utils import remove_umlaut
from loader.core.xlsx_pandas import XLSXToDataFrameFactory


class PermissiveXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    """The XLSX reader that ignores the sheets whose name is not available in the file"""
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)

    def get_dataframe(self) -> PandasDataFrameType:
        interface = self.interface

        xlsx_sheet_names = pd.ExcelFile(self.xlsx_path).sheet_names
        available_sheet_names = [s for s in interface.sheet_names if s in xlsx_sheet_names]
        logging.info(f"The following requested sheets are available in the file: {available_sheet_names} "
                     f"(requested sheet names list: {interface.sheet_names})")

        assert len(available_sheet_names) > 0, "None of the requested sheet names is available in the file."

        df = pd.concat(
            pd.read_excel(
                self._xlsx_path,
                header=interface.header,
                parse_dates=interface.get_date_fields(),
                date_parser=interface.date_parser(),
                dtype=interface.get_nondate_dtypes(),
                thousands=interface.thousands,
                skipfooter=interface.skipfooterint,
                skiprows=interface.skiprows,
                sheet_name=sheet_name,
                na_values=NA_VALUES,
                keep_default_na=False,
            )
            for sheet_name in available_sheet_names
        )

        if isinstance(interface.header, list):
            df.columns = (
                pd.Series(df.columns.get_level_values(0)).apply(str)
                + " "
                + pd.Series(df.columns.get_level_values(1)).apply(str)
            )
            df.columns = [re.sub(" +", " ", col) for col in df.columns]
            df.columns = [col.lower() for col in df.columns]

        source_key = interface.get_all_source_key()
        df.columns = [remove_umlaut(col.strip()) for col in df.columns]
        df.columns = [source_key.get(col, col) for col in df.columns]
        return df
