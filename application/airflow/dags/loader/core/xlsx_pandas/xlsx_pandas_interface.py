from typing import NewType

import numpy as np  # type: ignore

from loader.core.pandas import PandasInterfaceMixin


class XLSXPandasInterface(PandasInterfaceMixin):
    def __init__(self):
        self._header: int = 1
        self._decimal: str = "."
        self._thousands: str = ","
        self._skipfooterint: int = 0
        self._skiprows: int = 0
        self._sheet_names = [0]
        self._dto = None

    @property
    def sheet_names(self):
        return self._sheet_names


XLSXPandasInterfaceType = NewType("XLSXPandasInterfaceType", XLSXPandasInterface)
