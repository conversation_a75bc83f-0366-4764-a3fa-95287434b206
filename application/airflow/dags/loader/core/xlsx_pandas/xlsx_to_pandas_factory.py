import re

import pandas as pd  # type: ignore

from loader.core.custom_types import PandasDataFrameType
from loader.core.pandas import NA_VALUES, PandasFactoryMixin
from loader.core.utils import remove_umlaut
from loader.core.xlsx_pandas.xlsx_pandas_interface import XLSXPandasInterface


class XLSXToDataFrameFactory(PandasFactoryMixin):
    def __init__(self, xlsx_path: str):
        self._xlsx_path = xlsx_path
        self._interface = XLSXPandasInterface()

    @property
    def xlsx_path(self) -> str:
        return self._xlsx_path

    @property
    def interface(self) -> XLSXPandasInterface:
        return self._interface

    @property
    def na_values(self):
        return NA_VALUES

    def get_dataframe(self) -> PandasDataFrameType:
        interface = self.interface

        df = pd.concat(
            pd.read_excel(
                self._xlsx_path,
                header=interface.header,
                parse_dates=interface.get_date_fields(),
                date_parser=interface.date_parser(),
                dtype=interface.get_nondate_dtypes(),
                thousands=interface.thousands,
                decimal=interface.decimal,
                skipfooter=interface.skipfooterint,
                skiprows=interface.skiprows,
                sheet_name=sheet_name,
                na_values=self.na_values,
                keep_default_na=False,
            )
            for sheet_name in interface.sheet_names
        )
        if isinstance(interface.header, list):
            df.columns = (
                pd.Series(df.columns.get_level_values(0)).apply(str)
                + " "
                + pd.Series(df.columns.get_level_values(1)).apply(str)
            )
            df.columns = [re.sub(" +", " ", col) for col in df.columns]
            df.columns = [col.lower() for col in df.columns]

        source_key = interface.get_all_source_key()
        df.columns = [remove_umlaut(col.strip()) for col in df.columns]
        df.columns = [source_key.get(col, col) for col in df.columns]
        return df
