from typing import Dict, List

from sqlalchemy import <PERSON><PERSON><PERSON>, DateT<PERSON>, Integer, String  # type: ignore
from sqlalchemy.sql import func  # type: ignore


def get_sqlalchemy_types_from_dto(dto) -> Dict:
    """
    Extract a dicitionary of {field name: sqlalchemy type} from a dataclass metadata

    :param dto: input dataclass to extract the data from
    :type dto: dataclass

    :rtype: Dict
    """
    sqlalchemy_types = {k.lower(): v.metadata.get("sqlalchemy_type") for k, v in dto.__dataclass_fields__.items()}
    return sqlalchemy_types


def get_source_dtypes_from_dto(dto) -> Dict:
    """
    Extract a dicitionary of {source: dtype} from a dataclass metadata

    :param dto: input dataclass to extract the data from
    :type dto: dataclass

    :rtype: Dict
    """
    dtypes = {v.metadata.get("source"): v.metadata.get("dtype") for k, v in dto.__dataclass_fields__.items()}
    return dtypes


def get_source_key_from_dto(dto) -> Dict:
    """
    Extract a dicitionary of {source: key} from a dataclass metadata

    :param dto: input dataclass to extract the data from
    :type dto: dataclass

    :rtype: Dict
    """
    dtypes = {v.metadata.get("source"): k for k, v in dto.__dataclass_fields__.items()}
    return dtypes


def get_field_names_dto(dto) -> List:
    """
    Extract a lis of field names from the dataclass

    :param dto: input dataclass to extract the data from
    :type dto: dataclass

    :rtype: List
    """
    field_names = [k for k, v in dto.__dataclass_fields__.items()]
    return field_names


def get_sql_alchemy_dict_from_dto(dto) -> Dict:
    """
    Extracts a field type / dictionary used to generate a SQLAlchemy model

    :param dto: input dataclass to extract the data from
    :type dto: dataclass

    :rtype: Dict
    """
    sql_alchemy_dict = {
        k.lower(): Column(v.metadata.get("sqlalchemy_type")) for k, v in dto.__dataclass_fields__.items()
    }
    return sql_alchemy_dict


def generate_table_model_from_dtos(table_name: str, dtos: List, schema: str) -> Dict:
    """
    :param tablename: Table name for the created table
    :param dtos: List of DTOs to use to generate the model
    :param schema: Schema to create the table in
    """
    attr_dict: Dict = {
        "__tablename__": table_name,
        "__table_args__": {"schema": schema},
        "id": Column(Integer, primary_key=True),
        "chksum": Column(String),
        "created_on": Column(DateTime(timezone=True), server_default=func.now()),
    }
    dtos_list: List = [get_sql_alchemy_dict_from_dto(x) for x in dtos]

    for dto in dtos_list:
        attr_dict.update(dto)
    return attr_dict
