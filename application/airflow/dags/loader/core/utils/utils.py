from datetime import datetime


def get_folder_and_path(s3object):
    path = s3object.key.split("/")
    if len(path) <= 1:
        raise Exception("Couldn't locate a file folder")
    return path[0], "/".join(path[1:])


def get_file_name(s3object, folder):
    """Returns the file address in the given folder"""
    path = s3object.key
    if not path.startswith(folder):
        raise Exception("The object is not in the specified folder")
    return path[len(folder):].lstrip("/")


def remove_umlaut(string: str) -> str:
    string = string.replace(u"ü", "ue")
    string = string.replace(u"Ü", "Ue")
    string = string.replace("Ã\x9c", "Ue")
    string = string.replace(u"ä", "ae")
    string = string.replace("Ã¤", "ae")
    string = string.replace(u"Ä", "Ae")
    string = string.replace(u"ö", "oe")
    string = string.replace("Ã¶", "oe")
    string = string.replace(u"Ö", "Oe")
    string = string.replace(u"ß", "ss")
    string = string.replace(u"…", "...")
    string = string.replace(u"€", "EUR")
    string = string.replace(u"’ ", " ")
    return string


def convert_unix_ts_to_dt(ts):
    return datetime.utcfromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
