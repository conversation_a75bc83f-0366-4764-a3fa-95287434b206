from operator import itemgetter
from typing import Optional, SupportsInt, Union

from sqlalchemy import create_engine, schema  # type: ignore
from sqlalchemy.orm import sessionmaker  # type: ignore

import loader.config as config
from loader.config import logger  # type: ignore
from loader.core.custom_types import SqlAlchemyEngineType


def create_schema_if_not_exist(engine: SqlAlchemyEngineType, db_name: str, db_landing_schema: str) -> None:
    """
    Creates a schema in a database if it doesn't already exists

    :param engine: The SQL Alchemy engine to use to connect to the database
    :type engine: SqlAlchemyEngineType
    :param db_name: The database to use for the connection
    :type db_name: str
    :param db_landing_schema: The name of the schema to check existance of
    :type db_landing_schema: str

    :return: None
    """
    logger.debug("checking if schema %s exists", db_landing_schema)
    if not engine.dialect.has_schema(engine, db_landing_schema):
        logger.warning("schema %s not found in db %s, creating", db_landing_schema, db_name)
        engine.execute(schema.CreateSchema(db_landing_schema))
    else:
        logger.debug("schema %s exists", db_landing_schema)


def create_sql_alchemy_url(
    loader_conf: dict,  # type: ignore #type: ignore
) -> str:
    """
    :param loader_conf: Loader configuration
    :type loader_conf: dict
    """
    db_user, db_password, db_host, db_port, db_name = itemgetter(
        "db_user", "db_password", "db_host", "db_port", "db_name"
    )(loader_conf)
    return "postgresql://{}:{}@{}:{}/{}".format(db_user, db_password, db_host, db_port, db_name)


def create_iptiq_postgres_engine(
    loader_conf: dict,  # type: ignore
) -> SqlAlchemyEngineType:
    """
    Creates a SQLAlchemy Engine with the necessary credentials for accessing
    an iptiQ postgres database

    :param loader_conf: Loader configuration
    :type loader_conf: dict

    :return: SQL Alchemy engine to connect to an iptiq postgres db
    :rtype: SqlAlchemyEngineType
    """
    logger.info("starting process...")
    uri = create_sql_alchemy_url(
        loader_conf=loader_conf,
    )
    engine = create_engine(uri)
    return engine


def get_sql_alchemy_session(loader_conf: dict) -> SqlAlchemyEngineType:
    """
    Creates a SQLAlchemy Session

    :param loader_conf: Loader configuration
    :type loader_conf: dict
    """
    engine = create_iptiq_postgres_engine(loader_conf=loader_conf)
    create_schema_if_not_exist(engine, loader_conf.get("db_name"), loader_conf.get("db_landing_schema"))  # type: ignore
    # create the tables
    config.Base.metadata.create_all(engine)  # type: ignore

    Session = sessionmaker(bind=engine)
    session = Session()
    return session
