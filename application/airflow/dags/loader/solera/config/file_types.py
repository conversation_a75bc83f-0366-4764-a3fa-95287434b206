from loader.solera.factory import *

file_types = [
    {
        "name": "solera_csv_policy",
        "factory": SoleraPolisCSVToDataFrameFactory,
        "regexp": r"^(.+)_polis_(\d{6})_[A-Z](\d{3})_(\d{2})\.csv$",
        "category": "policies",
    },
    {
        "name": "solera_csv_covers",
        "factory": SoleraDekkingCSVToDataFrameFactory,
        "regexp": r"^(.+)_polis_dekking_(\d{6})_[A-Z](\d{3})_(\d{2})\.csv$",
        "category": "covers",
    },
    {
        "name": "solera_csv_insured_objects",
        "factory": SoleraXoCSVToDataFrameFactory,
        "regexp": r"^(.+)_polis_XO_(\d{6})_[A-Z](\d{3})_(\d{2})\.csv$",
        "category": "insured_objects",
    },
    {
        "name": "solera_csv_claims",
        "factory": SoleraSchadeCSVToDataFrameFactory,
        "regexp": r"^(.+)_schade_(\d{6})_[A-Z](\d{3})_(\d{2})\.csv$",
        "category": "claims",
    },
    {
        "name": "solera_csv_claim_details",
        "factory": SoleraSchadeFTCSVToDataFrameFactory,
        "regexp": r"^(.+)_schade_ft_(\d{6})_[A-Z](\d{3})_(\d{2})\.csv$",
        "category": "claim_details",
    },
    {
        "name": "solera_csv_premium",
        "factory": SoleraPremiumFTCSVToDataFrameFactory,
        "regexp": r"^(.+)_FINANCIEEL_FT_(\d{6})_\d{1,2}\.csv$",
        "category": "premium",
    },
    {
        "name": "solera_csv_premium",
        "factory": SoleraPremiumALCSVToDataFrameFactory,
        "regexp": r"^(.+)_FINANCIEEL_AL_(\d{6})_\d{1,2}\.csv$",
        "category": "financial",
    },
]
