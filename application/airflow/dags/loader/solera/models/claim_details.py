from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore

from loader.solera.dto.claims import SoleraCSVSchadeFTDTO

from loader.core.utils.dto import generate_table_model_from_dtos


attr_dict = generate_table_model_from_dtos(
    "solera_claim_details",
    [SoleraCSVSchadeFTDTO],
    schema=DEFAULT_DB_LANDING_SCHEMA
)
SoleraClaimDetails = type("SoleraClaimDetails", (Base,), attr_dict)
