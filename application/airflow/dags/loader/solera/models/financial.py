from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore

from loader.solera.dto.premium import SoleraCSVPremiumALDTO

from loader.core.utils.dto import generate_table_model_from_dtos


attr_dict = generate_table_model_from_dtos(
    "solera_financial",
    [SoleraCSVPremiumALDTO],
    schema=DEFAULT_DB_LANDING_SCHEMA
)
SoleraFinancial = type("SoleraFinancial", (Base,), attr_dict)
