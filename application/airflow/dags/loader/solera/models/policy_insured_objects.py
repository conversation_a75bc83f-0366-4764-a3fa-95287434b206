from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore

from loader.solera.dto.policy import SoleraCSVXoDTO

from loader.core.utils.dto import generate_table_model_from_dtos


attr_dict = generate_table_model_from_dtos("solera_insured_objects", [SoleraCSVXoDTO], schema=DEFAULT_DB_LANDING_SCHEMA)
SoleraInsuredObjects = type("SoleraInsuredObjects", (Base,), attr_dict)
