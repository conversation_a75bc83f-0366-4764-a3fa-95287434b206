from typing import Callable
from loader.core.csv_pandas import CSVPandasInterface


class SoleraCSVPandasInterface(CSVPandasInterface):
    # some edit need to be change for when leveraging other DTOs / filetype
    def __init__(self, dto):
        self._separator = ";"
        self._encoding = "cp1252"
        self._header = 0
        self._decimal = "."
        self._thousands = ","
        self._dto = dto()

    @classmethod
    def date_parser(cls) -> Callable:
        from dateutil.parser import parse

        skip_list = ["nan", "None", "00000000"]

        return lambda x: parse(str(x)) if str(x) not in skip_list else None
