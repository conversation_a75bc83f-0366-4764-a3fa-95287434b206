#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVPremiumFTDTO:
    id_premium: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "id_premium",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for the payment",
        }
    )
    contract_id: str = dataclasses.field(
        metadata={
            "source": "volmacht_id",
            "mapping": "contract_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for the contract",
        }
    )

    transaction_type: str = dataclasses.field(
        metadata={
            "source": "ft_trnsrt",
            "mapping": "transaction_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Code indicating the type of transaction",
        }
    )

    booking_date: str = dataclasses.field(
        metadata={
            "source": "ft_boekdat",
            "mapping": "booking_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date on which the booking took place",
        }
    )

    closed_commission_amount: str = dataclasses.field(
        metadata={
            "source": "ft_tafsprv",
            "mapping": "closed_commission_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Closed commission amount",
        }
    )

    ongoing_commission_amount: str = dataclasses.field(
        metadata={
            "source": "ft_tdrlprv",
            "mapping": "ongoing_commission_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Ongoing commission amount",
        }
    )

    insurer_commission_amount: str = dataclasses.field(
        metadata={
            "source": "ft_ttekcom",
            "mapping": "insurer_commission_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Coverholder commission amount",
        }
    )

    gross_written_premium: str = dataclasses.field(
        metadata={
            "source": "ft_tpremie",
            "mapping": "gross_written_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Gross written premium",
        }
    )

    total_incurred: str = dataclasses.field(
        metadata={
            "source": "ft_tschbdr",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "ft_tsvhbdr",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovered amount",
        }
    )

    reserved: str = dataclasses.field(
        metadata={
            "source": "ft_schrsrv",
            "mapping": "reserved",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Reserved amount",
        }
    )

    frequency: str = dataclasses.field(
        metadata={
            "source": "ft_betterm",
            "mapping": "frequency",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Payment term in months",
        }
    )

    payment_period_start: str = dataclasses.field(
        metadata={
            "source": "ft_tdatvan",
            "mapping": "payment_period_start",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Payment period start",
        }
    )

    payment_period_end: str = dataclasses.field(
        metadata={
            "source": "ft_tdattm",
            "mapping": "payment_period_end",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Payment period end",
        }
    )

    branch_code: str = dataclasses.field(
        metadata={
            "source": "ft_gabra",
            "mapping": "branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code of the authorized agent",
        }
    )

    branch_desc: str = dataclasses.field(
        metadata={
            "source": "ft_gabrao",
            "mapping": "branch_desc",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code description of the authorized agent",
        }
    )

    sub_branch_code: str = dataclasses.field(
        metadata={
            "source": "ft_gasbra",
            "mapping": "sub_branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subbranch code of the authorized agent",
        }
    )

    sub_branch_desc: str = dataclasses.field(
        metadata={
            "source": "ft_gasbrao",
            "mapping": "sub_branch_desc",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subbranch description of the authorized agent",
        }
    )

    cover_type: str = dataclasses.field(
        metadata={
            "source": "ft_gadekcd",
            "mapping": "cover_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Coverage code of the authorized agent",
        }
    )

    cover_description: str = dataclasses.field(
        metadata={
            "source": "ft_gadekco",
            "mapping": "cover_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Coverage code description of the authorized agent.",
        }
    )

    iptiq_branch_code: str = dataclasses.field(
        metadata={
            "source": "ft_vgbra",
            "mapping": "iptiq_branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Proxy branch code",
        }
    )

    iptiq_mapping_code: str = dataclasses.field(
        metadata={
            "source": "ms_newbran",
            "mapping": "iptiq_mapping_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurer mapping code",
        }
    )

    proxy_code: str = dataclasses.field(
        metadata={
            "source": "ft_vgcode",
            "mapping": "proxy_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Proxy code",
        }
    )

    policy_number: str = dataclasses.field(
        metadata={
            "source": "pp_nummer",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy number",
        }
    )

    internal_key: str = dataclasses.field(
        metadata={
            "source": "pp_intkey",
            "mapping": "internal_key",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The internal key is the identifying data under which the contract is stored in the "
            "contract administration.",
        }
    )

    package_number: str = dataclasses.field(
        metadata={
            "source": "pk_nummer",
            "mapping": "package_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Package number",
        }
    )

    contract_number: str = dataclasses.field(
        metadata={
            "source": "rc_nummer",
            "mapping": "contract_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Contract number",
        }
    )

    license_number: str = dataclasses.field(
        metadata={
            "source": "tp_afmvrgn",
            "mapping": "license_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "AFM License Number",
        }
    )

    identification_number: str = dataclasses.field(
        metadata={
            "source": "tp_idnr",
            "mapping": "identification_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification number",
        }
    )

    agent_claim_number: str = dataclasses.field(
        metadata={
            "source": "sd_schnrvm",
            "mapping": "agent_claim_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Agent identifier of each claim",
        }
    )

    tpa_number: str = dataclasses.field(
        metadata={
            "source": "sd_schnrvg",
            "mapping": "tpa_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim number under which this file is known to the insurer/agent",
        }
    )

    occurrence_date: str = dataclasses.field(
        metadata={
            "source": "sd_schadat",
            "mapping": "occurrence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim Event date - When the event = cause took place",
        }
    )
