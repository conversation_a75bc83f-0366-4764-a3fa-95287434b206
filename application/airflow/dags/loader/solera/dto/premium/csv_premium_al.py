#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVPremiumALDTO:
    contract_id: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "contract_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for the contract run",
        }
    )

    version_number: str = dataclasses.field(
        metadata={
            "source": "al_versiev",
            "mapping": "version_number",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "This allows the recipient to see which version of the view the message is based on",
        }
    )

    sender_reference: str = dataclasses.field(
        metadata={
            "source": "al_cpref",
            "mapping": "sender_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Indication of person or file at the sending party that can be referred to "
                           "in further communication.",
        }
    )

    application_name: str = dataclasses.field(
        metadata={
            "source": "al_applnm",
            "mapping": "application_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the application sending the data.",
        }
    )

    application_version: str = dataclasses.field(
        metadata={
            "source": "al_applvs",
            "mapping": "application_version",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Version of the application sending the data.",
        }
    )

    reporting_period_from: str = dataclasses.field(
        metadata={
            "source": "al_rdtmvan",
            "mapping": "reporting_period_from",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Reporting date from",
        }
    )

    reporting_period_to: str = dataclasses.field(
        metadata={
            "source": "al_rdtmtm",
            "mapping": "reporting_period_to",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Reporting date to",
        }
    )

    name: str = dataclasses.field(
        metadata={
            "source": "ve_anaam",
            "mapping": "name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the partner sending the data.",
        }
    )

    additional_name: str = dataclasses.field(
        metadata={
            "source": "ve_tweeded",
            "mapping": "additional_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Additional naming for the partner, if longer than 35 characters.",
        }
    )

    telephone: str = dataclasses.field(
        metadata={
            "source": "ve_telnum",
            "mapping": "telephone",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Phone number for contact",
        }
    )

    company_code: str = dataclasses.field(
        metadata={
            "source": "ve_myaand",
            "mapping": "company_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification of the insurance company. Applies to companies and authorized agents",
        }
    )

    identification_number: str = dataclasses.field(
        metadata={
            "source": "ve_idnr",
            "mapping": "identification_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification number",
        }
    )

    insurer_name: str = dataclasses.field(
        metadata={
            "source": "vl_anaam",
            "mapping": "insurer_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the insuring partner.",
        }
    )

    insurer_additional_name: str = dataclasses.field(
        metadata={
            "source": "vl_tweeded",
            "mapping": "insurer_additional_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Additional naming for the insuring partner, if longer than 35 characters.",
        }
    )

    insurer_telephone: str = dataclasses.field(
        metadata={
            "source": "vl_telnum",
            "mapping": "insurer_telephone",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Phone number for the insuring partner",
        }
    )

    insurer_company_code: str = dataclasses.field(
        metadata={
            "source": "vl_myaand",
            "mapping": "insurer_company_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification of the insurance company. Applies to companies and authorized agents",
        }
    )

    insurer_identification_number: str = dataclasses.field(
        metadata={
            "source": "vl_veidnr",
            "mapping": "insurer_identification_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification number by which the agent is known to the principal.",
        }
    )
