#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVXoDTO:

    id_xo: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "id_xo",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    id_polis: str = dataclasses.field(
        metadata={
            "source": "polis_id",
            "mapping": "id_polis",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    serial_number: str = dataclasses.field(
        metadata={
            "source": "xo_volgnum",
            "mapping": "serial_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "If the entity occurs repeatedly, then the combination entity code + sequence number makes "
            "the entity unique. "
            "Mandatory fill with '1' if the same entity does not occur more than once.",
        }
    )

    license_plate: str = dataclasses.field(
        metadata={
            "source": "xo_kenteke",
            "mapping": "license_plate",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "License Plate",
        }
    )

    reporting_code: str = dataclasses.field(
        metadata={
            "source": "xo_mldcode",
            "mapping": "reporting_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Reporting Code",
        }
    )

    vehicle_brand: str = dataclasses.field(
        metadata={
            "source": "xo_merk",
            "mapping": "vehicle_brand",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the manufacturer",
        }
    )

    vehicle_model: str = dataclasses.field(
        metadata={
            "source": "xo_type",
            "mapping": "vehicle_model",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Specific model of the brand",
        }
    )

    construction_year: str = dataclasses.field(
        metadata={
            "source": "xo_bouwjr",
            "mapping": "construction_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Year of the manufacturing",
        }
    )

    type_of_fuel: str = dataclasses.field(
        metadata={
            "source": "xo_bransto",
            "mapping": "type_of_fuel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Fuel type code",
        }
    )

    weight_in_kg: str = dataclasses.field(
        metadata={
            "source": "xo_afmwe",
            "mapping": "weight_in_kg",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Weight in kilograms",
        }
    )

    annual_mileage: str = dataclasses.field(
        metadata={
            "source": "xo_afmkm",
            "mapping": "annual_mileage",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Annual mileage in kilometers",
        }
    )

    use_description: str = dataclasses.field(
        metadata={
            "source": "xo_toegebr",
            "mapping": "use_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Use description - related to Home data",
        }
    )

    building_object_code: str = dataclasses.field(
        metadata={
            "source": "xo_aardobj",
            "mapping": "building_object_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Codes that map to an object description",
        }
    )

    wall_type_code: str = dataclasses.field(
        metadata={
            "source": "xo_aardbw",
            "mapping": "wall_type_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Codes that map to a wall type description",
        }
    )

    roof_type_code: str = dataclasses.field(
        metadata={
            "source": "xo_aarddak",
            "mapping": "roof_type_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Codes that map to a roof type description",
        }
    )

    floor_type_code: str = dataclasses.field(
        metadata={
            "source": "xo_aardvlr",
            "mapping": "floor_type_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Codes that map to a floor type description",
        }
    )

    state_of_maintenance: str = dataclasses.field(
        metadata={
            "source": "xo_ondstt",
            "mapping": "state_of_maintenance",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "State of Maintenance",
        }
    )

    zoning_description_code: str = dataclasses.field(
        metadata={
            "source": "xo_bestemc",
            "mapping": "zoning_description_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Zoning code",
        }
    )

    zoning_description: str = dataclasses.field(
        metadata={
            "source": "xo_bestemo",
            "mapping": "zoning_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Zoning description.",
        }
    )

    capacity_coded: str = dataclasses.field(
        metadata={
            "source": "xo_hdnghdc",
            "mapping": "capacity_coded",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Capacity Coded",
        }
    )

    capacity_description: str = dataclasses.field(
        metadata={
            "source": "xo_hdnghdo",
            "mapping": "capacity_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Capacity description",
        }
    )

    initial_value: str = dataclasses.field(
        metadata={
            "source": "xo_vwaca",
            "mapping": "initial_value",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Original value at time of issue",
        }
    )

    current_value: str = dataclasses.field(
        metadata={
            "source": "xo_dagwrde",
            "mapping": "nature_of_contract",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Classification of the type of insurance contract - description",
        }
    )

    tax_inlcuded: str = dataclasses.field(
        metadata={
            "source": "xo_btwcd",
            "mapping": "tax_inlcuded",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "I (Included) or E(excluded)",
        }
    )

    house_number: str = dataclasses.field(
        metadata={
            "source": "xo_huisnr",
            "mapping": "house_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "House number",
        }
    )

    house_number_addition: str = dataclasses.field(
        metadata={
            "source": "xo_toevoeg",
            "mapping": "house_number_addition",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "House number addition",
        }
    )

    postal_code: str = dataclasses.field(
        metadata={
            "source": "xo_pcode",
            "mapping": "postal_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Postal code",
        }
    )
