#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVPolisDTO:
    id_polis: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "id_polis",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    contract_id: str = dataclasses.field(
        metadata={
            "source": "volmacht_id",
            "mapping": "contract_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for the contract",
        }
    )

    contract_number: str = dataclasses.field(
        metadata={
            "source": "pp_nummer",
            "mapping": "contract_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Contract number",
        }
    )

    internal_key: str = dataclasses.field(
        metadata={
            "source": "pp_intkey",
            "mapping": "internal_key",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The internal key is the identifying data under which the contract is stored in the "
            "contract administration.",
        }
    )

    branch_code: str = dataclasses.field(
        metadata={
            "source": "pp_gabra",
            "mapping": "branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code of the authorized agent",
        }
    )

    branch_desc: str = dataclasses.field(
        metadata={
            "source": "pp_gabrao",
            "mapping": "branch_desc",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code description of the authorized agent",
        }
    )

    sub_branch_code: str = dataclasses.field(
        metadata={
            "source": "pp_gasbra",
            "mapping": "sub_branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subbranch code of the authorized agent",
        }
    )

    sub_branch_desc: str = dataclasses.field(
        metadata={
            "source": "pp_gasbrao",
            "mapping": "sub_branch_desc",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subbranch description of the authorized agent",
        }
    )

    proxy_code: str = dataclasses.field(
        metadata={
            "source": "pp_vgcode",
            "mapping": "proxy_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Proxy code",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "pp_ingdat",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy start date",
        }
    )

    policy_duration: str = dataclasses.field(
        metadata={
            "source": "pp_cduumnd",
            "mapping": "policy_duration",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy duration",
        }
    )

    period_end_date: str = dataclasses.field(
        metadata={
            "source": "pp_hvvdat",
            "mapping": "period_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Period end date",
        }
    )

    frequency: str = dataclasses.field(
        metadata={
            "source": "pp_betterm",
            "mapping": "frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment frequency",
        }
    )

    date_last_modified: str = dataclasses.field(
        metadata={
            "source": "pp_lwyzdat",
            "mapping": "date_last_modified",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date on which the last amendment was registered/noted on the contract",
        }
    )

    policy_end_date: str = dataclasses.field(
        metadata={
            "source": "pp_enddatc",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy end date",
        }
    )

    policy_status: str = dataclasses.field(
        metadata={
            "source": "pp_status",
            "mapping": "policy_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy status",
        }
    )

    premium_yearly_net: str = dataclasses.field(
        metadata={
            "source": "pp_njp",
            "mapping": "premium_yearly_net",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Net annual premium",
        }
    )

    is_part_of_package: str = dataclasses.field(
        metadata={
            "source": "pp_polpkjn",
            "mapping": "is_part_of_package",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Indication to indicate that the policy is part of a package",
        }
    )

    is_coinsured: str = dataclasses.field(
        metadata={
            "source": "pp_coassjn",
            "mapping": "is_coinsured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Indication to indicate that the policy is part of a co-insurance",
        }
    )

    is_part_of_distribution: str = dataclasses.field(
        metadata={
            "source": "pp_speclim",
            "mapping": "is_part_of_distribution",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "There is a special limit when an authorized office cannot make the acceptance itself, "
            "but must request permission for this from the company",
        }
    )

    nature_of_contract_code: str = dataclasses.field(
        metadata={
            "source": "pp_branche",
            "mapping": "nature_of_contract_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Classification of the type of insurance contract - code.",
        }
    )

    nature_of_contract: str = dataclasses.field(
        metadata={
            "source": "pp_afddefn",
            "mapping": "nature_of_contract",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Classification of the type of insurance contract - description",
        }
    )

    contract_version_number: str = dataclasses.field(
        metadata={
            "source": "pp_adefvrs",
            "mapping": "contract_version_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The AFD version of the product",
        }
    )

    company_code: str = dataclasses.field(
        metadata={
            "source": "pp_myaand",
            "mapping": "company_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification of the insurance company. Applies to companies and authorized agents",
        }
    )

    n_of_claim_free_years: str = dataclasses.field(
        metadata={
            "source": "pp_schdvry",
            "mapping": "n_of_claim_free_years",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The number of claim-free years as stated on the bonus/malus or no-claim statement",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "pp_tjrpp",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    underwriting_year_pool: str = dataclasses.field(
        metadata={
            "source": "pp_tjrpo",
            "mapping": "underwriting_year_pool",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    contract_package_number: str = dataclasses.field(
        metadata={
            "source": "pk_nummer",
            "mapping": "contract_package_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "contract_package_number",
        }
    )

    contract_blanket_number: str = dataclasses.field(
        metadata={
            "source": "rc_nummer",
            "mapping": "contract_blanket_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "contract_blanket_number",
        }
    )

    license_number: str = dataclasses.field(
        metadata={
            "source": "tp_afmvrgn",
            "mapping": "license_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "AFM License Number",
        }
    )

    identification_number: str = dataclasses.field(
        metadata={
            "source": "tp_idnr",
            "mapping": "identification_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification number with which the intermediary is recognizable in the application",
        }
    )

    entity_type: str = dataclasses.field(
        metadata={
            "source": "vp_parzakc",
            "mapping": "entity_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Private or business, code P/Z",
        }
    )

    gender: str = dataclasses.field(
        metadata={
            "source": "vp_geslach",
            "mapping": "gender",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Gender",
        }
    )

    post_code: str = dataclasses.field(
        metadata={
            "source": "vp_pcode",
            "mapping": "post_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Postal code",
        }
    )

    date_of_birth: str = dataclasses.field(
        metadata={
            "source": "vp_gebdat",
            "mapping": "date_of_birth",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of birth",
        }
    )

    policyholder_occupation: str = dataclasses.field(
        metadata={
            "source": "ik_beroms",
            "mapping": "policyholder_occupation",
            "dtype": np.dtype("str"),
            "sqlalchemy_type": String,
            "description": "Policyholder occupation",
        }
    )

    policyholder_occupation_code: str = dataclasses.field(
        metadata={
            "source": "ik_bklasse",
            "mapping": "policyholder_occupation_code",
            "dtype": np.dtype("str"),
            "sqlalchemy_type": String,
            "description": "Policyholder classification code",
        }
    )

    main_driver_birthdate: str = dataclasses.field(
        metadata={
            "source": "bs_gebdat",
            "mapping": "main_driver_birthdate",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of birth of the main driver",
        }
    )

    name: str = dataclasses.field(
        metadata={
            "source": "wg_anaam",
            "mapping": "name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name",
        }
    )

    employer_number: str = dataclasses.field(
        metadata={
            "source": "wg_wgnum",
            "mapping": "employer_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Employer number",
        }
    )

    nature_of_business_code: str = dataclasses.field(
        metadata={
            "source": "og_sbispcc",
            "mapping": "nature_of_business_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Coded specification of nature of business",
        }
    )

    cea_code: str = dataclasses.field(
        metadata={
            "source": "og_ceacode",
            "mapping": "cea_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "CEA code is a statistic code that indicates the nature of the company",
        }
    )

    secondary_sbi_code: str = dataclasses.field(
        metadata={
            "source": "og_sbinvsc",
            "mapping": "secondary_sbi_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Additional specification code to the secondary activities SBI code",
        }
    )

    sbi_code: str = dataclasses.field(
        metadata={
            "source": "wh_sbibedr",
            "mapping": "sbi_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Standard Company Classification",
        }
    )

    business_nature: str = dataclasses.field(
        metadata={
            "source": "wh_sbispcc",
            "mapping": "business_nature",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Coded specification of nature of business",
        }
    )

    bik_code: str = dataclasses.field(
        metadata={
            "source": "wh_bikcod",
            "mapping": "bik_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The BIK code (Business Classification Chambers of Commerce)",
        }
    )

    side_activities: str = dataclasses.field(
        metadata={
            "source": "wh_sbinvsc",
            "mapping": "side_activities",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Further specification code of the secondary activities",
        }
    )
