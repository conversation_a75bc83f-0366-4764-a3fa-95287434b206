#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVDekkingDTO:
    id_dekking: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "id_dekking",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    id_polis: str = dataclasses.field(
        metadata={
            "source": "polis_id",
            "mapping": "id_polis",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "identifier of the polis file",
        }
    )

    cover_object_code: str = dataclasses.field(
        metadata={
            "source": "xd_relvrh",
            "mapping": "cover_object_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Code that relates to a specific cover object description",
        }
    )

    version_number: str = dataclasses.field(
        metadata={
            "source": "xd_relvvnr",
            "mapping": "version_number",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Number that relates to the version of the cover",
        }
    )

    cover_code: str = dataclasses.field(
        metadata={
            "source": "xd_code",
            "mapping": "cover_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Code that relates to a specific cover description in sivi portal",
        }
    )

    encoded_cover_code: str = dataclasses.field(
        metadata={
            "source": "xd_mycode",
            "mapping": "encoded_cover_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "With this code, the company can give a company-specific name (encoded) to the Cover Code",
        }
    )

    cover_type: str = dataclasses.field(
        metadata={
            "source": "xd_gadekcd",
            "mapping": "cover_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Code that relates to a specific company cover description",
        }
    )

    cover_description: str = dataclasses.field(
        metadata={
            "source": "xd_gadekco",
            "mapping": "cover_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cover description",
        }
    )

    iptiq_branch: str = dataclasses.field(
        metadata={
            "source": "xd_vgbra",
            "mapping": "iptiq_branch",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code of the principal.",
        }
    )

    iptiq_mapping_code: str = dataclasses.field(
        metadata={
            "source": "ms_newbran",
            "mapping": "iptiq_mapping_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurer mapping code",
        }
    )

    insured_sum: str = dataclasses.field(
        metadata={
            "source": "xd_verzsom",
            "mapping": "insured_sum",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured Sum",
        }
    )

    deductible: str = dataclasses.field(
        metadata={
            "source": "xd_erb",
            "mapping": "deductible",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Deductible amount",
        }
    )

    net_annual_premium: str = dataclasses.field(
        metadata={
            "source": "xd_njp",
            "mapping": "net_annual_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The annual premium for this cover for the calculation of the costs and insurance tax.",
        }
    )

    premium_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_prmprc",
            "mapping": "premium_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium Percentage",
        }
    )

    bonus_malus_step: str = dataclasses.field(
        metadata={
            "source": "xd_bmnaand",
            "mapping": "bonus_malus_step",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The number of the Bonus/Malus step.",
        }
    )

    no_claim_step: str = dataclasses.field(
        metadata={
            "source": "xd_ncaand",
            "mapping": "no_claim_step",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The number of the NoClaim step",
        }
    )

    bonus_malus_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_bmperc",
            "mapping": "bonus_malus_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Percentage B/M",
        }
    )

    no_claim_step_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_ncprc",
            "mapping": "no_claim_step_percentage",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Percentage NoClaim step",
        }
    )

    renewal_commission_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_pprc",
            "mapping": "renewal_commission_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Percentage renewal commission",
        }
    )

    proxy_commission_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_prctekc",
            "mapping": "no_claim_step_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Proxy Percentage commission",
        }
    )

    group_discount_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_prccolk",
            "mapping": "group_discount_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Group discount percentage.",
        }
    )

    package_discount_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_prcpkkt",
            "mapping": "no_claim_step_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Package discount percentage",
        }
    )

    premium_per_thousand: str = dataclasses.field(
        metadata={
            "source": "xd_prmprom",
            "mapping": "premium_per_thousand",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium per thousand",
        }
    )

    insured_amount_a: str = dataclasses.field(
        metadata={
            "source": "xd_vbdraga",
            "mapping": "insured_amount_a",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured amount A",
        }
    )

    insured_amount_b: str = dataclasses.field(
        metadata={
            "source": "xd_vbdragb",
            "mapping": "insured_amount_b",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured amount B",
        }
    )

    insured_number_employees: str = dataclasses.field(
        metadata={
            "source": "xd_vmedew",
            "mapping": "insured_number_employees",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured number of employees.",
        }
    )

    insured_payroll: str = dataclasses.field(
        metadata={
            "source": "xd_vloonsm",
            "mapping": "insured_payroll",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured payroll.",
        }
    )

    wages_costs_percentage: str = dataclasses.field(
        metadata={
            "source": "xd_vpwglbl",
            "mapping": "wages_costs_percentage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Percentage of the employer's costs that are co-insured above the wage bill.",
        }
    )

    no_claim_protection: str = dataclasses.field(
        metadata={
            "source": "xd_nocbesc",
            "mapping": "no_claim_protection",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Protection noclaim Y/N",
        }
    )

    insured_sales_amount: str = dataclasses.field(
        metadata={
            "source": "xd_vomzet",
            "mapping": "insured_sales_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insured Sales amount",
        }
    )

    payment_period_1: str = dataclasses.field(
        metadata={
            "source": "xd_uitper1",
            "mapping": "payment_period_1",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Payout term in months Payout period 1",
        }
    )

    payment_period_2: str = dataclasses.field(
        metadata={
            "source": "xd_uitper2",
            "mapping": "payment_period_1",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Payout term in months Payout period 2",
        }
    )

    payment_period_3: str = dataclasses.field(
        metadata={
            "source": "xd_uitper3",
            "mapping": "payment_period_1",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Payout term in months Payout period 3",
        }
    )

    payment_period_4: str = dataclasses.field(
        metadata={
            "source": "xd_uitper4",
            "mapping": "payment_period_1",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Payout term in months Payout period 4",
        }
    )

    payment_percentage_period_1: str = dataclasses.field(
        metadata={
            "source": "xd_uitkpr1",
            "mapping": "payment_percentage_period_1",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Distribution percentage for period 1",
        }
    )

    payment_percentage_period_2: str = dataclasses.field(
        metadata={
            "source": "xd_uitkpr2",
            "mapping": "payment_percentage_period_2",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Distribution percentage for period 2",
        }
    )

    payment_percentage_period_3: str = dataclasses.field(
        metadata={
            "source": "xd_uitkpr3",
            "mapping": "payment_percentage_period_3",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Distribution percentage for period 3",
        }
    )

    payment_percentage_period_4: str = dataclasses.field(
        metadata={
            "source": "xd_uitkpr4",
            "mapping": "payment_percentage_period_4",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Distribution percentage for period 4",
        }
    )

    amount_deviation: str = dataclasses.field(
        metadata={
            "source": "xd_bafwst",
            "mapping": "amount_deviation",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The difference between the gross premium initially determined by the insurer and the gross "
            "premium ultimately charged to the customer. "
            "With discount negative, with surcharge positive amount.",
        }
    )

    percentage_deviation: str = dataclasses.field(
        metadata={
            "source": "xd_pafwst",
            "mapping": "percentage_deviation",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The percentage difference between the initial established premium and the final premium."
            " With discount negative, with surcharge positive perc.",
        }
    )

    step_deviation: str = dataclasses.field(
        metadata={
            "source": "xd_tafwsbm",
            "mapping": "no_claim_protection",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Number of steps deviation on standard BM step."
            " A lower step is negative, a higher step is positive.",
        }
    )

    percentage_step_deviation: str = dataclasses.field(
        metadata={
            "source": "xd_pafwsbm",
            "mapping": "percentage_step_deviation",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "The percentage deviation from the standard BM rating. "
            "With discount negative, with surcharge positive percentage.",
        }
    )

    correction_amount: str = dataclasses.field(
        metadata={
            "source": "xd_tcorbdr",
            "mapping": "correction_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Correction amount applied",
        }
    )

    years_before_correction: str = dataclasses.field(
        metadata={
            "source": "xd_rjrcor",
            "mapping": "years_before_correction",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Remaining years before correction",
        }
    )

    method_premium_calculation: str = dataclasses.field(
        metadata={
            "source": "xd_wprembp",
            "mapping": "method_premium_calculation",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Coded indication of the premium calculation method during renewal.",
        }
    )
