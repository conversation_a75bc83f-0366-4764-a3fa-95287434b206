import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVSchadeFTDTO:
    id_claim_detail: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "id_claim_detail",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    id_claim: str = dataclasses.field(
        metadata={
            "source": "schade_id",
            "mapping": "id_claim",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    transaction_type: str = dataclasses.field(
        metadata={
            "source": "ft_trnsrt",
            "mapping": "transaction_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Code indicating the type of transaction",
        }
    )

    total_incurred: str = dataclasses.field(
        metadata={
            "source": "ft_tschbdr",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "ft_tsvhbdr",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Recovered amount",
        }
    )

    reserved: str = dataclasses.field(
        metadata={
            "source": "ft_schrsrv",
            "mapping": "reserved",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Reserved amount",
        }
    )

    branch_code: str = dataclasses.field(
        metadata={
            "source": "ft_gabra",
            "mapping": "branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code of the authorized agent",
        }
    )

    branch_desc: str = dataclasses.field(
        metadata={
            "source": "ft_gabrao",
            "mapping": "branch_desc",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Branch code description of the authorized agent",
        }
    )

    sub_branch_code: str = dataclasses.field(
        metadata={
            "source": "ft_gasbra",
            "mapping": "sub_branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subbranch code of the authorized agent",
        }
    )

    sub_branch_desc: str = dataclasses.field(
        metadata={
            "source": "ft_gasbrao",
            "mapping": "sub_branch_desc",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subbranch description of the authorized agent",
        }
    )

    cover_type: str = dataclasses.field(
        metadata={
            "source": "ft_gadekcd",
            "mapping": "cover_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Coverage code of the authorized agent",
        }
    )

    cover_description: str = dataclasses.field(
        metadata={
            "source": "ft_gadekco",
            "mapping": "cover_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Coverage code description of the authorized agent.",
        }
    )

    iptiq_branch_code: str = dataclasses.field(
        metadata={
            "source": "ft_vgbra",
            "mapping": "iptiq_branch_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Proxy branch code",
        }
    )

    iptiq_mapping_code: str = dataclasses.field(
        metadata={
            "source": "ms_newbran",
            "mapping": "iptiq_mapping_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurer mapping code",
        }
    )

    proxy_code: str = dataclasses.field(
        metadata={
            "source": "ft_vgcode",
            "mapping": "proxy_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Proxy code",
        }
    )

    pool_number: str = dataclasses.field(
        metadata={
            "source": "mp_poolnum",
            "mapping": "pool_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Pool number",
        }
    )

    pool_share: str = dataclasses.field(
        metadata={
            "source": "mp_poolprc",
            "mapping": "pool_share",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Share of pool",
        }
    )

    insurer_company_code: str = dataclasses.field(
        metadata={
            "source": "ms_rcnr",
            "mapping": "insurer_company_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification of the insurance company. Applies to companies and authorized agents",
        }
    )
