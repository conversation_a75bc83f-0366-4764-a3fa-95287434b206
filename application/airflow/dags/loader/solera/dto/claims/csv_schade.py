import dataclasses
from dataclasses import dataclass
from sqlalchemy.types import String, Integer, Float, Date, DateTime  # type: ignore
import numpy as np  # type: ignore


@dataclass(init=False, repr=False)
class SoleraCSVSchadeDTO:
    id_claim: str = dataclasses.field(
        metadata={
            "source": "id",
            "mapping": "id_claim",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier",
        }
    )

    contract_id: str = dataclasses.field(
        metadata={
            "source": "volmacht_id",
            "mapping": "contract_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identifier for the contract",
        }
    )

    agent_claim_number: str = dataclasses.field(
        metadata={
            "source": "sd_schnrvm",
            "mapping": "agent_claim_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Agent identifier of each claim",
        }
    )

    tpa_number: str = dataclasses.field(
        metadata={
            "source": "sd_schnrvg",
            "mapping": "tpa_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim number under which this file is known to the insurer/agent",
        }
    )

    occurrence_date: str = dataclasses.field(
        metadata={
            "source": "sd_schadat",
            "mapping": "occurrence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim Event date - When the event = cause took place",
        }
    )

    date_notified_to_Solera: str = dataclasses.field(
        metadata={
            "source": "sd_smlddat",
            "mapping": "date_notified_to_Solera",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date occurence was notified to Solera",
        }
    )

    debt_damage: str = dataclasses.field(
        metadata={
            "source": "sd_schulds",
            "mapping": "debt_damage",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Indication that there is debt damage",
        }
    )

    claim_description: str = dataclasses.field(
        metadata={
            "source": "sd_calamit",
            "mapping": "claim_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim description",
        }
    )

    closing_date: str = dataclasses.field(
        metadata={
            "source": "sd_enddatd",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing date",
        }
    )

    claim_status: str = dataclasses.field(
        metadata={
            "source": "sd_status",
            "mapping": "claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status",
        }
    )

    direct_claim_handling: str = dataclasses.field(
        metadata={
            "source": "sd_dirscha",
            "mapping": "direct_claim_handling",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Direct claims handling Y/N",
        }
    )

    contract_number: str = dataclasses.field(
        metadata={
            "source": "pp_nummer",
            "mapping": "contract_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Contract number",
        }
    )

    internal_key: str = dataclasses.field(
        metadata={
            "source": "pp_intkey",
            "mapping": "internal_key",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The internal key is the identifying data under which the contract is stored in the "
            "contract administration.",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "pp_tjrpp",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    underwriting_year_pool: str = dataclasses.field(
        metadata={
            "source": "pp_tjrpo",
            "mapping": "underwriting_year_pool",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    license_number: str = dataclasses.field(
        metadata={
            "source": "tp_afmvrgn",
            "mapping": "license_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "AFM License Number",
        }
    )

    identification_number: str = dataclasses.field(
        metadata={
            "source": "tp_idnr",
            "mapping": "identification_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identification number",
        }
    )

    adn_damage_code: str = dataclasses.field(
        metadata={
            "source": "ic_oorzakc",
            "mapping": "adn_damage_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "ADN Damage cause code",
        }
    )

    cis_damage_code: str = dataclasses.field(
        metadata={
            "source": "ic_cissoz",
            "mapping": "cis_damage_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "CIS Damage cause code",
        }
    )

    cause_type: str = dataclasses.field(
        metadata={
            "source": "ic_oorzkcd",
            "mapping": "cause_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cause of damage/ reason of claim",
        }
    )

    cause_description: str = dataclasses.field(
        metadata={
            "source": "ic_oorzaak",
            "mapping": "cause_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of cause of damage/ reason of claim",
        }
    )

    date_of_birth: str = dataclasses.field(
        metadata={
            "source": "bs_gebdat",
            "mapping": "date_of_birth",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Date of birth",
        }
    )
