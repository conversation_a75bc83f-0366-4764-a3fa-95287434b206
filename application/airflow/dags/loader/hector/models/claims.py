from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.hector.dto.claims import (
    <PERSON><PERSON><PERSON><PERSON>msDTO,
    <PERSON><PERSON><PERSON><PERSON><PERSON>DTOV2,
    <PERSON><PERSON><PERSON>laimsDTOV3,
    <PERSON><PERSON><PERSON><PERSON><PERSON>DTODayFirst,
)

attr_dict = generate_table_model_from_dtos(
    "hector_claims",
    [<PERSON><PERSON><PERSON><PERSON><PERSON>TO, <PERSON><PERSON><PERSON><PERSON><PERSON>DTOV2, <PERSON><PERSON><PERSON><PERSON><PERSON>DTOV3, <PERSON><PERSON><PERSON><PERSON><PERSON>DTODayFirst],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
<PERSON><PERSON>lai<PERSON> = type("<PERSON><PERSON>lai<PERSON>", (Base,), attr_dict)
