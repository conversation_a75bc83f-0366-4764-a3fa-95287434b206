from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.hector.dto.premium import HectorXLSXPremiumDTO, HectorXLSXPremiumDTOV2

attr_dict = generate_table_model_from_dtos(
    "hector_premium", [Hector<PERSON>LSXPremiumDTO, HectorXLSXPremiumDTOV2], schema=DEFAULT_DB_LANDING_SCHEMA
)
HectorPremium = type("HectorPremium", (Base,), attr_dict)
