from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.hector.dto.complaints import HectorXLSXComplaintsDTO

attr_dict = generate_table_model_from_dtos(
    "hector_complaints", [HectorXLSXComplaintsDTO], schema=DEFAULT_DB_LANDING_SCHEMA
)
HectorComplaints = type("HectorComplaints", (Base,), attr_dict)
