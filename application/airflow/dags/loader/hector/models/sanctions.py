from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.hector.dto.sanctions import HectorCSVSanctionDTO


attr_dict = generate_table_model_from_dtos("hector_sanctions", [HectorCSVSanctionDTO], schema=DEFAULT_DB_LANDING_SCHEMA)
HectorSanctions = type("HectorSanctions", (Base,), attr_dict)
