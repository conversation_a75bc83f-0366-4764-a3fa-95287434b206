from loader.alembic_utils.alembic_config import DEFAULT_DB_LANDING_SCHEMA, Base  # type: ignore
from loader.core.utils.dto import generate_table_model_from_dtos
from loader.hector.dto.claims import HectorXLSXClaimsVanAmeydeDTO
from loader.hector.dto.claims import HectorXLSXClaimsVanAmeydeDTOV2

attr_dict = generate_table_model_from_dtos(
    "hector_claims_van_ameyde",
    [Hector<PERSON><PERSON><PERSON>ClaimsVanAmeydeDTO, HectorXLSXClaimsVanAmeydeDTOV2],
    schema=DEFAULT_DB_LANDING_SCHEMA,
)
HectorClaimsVanAmeyde = type("HectorClaimsVanAmeyde", (Base,), attr_dict)
