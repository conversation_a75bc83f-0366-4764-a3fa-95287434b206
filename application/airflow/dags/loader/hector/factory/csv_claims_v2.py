from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.claims import HectorCS<PERSON>laimsDTOV2
from loader.hector.interface import HectorCSVPandasInterface


class HectorClaimsCSVToDataFrameFactoryV2(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVClaimsDTOV2)
