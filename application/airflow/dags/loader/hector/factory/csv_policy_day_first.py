from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.policy import Hector<PERSON>VPolicyDTODayFirst
from loader.hector.interface import HectorCSVPandasInterface


class HectorPolicyCSVDayFirstToDataFrameFactory(CSVToDataFrameFactoryV2):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVPolicyDTODayFirst)
