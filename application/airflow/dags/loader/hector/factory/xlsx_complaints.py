from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.hector.dto.complaints import HectorX<PERSON>XComplaintsDTO
from loader.hector.interface import Hector<PERSON><PERSON>XPandasInterface


class HectorComplaintsXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = HectorXLSXPandasInterface(dto=HectorXLSXComplaintsDTO)
