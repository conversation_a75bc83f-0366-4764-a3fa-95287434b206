from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.claims import Hector<PERSON><PERSON>laimsDTOALT
from loader.hector.interface import HectorCSVPandasInterface


class HectorClaimsCSVToDataFrameFactoryALT(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVClaimsDTOALT)
