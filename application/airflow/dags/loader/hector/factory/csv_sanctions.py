from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.sanctions import Hector<PERSON><PERSON>anctionDTO
from loader.hector.interface import Hector<PERSON>VPandasInterface


class HectorSanctionsCSVToDataFrameFactory(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVSanctionDTO)
