from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.hector.dto.claims import Hector<PERSON><PERSON><PERSON><PERSON>laimsDTO
from loader.hector.interface import Hector<PERSON><PERSON>XPandasInterfaceV2


class HectorClaimsXLSXToDataFrameFactory(XLSXToDataFrameFactory):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = HectorXLSXPandasInterfaceV2(dto=HectorXLSXClaimsDTO)
