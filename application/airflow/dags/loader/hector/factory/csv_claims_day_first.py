from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.claims import HectorCSVClaimsDTODayFirst
from loader.hector.interface import HectorCSVPandasInterface


class HectorClaimsCSVToDataFrameFactoryDayFirst(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVClaimsDTODayFirst)
