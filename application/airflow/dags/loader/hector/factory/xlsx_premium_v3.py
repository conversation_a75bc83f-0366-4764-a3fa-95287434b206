from loader.core.xlsx_pandas import XLSXToDataFrameFactory
from loader.hector.dto.premium import HectorXLSXPremiumDTO
from loader.hector.interface import HectorXLSXPandasInterfaceV2


class HectorPremiumXLSXToDataFrameFactoryV3(XLSXToDataFrameFactory):
    # This factory is using the version 1 DTO and the V2 interface
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = HectorXLSXPandasInterfaceV2(dto=HectorXLSXPremiumDTO)
