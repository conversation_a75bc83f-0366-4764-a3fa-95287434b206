from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.claims import Hector<PERSON>VClaimsDTO
from loader.hector.interface import Hector<PERSON>VPandasInterface


class HectorClaimsCSVToDataFrameFactory(CSVToDataFrameFactoryV2):
    def __init__(self, csv_path: str):
        super().__init__(csv_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVClaimsDTO)
