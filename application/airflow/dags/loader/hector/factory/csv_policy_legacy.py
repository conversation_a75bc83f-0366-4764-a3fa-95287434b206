from loader.core.csv_pandas import CSVToDataFrameFactoryV2
from loader.hector.dto.policy import HectorCSVPolicyDTOLegacy
from loader.hector.interface import HectorCSVPandasInterface


class HectorPolicyCSVLegacyToDataFrameFactory(CSVToDataFrameFactoryV2):
    def __init__(self, xlsx_path: str):
        super().__init__(xlsx_path)
        self._interface = HectorCSVPandasInterface(dto=HectorCSVPolicyDTOLegacy)
