from typing import Callable

from loader.core.csv_pandas import CSVPandasInterface
from loader.hector.dto.sanctions import Hector<PERSON><PERSON><PERSON><PERSON>D<PERSON>
from loader.hector.dto.policy import <PERSON><PERSON><PERSON><PERSON>yDTODayFirst
from loader.hector.dto.claims import HectorCSVClaimsDTODayFirst


class HectorCSVPandasInterface(CSVPandasInterface):
    # some edit need to be change for when leveraging other DTOs / filetype
    def __init__(self, dto):
        self._separator = ";"
        self._encoding = "ISO-8859-1"
        self._header = 0
        self._decimal = ","
        self._thousands = "."
        self._dto = dto()

    def date_parser(self) -> Callable:
        """
        In the Hector input the date format used is in the following
        dd.mm.yyyy, eg: 19.12.1989
        """
        from dateutil.parser import parse

        # Check the type of the dto
        if (
            isinstance(self._dto, HectorCSVSanctionDTO)
            or isinstance(self._dto, HectorCSVPolicyDTODayFirst)
            or isinstance(self._dto, Hector<PERSON>VClaimsDTODayFirst)
        ):
            return lambda x: parse(str(x), dayfirst=True) if len(str(x)) >= 10 else None
        else:
            return lambda x: parse(str(x)) if len(str(x)) >= 10 else None
