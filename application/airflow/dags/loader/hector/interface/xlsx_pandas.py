from typing import Callable, Optional

from dateutil.parser import parse, parserinfo

from loader.core.xlsx_pandas import XLSXPandasInterface


class HectorXLSXPandasInterface(XLSXPandasInterface):
    # some edit need to be change for when leveraging other DTOs / filetype
    def __init__(self, dto, header: Optional[int] = 0):
        self._header = header
        self._decimal = ","
        self._thousands = "."
        self._skipfooterint = 0
        self._skiprows = [1]
        self._sheet_names = [0]
        self._dto = dto()

    @classmethod
    def date_parser(cls) -> Callable:

        skip_list = ["nan", "None"]

        return lambda x: parse(str(x)) if str(x) not in skip_list else None

    def converters(self):
        return self._dto.converters() if hasattr(self._dto, "converters") else None
