#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class HectorCSVPolicyDTO:
    policy_number: str = dataclasses.field(
        metadata={
            "source": "Policy reference",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    policy_type: str = dataclasses.field(
        metadata={
            "source": "Nature of Contract",
            "mapping": "policy_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "New Business or Cancellation (in case of total loss)",
        }
    )

    location_of_underwriting: str = dataclasses.field(
        metadata={
            "source": "Location of Underwriting",
            "mapping": "location_of_underwriting",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "ISO code for Germany",
        }
    )

    location_of_risk: str = dataclasses.field(
        metadata={
            "source": "Location of Risk",
            "mapping": "location_of_risk",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "ISO code for Germany",
        }
    )

    class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class of Business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II class",
        }
    )

    cover: str = dataclasses.field(
        metadata={
            "source": "Insurance cover",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The 2 covers are mandatory: MTPL+MOD",
        }
    )

    package: str = dataclasses.field(
        metadata={
            "source": "packages",
            "mapping": "package",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Package type",
        }
    )

    insured_value_pd: str = dataclasses.field(
        metadata={
            "source": "Insured value for Property Damage",
            "mapping": "insured_value_pd",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Maximum amount an insurance company will pay if total loss for property damage",
        }
    )

    insured_value_bi: str = dataclasses.field(
        metadata={
            "source": "Insured value for Bodily Injury Damage",
            "mapping": "insured_value_bi",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Maximum amount an insurance company will pay if total loss for bodily injury",
        }
    )

    insured_value_others: str = dataclasses.field(
        metadata={
            "source": "Insured value for other covers",
            "mapping": "insured_value_others",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Maximum amount an insurance company will pay in other circumstances",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "When the coverage of the vehicle starts",
        }
    )

    period_start_date: str = dataclasses.field(
        metadata={
            "source": "Period begin date",
            "mapping": "period_start_date",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Period start date",
        }
    )

    policy_end_date: str = dataclasses.field(
        metadata={
            "source": "Policy end date",
            "mapping": "policy_end_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "When the coverage of the vehicle ends",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Underwriting Year",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    transaction_currency: str = dataclasses.field(
        metadata={
            "source": "Transaction Currency",
            "mapping": "transaction_currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Should systematically be Euros, so ISO code EUR",
        }
    )

    gross_written_premium: str = dataclasses.field(
        metadata={
            "source": "Premium without taxes",
            "mapping": "gross_written_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Premium without taxes",
        }
    )

    ipt_rate: str = dataclasses.field(
        metadata={
            "source": "IPT_Rate",
            "mapping": "ipt_rate",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insurance Premium Tax rate",
        }
    )

    ipt: str = dataclasses.field(
        metadata={
            "source": "IPT_Amount",
            "mapping": "ipt",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Insurance Premium Tax",
        }
    )

    total_underwritten: str = dataclasses.field(
        metadata={
            "source": "Premium with taxes",
            "mapping": "total_underwritten",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Sum of Premium with taxes",
        }
    )

    commission: str = dataclasses.field(
        metadata={
            "source": "Commission_amount",
            "mapping": "commission",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Commission due to Hector",
        }
    )

    commission_rate: str = dataclasses.field(
        metadata={
            "source": "Commission_%",
            "mapping": "commission_rate",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Commission rate due to Hector",
        }
    )

    net_balance_due_to_iptiq: str = dataclasses.field(
        metadata={
            "source": "Net Balance due to IPTIQ",
            "mapping": "net_balance_due_to_iptiq",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount paid to iptiQ",
        }
    )

    annual_gross_written_premium: str = dataclasses.field(
        metadata={
            "source": "Annual Premium without taxes",
            "mapping": "annual_gross_written_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual premium without taxes",
        }
    )

    annual_tax_amount: str = dataclasses.field(
        metadata={
            "source": "Annual tax amount",
            "mapping": "annual_tax_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual tax amount",
        }
    )

    insured_name: str = dataclasses.field(
        metadata={
            "source": "Insured Name / Policyholder name",
            "mapping": "insured_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "First name and family name",
        }
    )

    vehicle_owner_name: str = dataclasses.field(
        metadata={
            "source": "Vehicle owner",
            "mapping": "vehicle_owner_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the vehicle owner (first and last)",
        }
    )

    main_driver_name: str = dataclasses.field(
        metadata={
            "source": "Main driver",
            "mapping": "main_driver_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the main driver (first and last)",
        }
    )

    main_driver_birthdate: str = dataclasses.field(
        metadata={
            "source": "Birthdate of the main driver / Driver's age",
            "mapping": "main_driver_birthdate",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of birth of the main driver",
        }
    )

    street: str = dataclasses.field(
        metadata={
            "source": "Address of the main driver: Street name and Street number",
            "mapping": "street",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Street name and number of the main driver",
        }
    )

    city: str = dataclasses.field(
        metadata={
            "source": "Address of the main driver: City",
            "mapping": "city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the main driver",
        }
    )

    post_code: str = dataclasses.field(
        metadata={
            "source": "Address of the main driver: Post code",
            "mapping": "post_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Post code of the main driver",
        }
    )

    installment: str = dataclasses.field(
        metadata={
            "source": "Payment Instalment",
            "mapping": "installment",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Payment installment",
        }
    )

    payment_frequency: str = dataclasses.field(
        metadata={
            "source": "Payment Frequency",
            "mapping": "payment_frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Frequency of payment",
        }
    )

    distribution_channel: str = dataclasses.field(
        metadata={
            "source": "Distribution channel",
            "mapping": "distribution_channel",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The channel the policy sale came through",
        }
    )

    number_of_previous_claims: str = dataclasses.field(
        metadata={
            "source": "previous_claims",
            "mapping": "number_of_previous_claims",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Number of previous claims",
        }
    )

    cancellation_of_previous_insurer: str = dataclasses.field(
        metadata={
            "source": "cancelation_of_the_previous_insurer",
            "mapping": "cancellation_of_previous_insurer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cancellation of the previous insurer",
        }
    )

    annual_milage: str = dataclasses.field(
        metadata={
            "source": "annual_milage",
            "mapping": "annual_milage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Annual milage of the vehicle",
        }
    )

    age_of_youngest_driver: str = dataclasses.field(
        metadata={
            "source": "age_of_youngest_driver",
            "mapping": "age_of_youngest_driver",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Age of the youngest driver",
        }
    )

    age_of_policyholder: str = dataclasses.field(
        metadata={
            "source": "age_of_policy_holder",
            "mapping": "age_of_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Age of the policyholder",
        }
    )

    gdv_mtpl_regional_class: str = dataclasses.field(
        metadata={
            "source": "gdv_mtpl_regional_class",
            "mapping": "gdv_mtpl_regional_class",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "MTPL Regional Class",
        }
    )

    gdv_lim_mod_regional_class: str = dataclasses.field(
        metadata={
            "source": "gdv_lim_mod_regional_class",
            "mapping": "gdv_lim_mod_regional_class",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "GDV Lim Mod Regional Class",
        }
    )

    gdv_mod_regional_class: str = dataclasses.field(
        metadata={
            "source": "gdv_mod_regional_class",
            "mapping": "gdv_mod_regional_class",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Mod Regional Class",
        }
    )

    gdv_type_class: str = dataclasses.field(
        metadata={
            "source": "gdv_type_class",
            "mapping": "gdv_type_class",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "GDV Type Class",
        }
    )

    hector_regional_class: str = dataclasses.field(
        metadata={
            "source": "hector_regional_class",
            "mapping": "hector_regional_class",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Hector Regional Class",
        }
    )

    vehicle_registration_number: str = dataclasses.field(
        metadata={
            "source": "vehicle_carplate",
            "mapping": "vehicle_registration_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car plate",
        }
    )

    vehicle_hsn_tsn: str = dataclasses.field(
        metadata={
            "source": "vehicle_hsn_tsn",
            "mapping": "vehicle_hsn_tsn",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "HSN TSN code",
        }
    )

    vehicle_brand: str = dataclasses.field(
        metadata={
            "source": "vehicle_brand",
            "mapping": "vehicle_brand",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Brand",
        }
    )

    vehicle_power: str = dataclasses.field(
        metadata={
            "source": "vehicle_horse_power",
            "mapping": "vehicle_power",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Horse power of the vehicle",
        }
    )

    vehicle_power_sum: str = dataclasses.field(
        metadata={
            "source": "vehicle_sum_of_horse_power",
            "mapping": "vehicle_power_sum",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Horse power sum of the vehicle",
        }
    )

    vehicle_category: str = dataclasses.field(
        metadata={
            "source": "vehicle_category",
            "mapping": "vehicle_category",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The category of the vehicle (European standard)",
        }
    )

    vehicle_sold_year: str = dataclasses.field(
        metadata={
            "source": "vehicle_sold_year",
            "mapping": "vehicle_sold_year",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Year vehicle was sold",
        }
    )

    deductible_amount: str = dataclasses.field(
        metadata={
            "source": "cover_level_deductible",
            "mapping": "deductible_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Deductible amount",
        }
    )
