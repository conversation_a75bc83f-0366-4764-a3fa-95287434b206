import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class HectorXLSXComplaintsDTO:
    complaint_reference: str = dataclasses.field(
        metadata={
            "source": "issue_number",
            "mapping": "complaint_reference",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint reference",
        }
    )

    creation_year: str = dataclasses.field(
        metadata={
            "source": "creation_year",
            "mapping": "creation_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Year complaint was created",
        }
    )

    complaint_code: str = dataclasses.field(
        metadata={
            "source": "complaint_code",
            "mapping": "complaint_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Code of complaint",
        }
    )

    complaint_handling_code: str = dataclasses.field(
        metadata={
            "source": "complaint_handling_code",
            "mapping": "complaint_handling_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Handling Code of complaint",
        }
    )

    reception_date: str = dataclasses.field(
        metadata={
            "source": "reception_date",
            "mapping": "reception_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of reception of the complaint",
        }
    )

    product_type: str = dataclasses.field(
        metadata={
            "source": "product_type",
            "mapping": "product_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of product",
        }
    )

    ref_policy: str = dataclasses.field(
        metadata={
            "source": "policy_id",
            "mapping": "ref_policy",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Policy Reference",
        }
    )

    sector: str = dataclasses.field(
        metadata={
            "source": "sector",
            "mapping": "sector",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Department/area of the company who received the complaint",
        }
    )

    name_policyholder: str = dataclasses.field(
        metadata={
            "source": "name_policyholder",
            "mapping": "name_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the policy holder",
        }
    )

    address_policyholder: str = dataclasses.field(
        metadata={
            "source": "address_policyholder",
            "mapping": "address_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address of the policy holder",
        }
    )

    type_policyholder: str = dataclasses.field(
        metadata={
            "source": "type_policyholder",
            "mapping": "type_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of Policy holder",
        }
    )

    geographic_area_policyholder: str = dataclasses.field(
        metadata={
            "source": "geographic_area_policyholder",
            "mapping": "geographic_area_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Geographical area of the policyholder",
        }
    )

    name_complainer: str = dataclasses.field(
        metadata={
            "source": "name_complainer",
            "mapping": "name_complainer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name and First name of the complainer",
        }
    )

    address_complainer: str = dataclasses.field(
        metadata={
            "source": "address_complainer",
            "mapping": "address_complainer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address of the complainer",
        }
    )

    type_complainer: str = dataclasses.field(
        metadata={
            "source": "type_complainer",
            "mapping": "type_complainer",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Type of complainer",
        }
    )

    closure_date: str = dataclasses.field(
        metadata={
            "source": "closure_date",
            "mapping": "closure_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closure date",
        }
    )

    result: str = dataclasses.field(
        metadata={
            "source": "result",
            "mapping": "result",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Result",
        }
    )

    judicial_authority_intervention: str = dataclasses.field(
        metadata={
            "source": "judicial_authority_intervention",
            "mapping": "judicial_authority_intervention",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Possible intervention of the judicial authorities",
        }
    )

    monetary_value: str = dataclasses.field(
        metadata={
            "source": "monetary_value",
            "mapping": "monetary_value",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Monetary value",
        }
    )

    duration_till_closure: str = dataclasses.field(
        metadata={
            "source": "duration_till_closure",
            "mapping": "duration_till_closure",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Duration to close the complaint",
        }
    )

    complaint_subject: str = dataclasses.field(
        metadata={
            "source": "complaint_subject",
            "mapping": "complaint_subject",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Subject of the complaint",
        }
    )

    complaint_cause: str = dataclasses.field(
        metadata={
            "source": "complaint_cause",
            "mapping": "complaint_cause",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Cause of complaint",
        }
    )

    handled_by_ivass: str = dataclasses.field(
        metadata={
            "source": "handled_by_ivass",
            "mapping": "handled_by_ivass",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "If complained is handled by ivass",
        }
    )

    ref_complaint_ivass: str = dataclasses.field(
        metadata={
            "source": "ref_complaint_ivass",
            "mapping": "ref_complaint_ivass",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint reference from ivass",
        }
    )

    ref_claim: str = dataclasses.field(
        metadata={
            "source": "ref_claim",
            "mapping": "ref_claim",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim Reference",
        }
    )

    response: str = dataclasses.field(
        metadata={
            "source": "response",
            "mapping": "response",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Response",
        }
    )

    transferred_judicial_authority: str = dataclasses.field(
        metadata={
            "source": "transferred_judicial_authority",
            "mapping": "transferred_judicial_authority",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Judicial Authorities",
        }
    )

    reopened: str = dataclasses.field(
        metadata={
            "source": "reopened",
            "mapping": "reopened",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Complaint reopened",
        }
    )

    payout_reason: str = dataclasses.field(
        metadata={
            "source": "payout_reason",
            "mapping": "payout_reason",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Reason for payout",
        }
    )

    complaint_compensation_currency: str = dataclasses.field(
        metadata={
            "source": "complaint_compensation_currency",
            "mapping": "complaint_compensation_currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Currency related to the possible compensation",
        }
    )

    complaint_compensation_amount: str = dataclasses.field(
        metadata={
            "source": "complaint_compensation_amount",
            "mapping": "complaint_compensation_amount",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Amount of the compensation asked",
        }
    )

    complaint_redressal_payment_currency: str = dataclasses.field(
        metadata={
            "source": "complaint_redressal_payment_currency",
            "mapping": "complaint_redressal_payment_currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Currency in which the compensation was paid",
        }
    )

    complaint_redressal_payment_amount: str = dataclasses.field(
        metadata={
            "source": "complaint_redressal_payment_amount",
            "mapping": "complaint_redressal_payment_amount",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Amount of the compensation payed",
        }
    )

    complaint_root_cause: str = dataclasses.field(
        metadata={
            "source": "complaint_root_cause",
            "mapping": "complaint_root_cause",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Root cause of the complaint",
        }
    )
