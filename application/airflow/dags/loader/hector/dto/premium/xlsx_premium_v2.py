import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.hector.dto.premium import HectorXLSXPremiumDTO


@dataclass(init=False, repr=False)
class HectorXLSXPremiumDTOV2(HectorXLSXPremiumDTO):
    courtage: str = dataclasses.field(
        metadata={
            "source": "VM Courtage",
            "mapping": "courtage",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Brokerage commission",
        }
    )

    payment_frequency: str = dataclasses.field(
        metadata={
            "source": "Zahlweise",
            "mapping": "payment_frequency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Brokerage commission",
        }
    )

    customer_connection: str = dataclasses.field(
        metadata={
            "source": "Kundenverbindung",
            "mapping": "customer_connection",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Customer connection",
        }
    )

    agent_name: str = dataclasses.field(
        metadata={
            "source": "AGENTURNR",
            "mapping": "agent_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Agent Name",
        }
    )
