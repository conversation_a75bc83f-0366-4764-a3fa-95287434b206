import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.hector.dto.premium import HectorXLSXPremiumDTO


@dataclass(init=False, repr=False)
class HectorXLSXPremiumDTOALT(HectorXLSXPremiumDTO):
    net_premium: str = dataclasses.field(
        metadata={
            "source": "gebuchte Nettopraemie",
            "mapping": "net_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Net premium",
        }
    )

    gross_written_premium: str = dataclasses.field(
        metadata={
            "source": "gebuchte Bruttopraemie",
            "mapping": "gross_written_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Gross written premium",
        }
    )
