import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class HectorXLSXPremiumDTO:
    company_name: str = dataclasses.field(
        metadata={
            "source": "Gesellschaftsname",
            "mapping": "company_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Company name",
        }
    )

    name_policyholder: str = dataclasses.field(
        metadata={
            "source": "Name_VN",
            "mapping": "name_policyholder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of policyholder",
        }
    )

    policy_number: str = dataclasses.field(
        metadata={
            "source": "VNR",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    booking_date: str = dataclasses.field(
        metadata={
            "source": "Buchungsdatum",
            "mapping": "booking_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Booking date",
        }
    )

    instalment: str = dataclasses.field(
        metadata={
            "source": "Buchungsmonat",
            "mapping": "instalment",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Instalment number",
        }
    )

    payment_year: str = dataclasses.field(
        metadata={
            "source": "Buchungsjahr",
            "mapping": "payment_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Payment year",
        }
    )

    underwriting_year: str = dataclasses.field(
        metadata={
            "source": "Zeichnungsjahr",
            "mapping": "underwriting_year",
            "dtype": np.dtype(int),
            "sqlalchemy_type": Integer,
            "description": "Underwriting year",
        }
    )

    payment_period_start: str = dataclasses.field(
        metadata={
            "source": "Buchungszeitraum von",
            "mapping": "payment_period_start",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Payment period start",
        }
    )

    payment_period_end: str = dataclasses.field(
        metadata={
            "source": "Buchungszeitraum bis",
            "mapping": "payment_period_end",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Payment period end",
        }
    )

    invoice_or_credit_note: str = dataclasses.field(
        metadata={
            "source": "Rechnung / Gutschrift",
            "mapping": "invoice_or_credit_note",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Invoice or credit note",
        }
    )

    net_premium: str = dataclasses.field(
        metadata={
            "source": "#Gebuchte_Nettopraemie",
            "mapping": "net_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Net premium",
        }
    )

    gross_written_premium: str = dataclasses.field(
        metadata={
            "source": "#Gebuchte_Bruttopraemie",
            "mapping": "gross_written_premium",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Gross written premium",
        }
    )

    commission: str = dataclasses.field(
        metadata={
            "source": "Provision",
            "mapping": "commission",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Commission",
        }
    )
