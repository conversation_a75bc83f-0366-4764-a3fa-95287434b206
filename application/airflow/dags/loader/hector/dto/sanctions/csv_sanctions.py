#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, String  # type: ignore


@dataclass(init=False, repr=False)
class HectorCSVSanctionDTO:
    sanction_id: str = dataclasses.field(
        metadata={
            "source": "ID",
            "mapping": "sanction_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Identifier for sanctions file",
        }
    )

    sanction_type: str = dataclasses.field(
        metadata={
            "source": "Zuordnung",
            "mapping": "sanction_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Company or individual",
        }
    )

    company_name: str = dataclasses.field(
        metadata={
            "source": "company name",
            "mapping": "company_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the company",
        }
    )

    company_address_line_1: str = dataclasses.field(
        metadata={
            "source": "adress Line1",
            "mapping": "company_address_line_1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Company address line 1",
        }
    )

    company_address_line_2: str = dataclasses.field(
        metadata={
            "source": "adress line2",
            "mapping": "company_address_line_2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Company address line 2",
        }
    )

    first_name: str = dataclasses.field(
        metadata={
            "source": "First Name",
            "mapping": "first_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "First name of the individual",
        }
    )

    middle_name: str = dataclasses.field(
        metadata={
            "source": "Middle Name",
            "mapping": "middle_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Middle name of the individual",
        }
    )
    last_name: str = dataclasses.field(
        metadata={
            "source": "Last Name",
            "mapping": "last_name",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Last name of the person",
        }
    )
    gender: str = dataclasses.field(
        metadata={
            "source": "Gender",
            "mapping": "gender",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Gender of the person",
        }
    )
    date_of_birth: str = dataclasses.field(
        metadata={
            "source": "Date of Birth",
            "mapping": "date_of_birth",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date of Birth",
        }
    )
    nationality: str = dataclasses.field(
        metadata={
            "source": "Nationality",
            "mapping": "nationality",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Nationality of the person",
        }
    )
    address_line1: str = dataclasses.field(
        metadata={
            "source": "Adress Line1",
            "mapping": "address_line1",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address of the person",
        }
    )
    address_line2: str = dataclasses.field(
        metadata={
            "source": "Adress Line2",
            "mapping": "address_line2",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Address complement",
        }
    )
    city: str = dataclasses.field(
        metadata={
            "source": "City",
            "mapping": "city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of residence",
        }
    )
    post_code: str = dataclasses.field(
        metadata={
            "source": "Postal Code",
            "mapping": "post_code",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Post code of the area",
        }
    )
    country: str = dataclasses.field(
        metadata={
            "source": "Country",
            "mapping": "country",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Country",
        }
    )
    bvd_id: str = dataclasses.field(
        metadata={
            "source": "bvd_id",
            "mapping": "bvd_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "bvd id",
        }
    )
    national_id: str = dataclasses.field(
        metadata={
            "source": "national_id",
            "mapping": "national_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "National Identifier",
        }
    )
    account_id: str = dataclasses.field(
        metadata={
            "source": "Account ID",
            "mapping": "account_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Account identifier",
        }
    )

    email: str = dataclasses.field(
        metadata={
            "source": "email",
            "mapping": "email",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Email address",
        }
    )

    business: str = dataclasses.field(
        metadata={
            "source": "Business",
            "mapping": "business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Business",
        }
    )
    tax_id: str = dataclasses.field(
        metadata={
            "source": "Tax ID",
            "mapping": "tax_id",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Tax identifier",
        }
    )
    ssn: str = dataclasses.field(
        metadata={
            "source": "SSN",
            "mapping": "ssn",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "SSN",
        }
    )
    other_information: str = dataclasses.field(
        metadata={
            "source": "Other Information",
            "mapping": "other_information",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Additional information",
        }
    )
