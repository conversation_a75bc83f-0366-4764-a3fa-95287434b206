import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore

from loader.hector.dto.claims import HectorCSVClaimsDTO


@dataclass(init=False, repr=False)
class HectorCSVClaimsDTOALT(HectorCSVClaimsDTO):
    total_incurred: str = dataclasses.field(
        metadata={
            "source": "Total incurred",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )
