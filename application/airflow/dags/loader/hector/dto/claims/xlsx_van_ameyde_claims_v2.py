#!/usr/bin/python
# -*- coding: latin-1 -*-

import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class HectorXLSXClaimsVanAmeydeDTOV2:
    policy_type: str = dataclasses.field(
        metadata={
            "source": "Coverage Type",
            "mapping": "policy_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The type of cover policy being claimed",
        }
    )

    claim_type: str = dataclasses.field(
        metadata={
            "source": "Claim Type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The type of claim",
        }
    )

    claim_id_hector: str = dataclasses.field(
        metadata={
            "source": "Client Reference",
            "mapping": "claim_id_hector",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The claim identifier from <PERSON>",
        }
    )

    claim_id_van_ameyde: str = dataclasses.field(
        metadata={
            "source": "Event Reference",
            "mapping": "claim_id_van_ameyde",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The claim identifier from Van Ameyde",
        }
    )

    date_of_loss: str = dataclasses.field(
        metadata={
            "source": "Event Date",
            "mapping": "date_of_loss",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim occurred on",
        }
    )

    date_of_notification: str = dataclasses.field(
        metadata={
            "source": "Notification Date",
            "mapping": "date_of_notification",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim was made",
        }
    )

    country_of_event: str = dataclasses.field(
        metadata={
            "source": "Event Country",
            "mapping": "country_of_event",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The country the claim occurred in",
        }
    )

    cause_type: str = dataclasses.field(
        metadata={
            "source": "Event Cause",
            "mapping": "cause_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The cause of the claim",
        }
    )

    policy_identifier: str = dataclasses.field(
        metadata={
            "source": "Policy Number",
            "mapping": "policy_identifier",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The policy the claim is for",
        }
    )

    policy_holder: str = dataclasses.field(
        metadata={
            "source": "Policy Holder Name",
            "mapping": "policy_holder",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The name of the policy holder",
        }
    )

    accident_code_type: str = dataclasses.field(
        metadata={
            "source": "Damage Type",
            "mapping": "accident_code_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The accident code of the claim",
        }
    )

    liability: str = dataclasses.field(
        metadata={
            "source": "Insured Liability Percentage",
            "mapping": "liability",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The liability on the claim",
        }
    )

    claim_status: str = dataclasses.field(
        metadata={
            "source": "Claim Status",
            "mapping": "claim_status",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The status of the claim",
        }
    )

    date_of_closure: str = dataclasses.field(
        metadata={
            "source": "Close Date",
            "mapping": "date_of_closure",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim was closed",
        }
    )

    date_of_last_review: str = dataclasses.field(
        metadata={
            "source": "Last Review Date",
            "mapping": "date_of_last_review",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "The date the claim was last reviewed",
        }
    )

    reserve_pd: str = dataclasses.field(
        metadata={
            "source": "Outstanding Damage Reserve",
            "mapping": "reserve_pd",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The reserve for property damage on the claim",
        }
    )

    paid_pd: str = dataclasses.field(
        metadata={
            "source": "Paid Indemnity",
            "mapping": "paid_pd",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for property damage on the claim",
        }
    )

    paid_external: str = dataclasses.field(
        metadata={
            "source": "Paid External Expenses",
            "mapping": "paid_external",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for external expenses on the claim",
        }
    )

    paid_other: str = dataclasses.field(
        metadata={
            "source": "Paid Other Costs",
            "mapping": "paid_other",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount paid for anything else on the claim",
        }
    )

    recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Outstanding Recovery",
            "mapping": "recovery_reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The outstanding amount for recoveries on the claim",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "Recovery Received",
            "mapping": "recovered",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The amount already recovered on the claim",
        }
    )

    total_paid: str = dataclasses.field(
        metadata={
            "source": "Total Paid",
            "mapping": "total_paid",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The total amount paid on the claim",
        }
    )

    total_reserve: str = dataclasses.field(
        metadata={
            "source": "Total Outstanding",
            "mapping": "total_reserve",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The total amount reserved on the claim",
        }
    )

    total_cost: str = dataclasses.field(
        metadata={
            "source": "Total Incurred",
            "mapping": "total_cost",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The total costs on the claim",
        }
    )

    claim_handling_fee: str = dataclasses.field(
        metadata={
            "source": "Invoiced Fees",
            "mapping": "claim_handling_fee",
            "dtype": np.dtype(float),
            "sqlalchemy_type": Float,
            "description": "The fee for Van Ameyde handling the claim",
        }
    )

    currency: str = dataclasses.field(
        metadata={
            "source": "Currency",
            "mapping": "currency",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "The currency of the claim",
        }
    )
