import dataclasses
from dataclasses import dataclass

import numpy as np  # type: ignore
from sqlalchemy.types import Date, DateTime, Float, Integer, String  # type: ignore


@dataclass(init=False, repr=False)
class HectorCSVClaimsDTO:
    policy_number: str = dataclasses.field(
        metadata={
            "source": "Policy Reference",
            "mapping": "policy_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each policy",
        }
    )

    claim_number: str = dataclasses.field(
        metadata={
            "source": "Claim reference",
            "mapping": "claim_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier of each claim",
        }
    )

    tpa_number: str = dataclasses.field(
        metadata={
            "source": "TPA Claim reference",
            "mapping": "tpa_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Unique identifier for TPA",
        }
    )

    occurrence_date: str = dataclasses.field(
        metadata={
            "source": "Occurrence_date",
            "mapping": "occurrence_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim Event date - When the event = cause took place",
        }
    )

    postcode_occurrence: str = dataclasses.field(
        metadata={
            "source": "Postcode_occurence",
            "mapping": "postcode_occurrence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - Postcode",
        }
    )

    city_occurrence: str = dataclasses.field(
        metadata={
            "source": "City_occurence",
            "mapping": "city_occurrence",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Where the event = cause took place - City",
        }
    )

    date_notified_to_hector: str = dataclasses.field(
        metadata={
            "source": "Date_notified",
            "mapping": "date_notified_to_hector",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Date occurence was notified to Hector",
        }
    )

    opening_date: str = dataclasses.field(
        metadata={
            "source": "Opening_date",
            "mapping": "opening_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim opening date",
        }
    )

    last_review_date: str = dataclasses.field(
        metadata={
            "source": "Last_review",
            "mapping": "last_review_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Claim last review date",
        }
    )

    insured: str = dataclasses.field(
        metadata={
            "source": "Insured",
            "mapping": "insured",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Name of the insured",
        }
    )

    insured_city: str = dataclasses.field(
        metadata={
            "source": "Insured_city",
            "mapping": "insured_city",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "City of the insured",
        }
    )

    class_of_business: str = dataclasses.field(
        metadata={
            "source": "Class_of_business",
            "mapping": "class_of_business",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Solvency II Class of business, listed as a text field e.g. Motor Third Party Liability",
        }
    )

    cover: str = dataclasses.field(
        metadata={
            "source": "coverage /Insurance cover",
            "mapping": "cover",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Insurance cover",
        }
    )

    claimant: str = dataclasses.field(
        metadata={
            "source": "Claimant",
            "mapping": "claimant",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claimant",
        }
    )

    policy_start_date: str = dataclasses.field(
        metadata={
            "source": "Policy begin date",
            "mapping": "policy_start_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Policy begin date",
        }
    )

    closing_date: str = dataclasses.field(
        metadata={
            "source": "Closing_date",
            "mapping": "closing_date",
            "dtype": np.dtype("M"),
            "sqlalchemy_type": Date,
            "description": "Closing date",
        }
    )

    vehicle_registration_number: str = dataclasses.field(
        metadata={
            "source": "Car plate",
            "mapping": "vehicle_registration_number",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Car plate",
        }
    )

    claim_type: str = dataclasses.field(
        metadata={
            "source": "claim_type",
            "mapping": "claim_type",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of the peril/non-peril",
        }
    )

    claim_description: str = dataclasses.field(
        metadata={
            "source": "Description of the claim",
            "mapping": "claim_description",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Description of the claim",
        }
    )

    presence_pd: str = dataclasses.field(
        metadata={
            "source": "Presence_Property Damage",
            "mapping": "presence_pd",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Presence of Property Damage (question)",
        }
    )

    claim_status_at_end_of_the_month: str = dataclasses.field(
        metadata={
            "source": "Claim_status_at_end_of_the_month",
            "mapping": "claim_status_at_end_of_the_month",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claim status at the end of the month",
        }
    )

    paid_amount: str = dataclasses.field(
        metadata={
            "source": "Paid",
            "mapping": "paid_amount",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Amount paid",
        }
    )

    claim_assessment_paid: str = dataclasses.field(
        metadata={
            "source": "Claim_assessment_paid",
            "mapping": "claim_assessment_paid",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the paid amount for claim assessment costs",
        }
    )

    recovered: str = dataclasses.field(
        metadata={
            "source": "Recovered",
            "mapping": "recovered",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the amount in case of recovery received from Stanza di Compensazione",
        }
    )

    reserved: str = dataclasses.field(
        metadata={
            "source": "Reserved",
            "mapping": "reserved",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Reserve amount ",
        }
    )

    recovery_reserve: str = dataclasses.field(
        metadata={
            "source": "Recovery_reserve",
            "mapping": "recovery_reserve",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "This is the reserve amount in case a recovery opportunity is identified",
        }
    )

    total_incurred: str = dataclasses.field(
        metadata={
            "source": "Total_incurred",
            "mapping": "total_incurred",
            "dtype": np.dtype("f"),
            "sqlalchemy_type": Float,
            "description": "Total incurred",
        }
    )

    claims_referred: str = dataclasses.field(
        metadata={
            "source": "Claims_referred",
            "mapping": "claims_referred",
            "dtype": np.dtype(str),
            "sqlalchemy_type": String,
            "description": "Claims_referred",
        }
    )
