from loader.hector.factory import *

file_types = [
    {
        "name": "hector_csv_policy_day_first",
        "factory": HectorPolicyCSVDayFirstToDataFrameFactory,
        # This for the time being is one off dto
        "regexp": r"^24-02-08_Reporting_PREMIUM_1_24_V1.csv",
        "category": "policies",
    },
    {
        "name": "hector_csv_policy_legacy",
        "factory": HectorPolicyCSVLegacyToDataFrameFactory,
        # For files received inside 2023
        "regexp": r"^\d{2}-\d{2}-\d{2} Reporting_PREMIUM.*.csv",
        "category": "policies",
    },
    {
        "name": "hector_csv_policy",
        "factory": HectorPolicyCSVToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^(\d{2}-\d{2}-\d{2}||\d{4}_\d{2})_Reporting_PREMIUM.*.csv",
        "category": "policies",
    },
    {
        "name": "hector_xlsx_premiumV2",
        "factory": HectorPremiumXLSXToDataFrameFactoryV2,
        # Placeholder for hector regex
        "regexp": r"^24-02_Booking_Report\.xlsx",
        "category": "premium",
    },
    {
        "name": "hector_xlsx_premiumV3",
        "factory": HectorPremiumXLSXToDataFrameFactoryV3,
        # Placeholder for hector regex
        "regexp": r"^\d{2}-\d{2}_Booking_Report\.xlsx",
        "category": "premium",
    },
    {
        "name": "hector_xlsx_premium_alt",
        "factory": HectorPremiumXLSXToDataFrameFactoryALT,
        # Changes in the name of #Gebuchte_Nettoprämie & #Gebuchte_Bruttoprämie
        "regexp": r"^\d{4}_\d{2}.+Reporting_Booking_Report_4_24.+\.xlsx",
        "category": "premium",
    },
    {
        "name": "hector_xlsx_premium",
        "factory": HectorPremiumXLSXToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^\d{4}[_-]\d{2}.+Reporting_Booking.+\.xlsx",
        "category": "premium",
    },
    {
        "name": "hector_xlsx_premium",
        "factory": HectorPremiumXLSXToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^\d{4}-\d{2}.+Finance.+\.xlsx",
        "category": "premium",
    },
    {
        "name": "hector_csv_claims_day_first",
        "factory": HectorClaimsCSVToDataFrameFactoryDayFirst,
        # Placeholder for hector regex
        "regexp": r"^2024-10_Reporting_CLAIMS_10_24_v2\.csv",
        "category": "claims",
    },
    {
        "name": "hector_csv_claims_v3",
        "factory": HectorClaimsCSVToDataFrameFactoryV3,
        # Placeholder for hector regex
        "regexp": r"^2024-03_Reporting_CLAIMS_3_24\.csv",
        "category": "claims",
    },
    {
        "name": "hector_csv_claims_v2",
        "factory": HectorClaimsCSVToDataFrameFactoryV2,
        # Placeholder for hector regex
        "regexp": r"^2024-02_Reporting_CLAIMS_2_24\.csv",
        "category": "claims",
    },
    {
        "name": "hector_csv_claims",
        "factory": HectorClaimsCSVToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^2024-01_Reporting_CLAIMS_1_24\.csv",
        "category": "claims",
    },
    {
        "name": "hector_csv_claims",
        "factory": HectorClaimsCSVToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^2023-\d{2}[ _]Reporting_CLAIMS_(\d{1,2}).*.csv",
        "category": "claims",
    },
    {
        "name": "hector_xlsx_claims",
        "factory": HectorClaimsXLSXToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^\d{4}-\d{2}.+Reporting_CLAIMS.+\.xlsx",
        "category": "claims",
    },
    {
        "name": "hector_csv_claims_alt",
        "factory": HectorClaimsCSVToDataFrameFactoryALT,
        # Placeholder for hector regex
        "regexp": r"^\d{4}-\d{2}.+Reporting_CLAIMS.+\.csv",
        "category": "claims",
    },
    {
        "name": "hector_csv_sanctions",
        "factory": HectorSanctionsCSVToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^.*iptiq_individuals-companies_export\.csv",
        "category": "sanctions",
    },
    {
        "name": "hector_xlsx_complaints",
        "factory": HectorComplaintsXLSXToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^\d{4}-\d{2}\sReporting_COMPLAINTS_\d{2}.xlsx",
        "category": "complaints",
    },
    {
        "name": "hector_xlsx_claims_van_ameyde",
        "factory": HectorClaimsVanAmeydeXLSXToDataFrameFactory,
        # Placeholder for hector regex
        "regexp": r"^\d{2}.\d{2}.\d{4} - Van Ameyde.*\.xlsx",
        "category": "claims_van_ameyde",
    },
    # For files received from April 2025 onwards. Header located at the 2nd row of the file
    {
        "name": "hector_xlsx_claims_van_ameyde_v3",
        "factory": HectorClaimsVanAmeydeXLSXToDataFrameFactoryV3,
        # i.e 30.04.2025 - Hector - BDX - VA.xlsx
        "regexp": r"^(\d{2}\.(0[4-9]|1[0-2])\.2025|\d{2}\.(0[1-9]|1[0-2])\.202[6-9]).*Hector.*VA\.xlsx",
        "category": "claims_van_ameyde",
    },
    {
        "name": "hector_xlsx_claims_van_ameyde_v2",
        "factory": HectorClaimsVanAmeydeXLSXToDataFrameFactoryV2,
        # Placeholder for hector regex
        "regexp": r"^\d{2}.\d{2}.\d{4}.*Hector.*VA\.xlsx",
        "category": "claims_van_ameyde",
    },
]
