from mock import MagicMock

from loader.core.sqlalchemy_utils import create_schema_if_not_exist


def test_create_schema_if_not_exist():

    no_schema_engine = MagicMock(
        dialect=MagicMock(has_schema=lambda x, y: False),
    )
    schema_engine = MagicMock(
        dialect=MagicMock(has_schema=lambda x, y: True),
    )

    create_schema_if_not_exist(no_schema_engine, "dummy_db", "dummy_schema")
    create_schema_if_not_exist(schema_engine, "dummy_db", "dummy_schema")

    no_schema_engine.execute.assert_called_once()
    schema_engine.execute.assert_not_called()
