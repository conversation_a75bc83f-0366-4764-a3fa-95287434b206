#!/usr/bin/python
# -*- coding: utf-8 -*-

import pytest

from mock import Mock

from loader.core.csv_pandas import CSVToDataFrameFactory
from loader.core.etl import get_applicable_factory, get_filetype
from loader.aerial.factory import *
from loader.domcura.factory import *
from loader.hector.factory import *
from loader.prima.factory import *
from loader.solera.factory import *
from loader.toni.factory import *
from loader.gallen.factory import *


@pytest.mark.parametrize(
    "file_input, file_name, expected_file_type",
    [
        ("domcura", "IPTIQ_efh_2018_01.csv", None),
        ("domcura", "IPTIQ_zlx_2018_01-01.csv", None),
        ("domcura", "iptiQ_01_2018.xlsx", "premium"),
        ("random", "iptiQ_01_2018.xlsx", None),
        ("domcura", "IPTIQ_zlx_2018_01-01.xlsx", None),
        ("domcura", "TEST_zlx_01_2018.xlsx", None),
        ("domcura", "IPTIQ_Beschwerdeübersicht_2020_08_31.xlsx", "complaints"),
        ("domcura", "IPTIQ_S_2020_09_01.csv", "claims"),
        ("prima", "07. July - Premia IT.xlsx", "premium"),
        ("prima", "08. August - Claims IT.xlsx", "claims"),
        ("domcura", "IPTIQ_S_2020_07_07.csv", "claims"),
        ("prima", "2021/01. January - Premia IT household.xlsx", "premium"),
        ("domcura", "iptiQ_05_2021_DC_Inno.xlsx", "premium"),
        ("prima", "03. March - Claims Household.xlsx", "claims"),
        ("prima", "2021/04. April - Premia IT household.xlsx", "premium"),
        ("prima", "2021/04. April - Complaints DE.xlsx", "complaints"),
        ("prima", "04. April - Complaints DE.xlsx", "complaints"),
        ("prima", "10. October - Complaints IT.xlsx", "complaints"),
        ("prima", "07. July - Claims Household.xlsx", "claims"),
        ("prima", "2021/07. July - Premia DE.xlsx", "premium"),
        ("prima", "2022/01. January - Premia DE adj.xlsx", "premium"),
        ("prima", "2022/02. January - Premia DE adj.xlsx", "premium"),
        ("domcura", "iptiQ_05_2021_DC_Inno_NV.xlsx", "premium"),
        ("prima", "2022/Archivio Reclami 10 2022 - iptiQ Italia.xlsx", "complaints"),
        ("prima", "2022/Archivio Reclami 2022 - iptiQ Germania_10_22.xlsx", "complaints"),
        ("aerial", "11-IPTIQ-NOVEMBRE-2022.xlsx", "premium"),
        ("aerial", "08-IPTIQ-AOUT-2023-V2.xlsx", "premium"),
        ("aerial", "09-IPTIQ-SEPTEMBRE-2023 V2.xlsx", "premium"),
        ("aerial", "04-Claims-IPTIQ-AVRIL-2023.xlsx", "claims"),
        ("aerial", "2023_06-Complaints_IPTIQ-AERIAL.xlsx", "complaints"),
        ("prima", "spain/2022/11. November Premia ES.xlsx", "premium_spain"),
        ("prima", "spain/2022/10. October Premia ES_v2.xlsx", "premium_spain"),
        ("domcura", "DOM- 31.01.2023 - large loss Overview.xlsx", "claims_insis"),
        ("prima", "2023/02. January - Premia DE adj.xlsx", "premium"),
        ("prima", "spain/2023_11_november_premia_es_vshared_04122023_v2.xlsx", "premium_spain"),
        ("domcura", "BDX VA 31.01.2023.xlsx", "claims_van_ameyde"),
        ("prima", "spain/2023_01_january_claims_es_bdx_report_vshared_09102023.xlsx", "claims_spain"),
        ("prima", "spain/2023_07_july_claims_es_bdx_report_vshared_08092023.xlsx", "claims_spain"),
        ("prima", "spain/2023/04. April Complaints ES.xlsx", "complaints_spain"),
        ("prima", "spain/2024_03_march_qyr_es_vshared_12042024.xlsx", "complaints_spain"),
        ("solera", "IPTIQ_polis_202401_A759_01.csv", "policies"),
        ("solera", "IPTIQ_polis_dekking_202401_A759_01.csv", "covers"),
        ("solera", "IPTIQ_polis_XO_202401_A759_01.csv", "insured_objects"),
        ("hector", "23-10-08 Reporting_PREMIUM_09.csv", "policies"),
        ("solera", "IPTIQ_schade_202401_A759_01.csv", "claims"),
        ("hector", "2023-09 Reporting_CLAIMS_09.csv", "claims"),
        ("hector", "2024-01_Reporting_CLAIMS_1_24.csv", "claims"),
        ("hector", "2024-02_Reporting_CLAIMS_2_24.csv", "claims"),
        ("hector", "31.03.2024 - Van Ameyde (Hector) BDX..xlsx", "claims_van_ameyde"),
        ("hector", "2023-09 Finance Report.xlsx", "premium"),
        ("hector", "24-02_Booking_Report.xlsx", "premium"),
        ("hector", "iptiq_individuals-companies_export.csv", "sanctions"),
        ("hector", "2023-09 Reporting_COMPLAINTS_09.xlsx", "complaints"),
        ("solera", "IPTIQ_FINANCIEEL_FT_202312_1.csv", "premium"),
        ("solera", "IPTIQ_FINANCIEEL_AL_202401_1.csv", "financial"),
        (
            "domcura",
            "domcura_00589_zahlungsdaten_terror_sanktionspruefung_gewerbe_202309120727.xls",
            "sanctions_firmen",
        ),
        ("domcura", "domcura_00589_zahlungsdaten_terror_sanktionspruefung_privat_202309120725.xls", "sanctions_privat"),
        ("domcura", "IPTIQ_EFH_RH_2023_06_16.csv", "efh_rh"),
        ("toni", "202401_iptiQ_policy_premium_bdx.csv", "policies"),
        ("toni", "202401_iptiQ_claim_bdx.csv", "claims"),
        ("gallen", "BORDERAUX_20240415_115043.csv", "policies")
    ],
)
def test_get_filetype(file_input, file_name, expected_file_type):
    file_type = get_filetype(file_input, file_name)
    if expected_file_type is None:
        assert file_type is expected_file_type
    else:
        assert file_type == expected_file_type


@pytest.mark.parametrize(
    "file_path, expected_factory",
    [
        ("domcura/IPTIQ_efh_2020_02.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_03.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_04.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_05.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_05_01.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_06.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2030_02.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_06_02.csv", CSVToDataFrameFactory),
        ("domcura/iptiQ_06_2020.xlsx", DomcuraXLSXToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_b6_01.csv", CSVToDataFrameFactory),
        ("test/www", CSVToDataFrameFactory),
        ("domcura/IPTIQ_efh_2020_05.xlsx", CSVToDataFrameFactory),
        ("test/IPTIQ_efh_2020_06.csv", CSVToDataFrameFactory),
        ("domcura/IPTIQ_Beschwerdeübersicht_2020_08_31.xlsx", DomcuraComplaintsTOXLSXToDataFrameFactory),
        ("domcura/IPTIQ_S_2020_07_07.csv", DomcuraClaimsTOCSVToDataFrameFactory),
        ("domcura/IPTIQ_S_2020_09_01.csv", DomcuraClaimsV2TOCSVToDataFrameFactory),
        ("prima/07. July - Premia IT.xlsx", PrimaPolicyXLSXToDataFrameFactory),
        ("prima/08. August - Claims DE.xlsx", PrimaClaimsXLSXToDataFrameFactory),
        ("prima/12. December - Claims IT Amended.xlsx", PrimaClaimsXLSXV2ToDataFrameFactory),
        ("prima/2021/01. January - Claims DE amended.xlsx", PrimaClaimsXLSXV2ToDataFrameFactory),
        ("prima/08. July - Premia DE.xlsx", PrimaPolicyXLSXV2ToDataFrameFactory),
        ("prima/2021/01. January - Premia IT household.xlsx", PrimaPolicyHouseholdXLSXToDataFrameFactory),
        ("prima/2021/04. April - Premia IT household.xlsx", PrimaPolicyHouseholdXLSXV2ToDataFrameFactory),
        ("prima/2021/04. April - Complaints DE.xlsx", PrimaComplaintsXLSXV2ToDataFrameFactory),
        ("prima/04. April - Complaints DE.xlsx", PrimaComplaintsXLSXV2ToDataFrameFactory),
        ("prima/10. October - Complaints DE.xlsx", PrimaComplaintsXLSXToDataFrameFactory),
        ("prima/2021/12. December - Complaints DE_adj.xlsx", PrimaComplaintsXLSXV2ToDataFrameFactory),
        ("prima/2021/12. December - Complaints IT_adj.xlsx", PrimaComplaintsXLSXV2ToDataFrameFactory),
        ("domcura/iptiQ_05_2021_DC_Inno.xlsx", DomcuraPremiumXLSXToDataFrameFactory),
        ("domcura/iptiQ_05_2021_DC_Inno_NV.xlsx", DomcuraPremiumXLSXToDataFrameFactory),
        ("prima/2021/07. July - Claims Household.xlsx", PrimaClaimsXLSXHouseholdV2ToDataFrameFactory),
        ("prima/2021/06. June - Claims Household.xlsx", PrimaClaimsXLSXHouseholdToDataFrameFactory),
        ("prima/2022/06. June - Claims Household.xlsx", PrimaClaimsXLSXHouseholdV2ToDataFrameFactory),
        ("prima/2021/07. July - Premia DE.xlsx", PrimaPolicyXLSXV3ToDataFrameFactory),
        ("prima/2022/01. January - Premia DE adj.xlsx", PrimaPolicyXLSXV4ToDataFrameFactory),
        ("prima/2022/02. January - Premia DE adj.xlsx", PrimaPolicyXLSXV5ToDataFrameFactory),
        ("prima/2024/04. April - Premia IT motor.xlsx", PrimaPolicyXLSXV7ToDataFrameFactory),
        ("prima/2022/Archivio Reclami 01 2022 - iptiQ Germania.xlsx", PrimaComplaintsXLSXV2ToDataFrameFactory),
        ("prima/2022/Archivio Reclami 2022 - iptiQ Italia_10_22.xlsx", PrimaComplaintsXLSXV2ToDataFrameFactory),
        ("aerial/11-IPTIQ-NOVEMBRE-2022.xlsx", AerialXLSXToDataFrameFactory),
        ("aerial/08-IPTIQ-AOUT-2023-V2.xlsx", AerialXLSXToDataFrameFactoryV2),
        ("aerial/09-IPTIQ-SEPTEMBRE-2023 V2.xlsx", AerialXLSXToDataFrameFactoryV2),
        ("aerial/04-Claims-IPTIQ-AVRIL-2023.xlsx", AerialClaimsXLSXToDataFrameFactory),
        ("aerial/2023_06-Complaints_IPTIQ-AERIAL.xlsx", AerialComplaintsXLSXToDataFrameFactory),
        ("domcura/DOM- 31.01.2023 - large loss Overview.xlsx", DomcuraClaimsInsisXLSXToDataFrameFactory),
        ("domcura/BDX VA 31.01.2023.xlsx", DomcuraClaimsVanAmeydeXLSXToDataFrameFactory),
        ("prima/2023/02. February - Premia DE adj.xlsx", PrimaPolicyXLSXV5ToDataFrameFactory),
        ("prima/spain/2022/10. October Premia ES vShared 01312023_v2.xlsx", PrimaPolicySpainXLSXToDataFrameFactory),
        ("prima/spain/2023/05. May Premia ES.xlsx", PrimaPolicySpainXLSXToDataFrameFactory),
        ("prima/spain/2023_12_december_premia_es_vshared_03012024.xlsx", PrimaPolicySpainXLSXV2ToDataFrameFactory),
        ("prima/spain/2023_07_july_claims_es_bdx_report_vshared_08092023.xlsx", PrimaClaimsSpainXLSXToDataFrameFactory),
        ("prima/spain/2023/04. April Complaints ES.xlsx", PrimaComplaintsSpainXLSXToDataFrameFactory),
        ("prima/spain/2024_03_march_qyr_es_vshared_12042024.xlsx", PrimaComplaintsSpainXLSXV2ToDataFrameFactory),
        ("solera/IPTIQ_polis_202401_A759_01.csv", SoleraPolisCSVToDataFrameFactory),
        ("solera/IPTIQ_polis_dekking_202401_A759_01.csv", SoleraDekkingCSVToDataFrameFactory),
        ("solera/IPTIQ_polis_XO_202401_A759_01.csv", SoleraXoCSVToDataFrameFactory),
        ("hector/23-10-08 Reporting_PREMIUM_09.csv", HectorPolicyCSVLegacyToDataFrameFactory),
        ("hector/24-02-08_Reporting_PREMIUM_1_24.csv", HectorPolicyCSVToDataFrameFactory),
        ("solera/IPTIQ_schade_202401_A759_01.csv", SoleraSchadeCSVToDataFrameFactory),
        ("hector/2023-09 Reporting_CLAIMS_09.csv", HectorClaimsCSVToDataFrameFactory),
        ("hector/2024-01_Reporting_CLAIMS_1_24.csv", HectorClaimsCSVToDataFrameFactory),
        ("hector/2024-02_Reporting_CLAIMS_2_24.csv", HectorClaimsCSVToDataFrameFactoryV2),
        ("hector/31.03.2024 - Van Ameyde (Hector) BDX..xlsx", HectorClaimsVanAmeydeXLSXToDataFrameFactory),
        ("hector/2023-09 Finance Report.xlsx", HectorPremiumXLSXToDataFrameFactory),
        ("hector/24-02_Booking_Report.xlsx", HectorPremiumXLSXToDataFrameFactoryV2),
        ("hector/iptiq_individuals-companies_export.csv", HectorSanctionsCSVToDataFrameFactory),
        ("hector/2023-09 Reporting_COMPLAINTS_09.xlsx", HectorComplaintsXLSXToDataFrameFactory),
        ("solera/IPTIQ_FINANCIEEL_FT_202312_1.csv", SoleraPremiumFTCSVToDataFrameFactory),
        ("solera/IPTIQ_FINANCIEEL_AL_202401_1.csv", SoleraPremiumALCSVToDataFrameFactory),
        ("prima/spain/2023_07_july_claims_es_bdx_report_vshared_08092023.xlsx", PrimaClaimsSpainXLSXToDataFrameFactory),
        (
            "domcura/domcura_00589_zahlungsdaten_terror_sanktionspruefung_privat_202309120725.xls",
            DomcuraSanctionsPrivatXLSXToDataFrameFactory,
        ),
        (
            "domcura/domcura_00589_zahlungsdaten_terror_sanktionspruefung_gewerbe_202309120727.xls",
            DomcuraSanctionsFirmenXLSXToDataFrameFactory,
        ),
        ("domcura/IPTIQ_EFH_RH_2023_06_16.csv", DomcuraPolicyDTOV2CSVToDataFrameFactory),
        ("toni/202401_iptiQ_policy_premium_bdx.csv", ToniPolicyCSVToDataFrameFactory),
        ("toni/202401_iptiQ_claim_bdx.csv", ToniClaimsCSVToDataFrameFactory),
        ("gallen/BORDERAUX_20240415_115043.csv", GallenPremiumCSVToDataFrameFactory)
    ],
)
def test_get_applicable_factory(file_path, expected_factory):
    mock_s3object = Mock(key=file_path)
    applicable_factory = get_applicable_factory(mock_s3object)
    assert applicable_factory == expected_factory
