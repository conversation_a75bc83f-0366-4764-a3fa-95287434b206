from datetime import date

from mock import Mock

from loader.core.s3 import (
    add_or_update_processed_tag,
    extract_md5_hash_from_s3object,
    extract_md5_hash_from_tags,
    extract_mod_time_from_tags,
    get_all_objects_to_process,
    get_s3_file_path,
    extract_bucket_and_key_from_s3_url,
    read_s3_object
)


def test_add_s3_processed_tags():
    """
    Checks if the tags are created with expected tagsets.
    """
    basic_tagset = {"TagSet": [{"Key": "Name", "Value": "John doe"}]}
    expected_tagset = {
        "TagSet": [
            {"Key": "Name", "Value": "John doe"},
            {"Key": "processed", "Value": str(date.today())},
        ]
    }

    processed_tagset = add_or_update_processed_tag(basic_tagset)
    assert processed_tagset == expected_tagset


def test_upsert_s3_processed_tags():
    """
    Checks if tag is updated with the current date
    """

    basic_tagset = {
        "TagSet": [
            {"Key": "Name", "Value": "John doe"},
            {"Key": "processed", "Value": "2019-01-01"},
        ]
    }

    expected_tagset = {
        "TagSet": [
            {"Key": "Name", "Value": "<PERSON> doe"},
            {"Key": "processed", "Value": str(date.today())},
        ]
    }

    processed_tagset = add_or_update_processed_tag(basic_tagset)
    assert processed_tagset == expected_tagset


def test_extract_md5_hash_from_tags():
    """
    Checks if hash is extracted from tag as expected
    """

    basic_tagset = {
        "TagSet": [
            {"Key": "Name", "Value": "John doe"},
            {"Key": "MD5", "Value": "MyMD5"},
        ]
    }
    md5hash = extract_md5_hash_from_tags(basic_tagset)
    assert md5hash == "MyMD5"

    basic_tagset_no_md5 = {
        "TagSet": [
            {"Key": "Name", "Value": "John doe"},
        ]
    }
    no_md5hash = extract_md5_hash_from_tags(basic_tagset_no_md5)
    assert no_md5hash is None


def test_extract_mod_time_from_tags():
    """
    Checks if mod time is extracted from tag as expected
    """
    basic_tagset = {
        "TagSet": [
            {"Key": "Name", "Value": "John doe"},
            {"Key": "MOD_TIME", "Value": "1234"},
        ]
    }
    modtime = extract_mod_time_from_tags(basic_tagset)
    assert modtime == 1234


def test_get_all_objects_to_process():
    """
    Checks if all objects are fetched for processing
    """

    obj_iter = iter([Mock(), Mock(), Mock()])
    mock_objects = Mock(all=lambda: obj_iter)
    mock_bucket = Mock(objects=mock_objects)
    all_obj = get_all_objects_to_process(mock_bucket)

    assert len(all_obj) == 3


def test_extract_md5_hash_from_s3object():
    """
    Checks if hash is extracted from S3 Bucket as expected
    """

    basic_tagset = {
        "TagSet": [
            {"Key": "Name", "Value": "John doe"},
            {"Key": "MD5", "Value": "MyMD5"},
        ]
    }
    mock_s3client = Mock(get_object_tagging=lambda Bucket, Key: basic_tagset)
    mock_s3obj = Mock()
    md5_hash = extract_md5_hash_from_s3object(mock_s3client, mock_s3obj)
    assert md5_hash == "MyMD5"


def test_get_s3_file_path():
    """
    Checks if the path from file stored in S3 is as expected
    """
    mock_s3obj = Mock(bucket_name="test", key="dummy.file")
    s3filepath = get_s3_file_path(mock_s3obj)
    assert s3filepath == "s3://test/dummy.file"


def test_extract_bucket_and_key_from_s3_url():
    s3_url = "s3://test/sample_folder/dummy.file"
    bucket, key = extract_bucket_and_key_from_s3_url(s3_url)

    assert bucket == "test"
    assert key == "sample_folder/dummy.file"


def test_read_s3_object():
    s3_client_mock = Mock()

    expected_content = b"Dummy content"
    s3_client_mock.get_object.return_value = {"Body": Mock(read=Mock(return_value=expected_content))}

    bucket = "test"
    key = "sample_key"
    content = read_s3_object(s3_client_mock, bucket, key)

    s3_client_mock.get_object.assert_called_once_with(Bucket=bucket, Key=key)

    assert content == expected_content
