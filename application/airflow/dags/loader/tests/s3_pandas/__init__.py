import pandas as pd  # type: ignore
import pytest
from mock import <PERSON><PERSON><PERSON>, Mock, mock

from loader.core.s3_pandas import add_s3_tags_to_df, read_magic_from_s3, s3_get_object


def get_mock_s3_client(body):
    mock_s3object_body = Mock(read=MagicMock(return_value=body))
    mock_s3_get_object_response = MagicMock(__getitem__=lambda x, v: mock_s3object_body)

    def mock_get_object(Bucket=None, Key=None):
        pass

    mock_s3client = MagicMock(
        get_object=mock.create_autospec(mock_get_object, return_value=mock_s3_get_object_response)
    )
    return mock_s3client


@pytest.fixture(scope="session")
def mock_s3object():
    return Mock(key="123", e_tag="345", bucket_name="mybucket")


@pytest.fixture(scope="session")
def df():
    return pd.DataFrame()


def test_add_s3_tags_to_df(df, mock_s3object):
    df = add_s3_tags_to_df(df, mock_s3ob<PERSON>)
    assert df.s3object_bucket_name == "mybucket"
    assert df.s3object_key == "123"
    assert df.s3object_e_tag == "345"


def test_s3_get_object(mock_s3object):
    mock_s3client = get_mock_s3_client(b"success")
    obj = s3_get_object(mock_s3client, mock_s3object)

    assert obj == b"success"


def test_read_magic_from_s3(mock_s3object):
    mock_s3client = Mock()
    mock_s3obj = mock_s3object
    mock_csv_to_df_factory = Mock()
    read_magic_from_s3(mock_s3client, mock_s3obj, mock_csv_to_df_factory)
    mock_csv_to_df_factory.assert_called_once()
