import datetime
import io

import numpy as np  # type: ignore
import pandas as pd  # type: ignore
import pytest
from pytest_lazyfixture import lazy_fixture
from sqlalchemy.types import DateTime, Float, Integer, String  # type: ignore

from loader.core.utils import remove_umlaut
from loader.core.utils.dto import get_sqlalchemy_types_from_dto
from loader.domcura.dto.policy import DomcuraCSVPolicyDTO, DomcuraCSVPolicyDTOV2
from loader.domcura.dto.premium import DomcuraXLSXPremiumDTO
from loader.domcura.factory import (
    DomcuraCSVToDataFrameFactory,
    DomcuraPolicyDTOV2CSVToDataFrameFactory,
    DomcuraPremiumXLSXToDataFrameFactory,
    DomcuraXLSXToDataFrameFactory,
)
from tests.domcura.domcura_sample_row import (
    domcura_policy_sample_row,
    domcura_sample_premium_row,
    domcura_sample_premium_row_b,
)


def setup_policy_samples(sample_row):
    """
    Generate policy samples for <PERSON><PERSON>ra
    """

    valid_sample = "\n".join(
        [
            "|".join([k for k in sample_row.keys()]),
            "|".join([v for v in sample_row.values()]),
        ]
    ).encode()
    return DomcuraCSVToDataFrameFactory(io.BytesIO(valid_sample))


def setup_dfs_policy():
    """
    Setup Domcura Policy Samples
    """
    csv_factory = setup_policy_samples(domcura_policy_sample_row)
    df_policy = csv_factory.get_dataframe()

    domcura_policy_sample_row_v2 = {k: v for k, v in domcura_policy_sample_row.items() if k not in ("Bauart", "Dach")}
    domcura_policy_sample_row_v2["Bauartklasse"] = "SAMPLEBAURTKLASS"
    csv_factory_02 = setup_policy_samples(domcura_policy_sample_row_v2)
    df_policy_02 = csv_factory_02.get_dataframe()

    return df_policy, df_policy_02


def setup_sum_cols(x, y):
    """
    Setup sum cols
    """
    return y if x in {"Anteil", "Netto", "VST", "Brutto", "Courtage", "Abrechnungsbetrag"} else None


def setup_dfs_premium():
    """
    Setup Domcura Premium Samples
    """
    domcura_premium_sum_row = {k: setup_sum_cols(k, v) for k, v in domcura_sample_premium_row.items()}
    records = [domcura_sample_premium_row, domcura_premium_sum_row]

    tmp_df = pd.DataFrame(records)
    premium_xlsx_bytes = io.BytesIO()
    tmp_df.to_excel(premium_xlsx_bytes, index=False)
    premium_df_factory = DomcuraXLSXToDataFrameFactory(premium_xlsx_bytes)
    premium_df = premium_df_factory.get_dataframe()

    domcura_premium_sum_row_sheet_2 = {k: setup_sum_cols(k, v) for k, v in domcura_sample_premium_row_b.items()}
    records_sheet_2 = [
        domcura_sample_premium_row_b,
        domcura_premium_sum_row_sheet_2,
    ]

    # Setting up a bytes object as a 2 sheets XL workbook
    new_premium_xlsx_bytes = io.BytesIO()
    writer = pd.ExcelWriter(new_premium_xlsx_bytes, engine="xlsxwriter")
    tmp_df_new_premium = pd.DataFrame(records_sheet_2)
    tmp_df.to_excel(writer, sheet_name="random12", index=False)
    tmp_df_new_premium.to_excel(writer, sheet_name="random13", index=False)
    writer.close()

    new_premium_df_factory = DomcuraPremiumXLSXToDataFrameFactory(new_premium_xlsx_bytes)
    new_premium_df = new_premium_df_factory.get_dataframe()

    return premium_df, new_premium_df


@pytest.fixture(scope="session")
def policy_df():
    """
    Sample policy df v1
    """
    return setup_dfs_policy()[0]


@pytest.fixture(scope="session")
def policy_df_02():
    """
    Sample policy df v2
    """
    return setup_dfs_policy()[1]


@pytest.fixture(scope="session")
def premium_df():
    """
    Sample premium df v1
    """
    return setup_dfs_premium()[0]


@pytest.fixture(scope="session")
def premium_df_02():
    """
    Sample premium df v2
    """
    return setup_dfs_premium()[1]


@pytest.mark.parametrize(
    "new_premium_values",
    [
        {
            "VSNR": ["DCDO-04-0001010", "DCDO-04-0001012"],
            "VN_Name1": ["Vorname1", "Vorname2"],
        }
    ],
)
def test_column_values(premium_df_02, new_premium_values):
    """
    Check if column values match expected values
    """
    for column_name, column_values in new_premium_values.items():
        assert (
            np.array_equal(
                premium_df_02[column_name].values,
                np.array(column_values),
            )
            is True
        )


@pytest.mark.parametrize(
    "df,expected_count",
    [
        (lazy_fixture("policy_df"), 1),
        (lazy_fixture("policy_df_02"), 1),
        (lazy_fixture("premium_df"), 1),
        (lazy_fixture("premium_df_02"), 2),
    ],
)
def test_row_count(df, expected_count):
    """
    Check if row counts match expected values for each sample
    """
    row_cnt, col_cnt = df.shape
    assert row_cnt == expected_count


@pytest.mark.parametrize(
    "df,df_column_cnt",
    [
        (lazy_fixture("policy_df"), 45),
        (lazy_fixture("policy_df_02"), 44),
        (lazy_fixture("premium_df"), 22),
        (lazy_fixture("premium_df_02"), 22),
    ],
)
def test_column_count(df, df_column_cnt):
    """
    Check if column counts match expected values for each sample
    """
    column_count = len(df.columns)
    assert column_count == df_column_cnt


@pytest.mark.parametrize(
    "dto,df",
    [
        (DomcuraCSVPolicyDTO, lazy_fixture("policy_df")),
        (DomcuraCSVPolicyDTOV2, lazy_fixture("policy_df_02")),
        (DomcuraXLSXPremiumDTO, lazy_fixture("premium_df")),
        (DomcuraXLSXPremiumDTO, lazy_fixture("premium_df_02")),
    ],
)
def test_column_names_and_dto(dto, df):
    """
    Check if column names match expected values for each sample
    """
    dto_fields = set([remove_umlaut(k) for k in dto.__dataclass_fields__])
    df_fields = set(k for k in df.columns)
    assert dto_fields == df_fields


@pytest.mark.parametrize(
    "df, datetime_fields",
    [
        (
            lazy_fixture("policy_df"),
            {
                "Geburtstag": "datetime64[ns]",
                "akt_Vertragsablauf": "datetime64[ns]",
                "Vertragsbeginn": "datetime64[ns]",
                "GF_Einschluss": "datetime64[ns]",
                "Beginn_VU": "datetime64[ns]",
                "GF_Ausschluss": "O",
                # The number is outside the range for datetime64, a datetime object is used instead
            },
        ),
        (
            lazy_fixture("policy_df_02"),
            {
                "Geburtstag": "datetime64[ns]",
                "akt_Vertragsablauf": "datetime64[ns]",
                "Vertragsbeginn": "datetime64[ns]",
                "GF_Einschluss": "datetime64[ns]",
                "Beginn_VU": "datetime64[ns]",
                "GF_Ausschluss": "O",
                # The number is outside the range for datetime64, a datetime object is used instead
            },
        ),
    ],
)
def test_policy_datetime_types(df, datetime_fields):
    """
    Check if datetime columns have the correct data type
    """
    for k, v in datetime_fields.items():
        assert df[k].dtype == v

    assert isinstance(df["GF_Ausschluss"][0], datetime.datetime)


@pytest.mark.parametrize(
    "df, date_colname",
    [
        (
            lazy_fixture("policy_df"),
            {
                "Geburtstag": "1968-07-19",
                "Vertragsbeginn": "2001-05-01",
                "akt_Vertragsablauf": "2019-12-31",
                "GF_Einschluss": "2017-12-31",
                "GF_Ausschluss": "2999-12-31",
                "Beginn_VU": "2017-12-31",
            },
        ),
        (
            lazy_fixture("policy_df_02"),
            {
                "Geburtstag": "1968-07-19",
                "Vertragsbeginn": "2001-05-01",
                "akt_Vertragsablauf": "2019-12-31",
                "GF_Einschluss": "2017-12-31",
                "GF_Ausschluss": "2999-12-31",
                "Beginn_VU": "2017-12-31",
            },
        ),
    ],
)
def test_policy_datetime_values_policy(df, date_colname):
    """
    Check if datetime columns have the expected datetime values for policy samples
    """
    for col_name, expected_val in date_colname.items():
        assert df[col_name][0] == datetime.datetime.strptime(expected_val, "%Y-%m-%d")


def test_policy_datetime_values_premium(premium_df):
    """
    Check if datetime columns have the expected datetime values for premium samples
    """
    assert premium_df["von"][0] == datetime.datetime(2020, 5, 1)
    assert premium_df["bis"][0] == datetime.datetime(2022, 1, 5)


@pytest.mark.parametrize("string_colname", ["Produktname", "Zahlweise"])
def test_string_types(policy_df, policy_df_02, string_colname):
    """
    Check if string columns have the expected data types
    """
    assert isinstance(policy_df[string_colname][0], str)
    assert isinstance(policy_df_02[string_colname][0], str)


@pytest.mark.parametrize(
    "string_values",
    [
        "Stornogrund",
        "Selbstbehalt",
        "Klauseln1",
        "Klauseln2",
        "Gebaeudealteralterrabatt",
        "Vorvertragsnummer",
        "Anzahl_Vorschaeden",
        "Vorschadenhoehe",
    ],
)
def test_string_values(policy_df, policy_df_02, string_values):
    """
    Check if string columns have the expected values
    """
    assert pd.isna(policy_df[string_values][0]) is True
    assert pd.isna(policy_df_02[string_values][0]) is True
    assert policy_df["Bauart"][0] == "Holz"
    assert policy_df_02["Bauartklasse"][0] == "SAMPLEBAURTKLASS"


@pytest.mark.parametrize(
    "df, expected",
    [
        (
            lazy_fixture("policy_df"),
            {
                "VSNR": "CCC-000000163-I2",
                "Anrede": "Herrn",
                "VN_Name1": "Vorname1",
                "VN_Name2": "Nachname1",
                "VN_PLZ": "65263",
                "VN_Strasse": "Strasse1",
                "Vertragsstatus": "aktiv",
                "Hauptfaelligkeit": "3112",
                "Gesellschaft_Name": "XXX Versicherungs-AG",
                "Agenturnummer": "DK1-XXX-0428-69/1829",
                "Bedingungen": "P04013",
                "Risiko": "D;65263 Stadt1;Strasse1",
                "Gefahr": "Elementar",
                "GF_Status": "aktiv",
                "Nutzung": "privat",
                "VM_NR": "00500",
            },
        ),
        (
            lazy_fixture("policy_df_02"),
            {
                "VSNR": "CCC-000000163-I2",
                "Anrede": "Herrn",
                "VN_Name1": "Vorname1",
                "VN_Name2": "Nachname1",
                "VN_PLZ": "65263",
                "VN_Strasse": "Strasse1",
                "Vertragsstatus": "aktiv",
                "Hauptfaelligkeit": "3112",
                "Gesellschaft_Name": "XXX Versicherungs-AG",
                "Agenturnummer": "DK1-XXX-0428-69/1829",
                "Bedingungen": "P04013",
                "Risiko": "D;65263 Stadt1;Strasse1",
                "Gefahr": "Elementar",
                "GF_Status": "aktiv",
                "Nutzung": "privat",
                "VM_NR": "00500",
            },
        ),
    ],
)
def test_string_values_02(df, expected):
    """
    Check if string columns have the expected values
    """
    for column, value in expected.items():
        assert df[column][0] == value


@pytest.mark.parametrize(
    "numeric_colname",
    ["Jahresnetto", "Jahresbrutto", "GF_Netto_VN", "Vers_Steuer", "GF_Brutto_VN", "Baujahr", "Tarifzone", "ZUeRS_Zone"],
)
def test_numeric_types(policy_df, policy_df_02, numeric_colname):
    """
    Check if numeric columns have the expected data types
    """
    assert policy_df[numeric_colname].dtype == "float32"
    assert policy_df_02[numeric_colname].dtype == "float32"


@pytest.mark.parametrize(
    "numeric_colname, expected_val",
    [
        ("Jahresnetto", np.float32(284.83)),
        ("Jahresbrutto", np.float32(331.37)),
        ("GF_Netto_VN", np.float32(49.82)),
        ("Vers_Steuer", np.float32(8.14)),
        ("GF_Brutto_VN", np.float32(57.96)),
        ("Wohnflaeche", np.int32(100)),
        ("Baujahr", np.float32(1987)),
        ("Tarifzone", np.float32(4)),
        ("ZUeRS_Zone", np.float32(1)),
    ],
)
def test_numeric_values(policy_df, policy_df_02, numeric_colname, expected_val):
    """
    Check if numeric columns have the expected values
    """
    assert len(policy_df) > 0, "Dataframe is empty"
    assert len(policy_df_02) > 0, "Dataframe is empty"
    assert pd.isna(policy_df["Gebaeudealteralterrabatt"][0]) is True
    assert pd.isna(policy_df_02["Gebaeudealteralterrabatt"][0]) is True

    assert policy_df[numeric_colname][0] == expected_val
    assert policy_df_02[numeric_colname][0] == expected_val


@pytest.mark.parametrize(
    "numeric_colname, expected_type",
    [
        ("jahresnetto", Float),
        ("jahresbrutto", Float),
        ("gf_netto_vn", Float),
        ("vers_steuer", Float),
        ("gf_brutto_vn", Float),
        ("gebaeudealteralterrabatt", Float),
        ("wohnflaeche", Integer),
        ("baujahr", Integer),
        ("tarifzone", Integer),
        ("zuers_zone", Integer),
        ("vsnr", String),
        ("anrede", String),
        ("vn_name1", String),
        ("vn_name2", String),
        ("vn_plz", String),
        ("vn_ort", String),
        ("stornogrund", String),
        ("selbstbehalt", String),
        ("klauseln1", String),
        ("klauseln2", String),
        ("dach", String),
        ("vorvertragsnummer", String),
        ("anzahl_vorschaeden", String),
        ("vorschadenhoehe", String),
        ("produktname", String),
        ("zahlweise", String),
        ("vertragsstatus", String),
        ("gesellschaft_name", String),
        ("hauptfaelligkeit", String),
        ("agenturnummer", String),
        ("bedingungen", String),
        ("risiko", String),
        ("bauart", String),
        ("gefahr", String),
        ("gf_status", String),
        ("vorversicherer", String),
        ("vorschadenhoehe", String),
        ("nutzung", String),
        ("vm_nr", String),
        ("geburtstag", DateTime),
        ("akt_vertragsablauf", DateTime),
        ("vertragsbeginn", DateTime),
        ("gf_einschluss", DateTime),
        ("gf_ausschluss", DateTime),
        ("beginn_vu", DateTime),
    ],
)
def test_policy_sqlalchemy_postgres_types(numeric_colname, expected_type):
    """
    Check if dto types match expected data types
    """
    sqlalchemy_types = get_sqlalchemy_types_from_dto(setup_policy_samples(domcura_policy_sample_row).interface.dto)
    assert sqlalchemy_types.get(numeric_colname) == expected_type
