import io
from contextlib import nullcontext as does_not_raise

import pytest
from mock import Mock
from pytest_lazyfixture import lazy_fixture

from loader.core.validations import ValidationError, schema_validation, validate_column_names_characters
from loader.domcura.dto.policy import DomcuraCSVPolicyDTO
from loader.domcura.factory import DomcuraCSVToDataFrameFactory
from tests.domcura.domcura_sample_row import domcura_policy_sample_row


def preprocess_df(df):
    df.s3object_key = "dummy_key"
    df.s3object_e_tag = "dummy_etag"
    df["chksum"] = "007"
    df.columns = [c.lower() for c in df.columns]
    return df


def generate_test_df():
    valid_domcura_sample_policy_v1 = "\n".join(
        [
            "|".join([k for k in domcura_policy_sample_row.keys()]),
            "|".join([v for v in domcura_policy_sample_row.values()]),
        ]
    ).encode()

    domcura_csv_factory_policy_v1 = DomcuraCSVToDataFrameFactory(io.BytesIO(valid_domcura_sample_policy_v1))
    domcura_policy_v1_df = domcura_csv_factory_policy_v1.get_dataframe()

    return preprocess_df(domcura_policy_v1_df)


@pytest.fixture(scope="session")
def domcura_policy_v1_df():
    return generate_test_df()


@pytest.fixture(scope="session")
def domcura_policy_v1_df_less_1_column():
    domcura_policy_v1_df = generate_test_df()
    domcura_policy_v1_df_less_1_column = domcura_policy_v1_df.copy()
    del domcura_policy_v1_df_less_1_column["vsnr"]
    return domcura_policy_v1_df_less_1_column


@pytest.fixture(scope="session")
def domcura_policy_v1_df_more_1_column():
    domcura_policy_v1_df = generate_test_df()
    domcura_policy_v1_df_more_1_column = domcura_policy_v1_df.copy()
    domcura_policy_v1_df_more_1_column["dummy_column"] = "dummy value"
    return domcura_policy_v1_df_more_1_column


testdata = [(["1a32", "2j3a"], True), (["1A32", "2j3a"], False), (["1ü32", "2j3a"], False)]


@pytest.mark.parametrize("columns, expected_result", testdata)
def test_validation_column_names(columns, expected_result):
    """
    Assert that non-expected character are not valid column names
    """
    mock_df = Mock(columns=columns)
    mock_df_validation = validate_column_names_characters(mock_df)
    assert mock_df_validation == expected_result


@pytest.mark.parametrize(
    "inp, expectation",
    [
        (lazy_fixture("domcura_policy_v1_df"), does_not_raise()),
        (lazy_fixture("domcura_policy_v1_df_less_1_column"), pytest.raises(ValidationError)),
        (lazy_fixture("domcura_policy_v1_df_more_1_column"), pytest.raises(ValidationError)),
    ],
)
def test_schema_validation(inp, expectation):
    """
    Assert that it should not raise an exception for a valid schema, and that it should raise an exception if a column
    is added or removed from domcura_policy_v1_df
    """
    with expectation:
        schema_validation(preprocess_df(inp), DomcuraCSVPolicyDTO)
