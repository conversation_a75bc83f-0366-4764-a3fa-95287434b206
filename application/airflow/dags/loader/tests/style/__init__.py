import os

import pycodestyle  # type: ignore
import pytest


def get_all_py_files():
    py_files = []
    for root, dirs, files in os.walk("./"):
        for file in files:
            if file.endswith(".py"):
                py_files.append(os.path.join(root, file))
    return py_files


def test_conformance():
    """Test that we conform to PEP-8."""
    style = pycodestyle.StyleGuide(quiet=False, config_file="tox.ini")
    result = style.check_files(get_all_py_files())
    print(result)
    assert result.total_errors == 0, f"Found {result.total_errors} code style errors (and warnings)."
