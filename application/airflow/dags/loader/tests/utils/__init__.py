import pytest
from mock import Mock

from loader.core.utils import get_folder_and_path


def test_get_folder_and_path():
    """
    Asserts that function returns folder and path and throws an error when incomplete
    """

    mock_s3object_1 = Mock(key="123/456")
    mock_s3object_2 = <PERSON><PERSON>(key="123/456/789")
    mock_s3object_3 = Mo<PERSON>(key="123")

    assert get_folder_and_path(mock_s3object_1) == ("123", "456")
    assert get_folder_and_path(mock_s3object_2) == ("123", "456/789")

    with pytest.raises(Exception):
        get_folder_and_path(mock_s3object_3)
