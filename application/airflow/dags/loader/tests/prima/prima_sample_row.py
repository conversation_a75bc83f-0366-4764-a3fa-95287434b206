prima_policy_sample_row = {
    "Agent_Policy_Number": "PRP502558236",
    "Insured_Name": "anonymized",
    "Direct/Reinsurance": "D",
    "Nature_of_contract": "Emissione - Mensile",
    "Location_of_underwriting": "IT",
    "Location_of_Risk": "IT",
    "Class_of_business": 10,
    "Policy_Sequence_Number": "059200/01",
    "Period_start_date": "31.03.2020",
    "Period_end_date": "30.04.2020",
    "Underwriting_Year": "2019",
    "Transaction_Currency": "EUR",
    "Gross_Premium": 80.36,
    "IPT_Rate": "26.50%",
    "IPT_Amount": 21.3,
    "Brokerage": "",
    "Agency_Commission": 11.25,
    "Provisional_Profit_Commission": "",
    "Tax_deducted": None,
    "Net_Balance_due_to_GLUK": 90.41,
    "IPT_Territory": "FR",
    "Targa": "anonymized",
    "Data_emissione": "01.04.2020",
    "Data_Incasso": "01.04.2020",
    "Imposta_SSN": "10.50%",
    "Imposta_SSN_eur": 8.44,
    "Imposta_CVT": "16.00%",
    "Imposta_CVT_eur": 12.86,
    "Imposta_antiracket": "0.00%",
    "Imposta_antiracket_eur": "",
    "Premio_totale": 101.66,
    "Provvigione": "14.00%",
    "Provvigione_eur": 11.25,
    "Insured's address": "anonymized",
    "Comune": "Frosinone",
    "Post_code": "3100",
    "Cod_fiscale": "anonymized",
    "Descrizione_garanzia": "rca",
    "Riassicurazione_Premi": "",
    "Instalment": 5,
    "Veicolo": "car",
    "Tipologia_garanzia_acquistata": "",
    "Frequenza": "monthly",
    "Parent_code": "",
    "Data_emissione_sost": "",
    "Terrorism_Premium": "",
}

prima_policy_sample_row_02 = {
    "Agent_Policy_Number": "PRP502558236",
    "Insured_Name": "anonymized",
    "Direct/Reinsurance": "D",
    "Nature_of_contract": "SosOUT - Mensile",
    "Location_of_underwriting": "IT",
    "Location_of_Risk": "IT",
    "Class_of_business": 10,
    "Policy_Sequence_Number": "059200/01",
    "Period_start_date": "31.03.2020",
    "Period_end_date": "30.04.2020",
    "Underwriting_Year": "2019",
    "Transaction_Currency": "EUR",
    "Gross_Premium": -29.47,
    "IPT_Rate": "26.50%",
    "IPT_Amount": -7.81,
    "Brokerage": "",
    "Agency_Commission": -4.13,
    "Provisional_Profit_Commission": "",
    "Tax_deducted": None,
    "Net_Balance_due_to_GLUK": -33.15,
    "IPT_Territory": "FR",
    "Targa": "anonymized",
    "Data_emissione": "01.04.2020",
    "Data_Incasso": "01.04.2020",
    "Imposta_SSN": "10.50%",
    "Imposta_SSN_eur": -3.09,
    "Imposta_CVT": "16.00%",
    "Imposta_CVT_eur": -4.72,
    "Imposta_antiracket": "0.00%",
    "Imposta_antiracket_eur": "",
    "Premio_totale": -37.28,
    "Provvigione": "14.00%",
    "Provvigione_eur": -4.13,
    "Insured's address": "anonymized",
    "Comune": "Frosinone",
    "Post_code": "3100",
    "Cod_fiscale": "anonymized",
    "Descrizione_garanzia": "rca",
    "Riassicurazione_Premi": "",
    "Instalment": 5,
    "Veicolo": "car",
    "Tipologia_garanzia_acquistata": "",
    "Frequenza": "monthly",
    "Parent_code": "",
    "Data_emissione_sost": "",
    "Terrorism_Premium": "",
}

prima_policy_sample_row_03 = {
    "Agent_Policy_Number": "PRP502558236",
    "Insured_Name": "anonymized",
    "Direct/Reinsurance": "D",
    "Nature_of_contract": "SosIN - Mensile",
    "Location_of_underwriting": "IT",
    "Location_of_Risk": "IT",
    "Class_of_business": 18,
    "Policy_Sequence_Number": "059200/05",
    "Period_start_date": "31.03.2020",
    "Period_end_date": "30.04.2020",
    "Underwriting_Year": "2020",
    "Transaction_Currency": "EUR",
    "Gross_Premium": 1.39,
    "IPT_Rate": "10.00%",
    "IPT_Amount": 0.14,
    "Brokerage": "",
    "Agency_Commission": 0.19,
    "Provisional_Profit_Commission": "",
    "Tax_deducted": None,
    "Net_Balance_due_to_GLUK": 1.33,
    "IPT_Territory": "FR",
    "Targa": "anonymized",
    "Data_emissione": "01.04.2020",
    "Data_Incasso": "01.04.2020",
    "Imposta_SSN": "0.00%",
    "Imposta_SSN_eur": 0,
    "Imposta_CVT": "10.00%",
    "Imposta_CVT_eur": 0.14,
    "Imposta_antiracket": "0.00%",
    "Imposta_antiracket_eur": "",
    "Premio_totale": 1.53,
    "Provvigione": "14.00%",
    "Provvigione_eur": 0.19,
    "Insured's address": "anonymized",
    "Comune": "Frosinone",
    "Post_code": "3100",
    "Cod_fiscale": "anonymized",
    "Descrizione_garanzia": "assistenza_stradale",
    "Riassicurazione_Premi": "",
    "Instalment": 5,
    "Veicolo": "car",
    "Tipologia_garanzia_acquistata": "base",
    "Frequenza": "monthly",
    "Parent_code": "PRP502558236",
    "Data_emissione_sost": "30.11.2019",
    "Terrorism_Premium": "",
}

prima_policy_sample_row_04 = {
    "Agent_Policy_Number": "PRP502558236",
    "Insured_Name": "anonymized",
    "Direct/Reinsurance": "D",
    "Nature_of_contract": "SosIN - Mensile",
    "Location_of_underwriting": "IT",
    "Location_of_Risk": "IT",
    "Class_of_business": 10,
    "Policy_Sequence_Number": "059200/05",
    "Period_start_date": "31.03.2020",
    "Period_end_date": "30.04.2020",
    "Underwriting_Year": "2020",
    "Transaction_Currency": "EUR",
    "Gross_Premium": 46.1,
    "IPT_Rate": "26.5%",
    "IPT_Amount": 12.22,
    "Brokerage": "",
    "Agency_Commission": 6.45,
    "Provisional_Profit_Commission": "",
    "Tax_deducted": None,
    "Net_Balance_due_to_GLUK": 51.86,
    "IPT_Territory": "FR",
    "Targa": "anonymized",
    "Data_emissione": "01.04.2020",
    "Data_Incasso": "01.04.2020",
    "Imposta_SSN": "10.5%",
    "Imposta_SSN_eur": 4.84,
    "Imposta_CVT": "16.00%",
    "Imposta_CVT_eur": 7.38,
    "Imposta_antiracket": "0.00%",
    "Imposta_antiracket_eur": "",
    "Premio_totale": 58.32,
    "Provvigione": "14.00%",
    "Provvigione_eur": 6.45,
    "Insured's address": "anonymized",
    "Comune": "Frosinone",
    "Post_code": "3100",
    "Cod_fiscale": "anonymized",
    "Descrizione_garanzia": "rca",
    "Riassicurazione_Premi": "",
    "Instalment": 5,
    "Veicolo": "car",
    "Tipologia_garanzia_acquistata": "",
    "Frequenza": "monthly",
    "Parent_code": "PRP502558236",
    "Data_emissione_sost": "30.11.2019",
    "Terrorism_Premium": "",
}

prima_policy_sample_row_05 = {
    "Agent_Policy_Number": "PRP502558236",
    "Insured_Name": "anonymized",
    "Direct/Reinsurance": "D",
    "Nature_of_contract": "SosIN - Mensile",
    "Location_of_underwriting": "IT",
    "Location_of_Risk": "IT",
    "Class_of_business": 10,
    "Policy_Sequence_Number": "059200/05",
    "Period_start_date": "31.03.2020",
    "Period_end_date": "30.04.2020",
    "Underwriting_Year": "2020",
    "Transaction_Currency": "EUR",
    "Gross_Premium": 46.1,
    "IPT_Rate": "26.5%",
    "IPT_Amount": 12.22,
    "Brokerage": "",
    "Agency_Commission": 6.45,
    "Provisional_Profit_Commission": "",
    "Tax_deducted": None,
    "Net_Balance_due_to_GLUK": 51.86,
    "IPT_Territory": "NA",
    "Targa": "anonymized",
    "Data_emissione": "01.04.2020",
    "Data_Incasso": "01.04.2020",
    "Imposta_SSN": "10.5%",
    "Imposta_SSN_eur": 4.84,
    "Imposta_CVT": "16.00%",
    "Imposta_CVT_eur": 7.38,
    "Imposta_antiracket": "0.00%",
    "Imposta_antiracket_eur": "",
    "Premio_totale": 58.32,
    "Provvigione": "14.00%",
    "Provvigione_eur": 6.45,
    "Insured's address": "anonymized",
    "Comune": "Frosinone",
    "Post_code": "3100",
    "Cod_fiscale": "anonymized",
    "Descrizione_garanzia": "rca",
    "Riassicurazione_Premi": "",
    "Instalment": 5,
    "Veicolo": "car",
    "Tipologia_garanzia_acquistata": "",
    "Frequenza": "monthly",
    "Parent_code": "PRP502558236",
    "Data_emissione_sost": "30.11.2019",
    "Terrorism_Premium": "",
}

prima_policy_sample_rows = [
    prima_policy_sample_row,
    prima_policy_sample_row_02,
    prima_policy_sample_row_03,
    prima_policy_sample_row_04,
    prima_policy_sample_row_05,
]
