#!/usr/bin/python
# -*- coding: utf-8 -*-

import io
from datetime import datetime

import numpy as np  # type: ignore
import pandas as pd
import pytest

from loader.prima.dto.policy import PrimaCSVPolicyDTO
from loader.prima.factory import PrimaCSVToDataFrameFactory
from loader.prima.interface import PrimaClaimsXLSXPandasInterface
from tests.prima.prima_sample_row import prima_policy_sample_row, prima_policy_sample_row_05


def setup_samples(sample_row):
    """
    Generate samples for Prima
    """
    valid_sample = "\n".join(
        [
            ";".join([str(k) for k in sample_row.keys()]),
            ";".join([str(v) for v in sample_row.values()]),
        ]
    ).encode()
    csv_factory = PrimaCSVToDataFrameFactory(io.BytesIO(valid_sample))
    return csv_factory.get_dataframe()


@pytest.fixture(scope="session")
def df():
    return setup_samples(prima_policy_sample_row)


@pytest.fixture(scope="session")
def df05():
    return setup_samples(prima_policy_sample_row_05)


@pytest.fixture(scope="session")
def date_df():
    df = pd.DataFrame(
        {
            "Date1": ["2020-02-10 09:08", "2020-02-10 09:08", "2020-02-10 09:08"],
            "Date2": ["10-02-2020 09:08", "10-02-2020 09:08", "10-02-2020 09:08"],
            "Date3": ["10/02/2020", "11/02/2020", "12/02/2020"],
            "Date4": ["10/02/2020", "2020-02-10 00:00:00", "2020-02-10 00:05:00"],
            "Date5": ["10.02.2020", "2020.02.10 00:00:00", "2020.02.10 00:00:00"],
        }
    )

    output = io.BytesIO()

    # Use the BytesIO object as the filehandle.
    writer = pd.ExcelWriter(output, engine="xlsxwriter")

    # Write the data frame to the BytesIO object.
    df.to_excel(writer, sheet_name="Sheet1", index=False)
    writer.close()

    # Read the BytesIO object back to a data frame - here you should use your method

    return pd.read_excel(
        output,
        parse_dates=["Date1", "Date2", "Date3", "Date4", "Date5"],
        date_parser=PrimaClaimsXLSXPandasInterface.date_parser(),
    )


def test_row_count(df):
    row_cnt, col_cnt = df.shape
    assert row_cnt == 1


def test_column_count(df):
    column_count = len(df.columns)
    assert column_count == 46


def test_column_names_and_dto(df):
    dto_fields = set([v.metadata.get("source") for k, v in PrimaCSVPolicyDTO.__dataclass_fields__.items()])
    df_fields = set(k for k in df.columns)
    assert dto_fields == df_fields


@pytest.mark.parametrize(
    "datetime_fields",
    [
        "Period_start_date",
        "Period_end_date",
        "Data_emissione",
        "Data_Incasso",
    ],
)
def test_datetime_types(df, datetime_fields):
    assert df[datetime_fields].dtype == "datetime64[ns]"


@pytest.mark.parametrize(
    "sample_column",
    [
        "Agent_Policy_Number",
        "Insured_Name",
        "Direct/Reinsurance",
        "Nature_of_contract",
        "Location_of_underwriting",
        "Location_of_Risk",
        "Policy_Sequence_Number",
        "Transaction_Currency",
        "IPT_Territory",
        "Insured's address",
        "Targa",
        "Comune",
        "Post_code",
        "Cod_fiscale",
        "Descrizione_garanzia",
        "Veicolo",
        "Frequenza",
    ],
)
def test_string_types(df, sample_column):
    assert isinstance(df[sample_column][0], str)


@pytest.mark.parametrize(
    "string_column, string_value",
    [
        ("Agent_Policy_Number", "PRP502558236"),
        ("Insured_Name", "anonymized"),
        ("Direct/Reinsurance", "D"),
        ("Nature_of_contract", "Emissione - Mensile"),
        ("Location_of_underwriting", "IT"),
        ("Location_of_Risk", "IT"),
        ("Policy_Sequence_Number", "059200/01"),
        ("Transaction_Currency", "EUR"),
        ("IPT_Territory", "FR"),
        ("Insured's address", "anonymized"),
        ("Targa", "anonymized"),
        ("Comune", "Frosinone"),
        ("Post_code", "3100"),
        ("Cod_fiscale", "anonymized"),
        ("Descrizione_garanzia", "rca"),
        ("Veicolo", "car"),
        ("Frequenza", "monthly"),
    ],
)
def test_string_values(df, string_column, string_value):
    assert df[string_column][0] == string_value


@pytest.mark.parametrize(
    "num_column",
    [
        "Gross_Premium",
        "IPT_Amount",
        "Agency_Commission",
        "Net_Balance_due_to_GLUK",
        "Imposta_SSN_eur",
        "Imposta_CVT_eur",
        "Premio_totale",
        "Provvigione_eur",
    ],
)
def test_numerical_types(df, num_column):
    assert df[num_column].dtype == "float32"


@pytest.mark.parametrize(
    "num_column, num_value",
    [
        ("Class_of_business", 10),
        ("Gross_Premium", np.float32(80.36)),
        ("IPT_Amount", np.float32(21.3)),
        ("Agency_Commission", np.float32(11.25)),
        ("Net_Balance_due_to_GLUK", np.float32(90.41)),
        ("Imposta_SSN_eur", np.float32(8.44)),
        ("Imposta_CVT_eur", np.float32(12.86)),
        ("Premio_totale", np.float32(101.66)),
        ("Provvigione_eur", np.float32(11.25)),
        ("Instalment", 5),
    ],
)
def test_numerical_values(df, num_column, num_value):
    assert df[num_column][0] == num_value


@pytest.mark.parametrize(
    "df_result",
    [
        pd.DataFrame(
            {
                "Date1": [
                    datetime(year=2020, month=2, day=10, hour=9, minute=8),
                    datetime(year=2020, month=2, day=10, hour=9, minute=8),
                    datetime(year=2020, month=2, day=10, hour=9, minute=8),
                ],
                "Date2": [
                    datetime(year=2020, month=10, day=2, hour=9, minute=8),
                    datetime(year=2020, month=10, day=2, hour=9, minute=8),
                    datetime(year=2020, month=10, day=2, hour=9, minute=8),
                ],
                "Date3": [
                    datetime(year=2020, month=2, day=10),
                    datetime(year=2020, month=2, day=11),
                    datetime(year=2020, month=2, day=12),
                ],
                "Date4": [
                    datetime(year=2020, month=2, day=10),
                    datetime(year=2020, month=2, day=10),
                    datetime(year=2020, month=2, day=10, minute=5),
                ],
                "Date5": [
                    datetime(year=2020, month=2, day=10),
                    datetime(year=2020, month=2, day=10),
                    datetime(year=2020, month=2, day=10),
                ],
            }
        )
    ],
)
def test_parser_in_claims(date_df, df_result):
    # Date2 will be handled by a default parser but in a wrong way..

    # Assert that the data frame is the same as the original
    pd.testing.assert_frame_equal(date_df, df_result)


def test_na_values(df05):
    assert df05["IPT_Territory"][0] == "NA"
