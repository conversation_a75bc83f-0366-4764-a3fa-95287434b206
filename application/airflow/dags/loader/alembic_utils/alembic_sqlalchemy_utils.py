from typing import Optional, SupportsInt, Union

from sqlalchemy import create_engine, schema  # type: ignore
from sqlalchemy.orm import sessionmaker  # type: ignore

import loader.alembic_utils.alembic_config as config
from loader.alembic_utils.alembic_config import SqlAlchemyEngineType


def create_schema_if_not_exist(engine: SqlAlchemyEngineType, db_name: str, db_landing_schema: str) -> None:
    """
    Creates a schema in a database if it doesn't already exists

    :param engine: The SQL Alchemy engine to use to connect to the database
    :type engine: SqlAlchemyEngineType
    :param db_name: The database to use for the connection
    :type db_name: str
    :param db_landing_schema: The name of the schema to check existance of
    :type db_landing_schema: str

    :return: None
    """

    config.logger.debug("checking if schema %s exists", db_landing_schema)
    if not engine.dialect.has_schema(engine, db_landing_schema):
        config.logger.warning("schema %s not found in db %s, creating", db_landing_schema, db_name)
        engine.execute(schema.CreateSchema(db_landing_schema))
    else:
        config.logger.debug("schema %s exists", db_landing_schema)


def create_sql_alchemy_url(
    db_user: Optional[str] = config.DB_USER,  # type: ignore
    db_host: Optional[str] = config.DB_HOST,  # type: ignore
    db_port: Optional[Union[str, SupportsInt]] = config.DB_PORT,  # type: ignore
    db_name: Optional[str] = config.DB_NAME,  # type: ignore
    db_password: Optional[str] = config.DB_PASSWORD,  # type: ignore #type: ignore
) -> str:
    return "postgresql://{}:{}@{}:{}/{}".format(db_user, db_password, db_host, db_port, db_name)
