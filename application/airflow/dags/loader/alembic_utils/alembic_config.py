import boto3
import os
import logging

from typing import NewType
from sqlalchemy.engine.base import Engine  # type: ignore
from sqlalchemy.ext.declarative import declarative_base

numeric_level = getattr(logging, os.environ.get("LOGLEVEL", "INFO").upper(), None)
logging.basicConfig(level=numeric_level)

logger = logging.getLogger()

SqlAlchemyEngineType = NewType("SqlAlchemyEngineType", Engine)

Base = declarative_base()

DB_USER = os.getenv("DB_USER", "root")
DB_NAME = os.getenv("DB_NAME", "mgadb")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = str(os.getenv("DB_PORT", 5432))
DB_PASS = os.getenv("DB_PASS")
DB_METADATA_SCHEMA = os.getenv("DB_METADATA_SCHEMA", "mga_metadata")
DEFAULT_DB_LANDING_SCHEMA = os.getenv("DEFAULT_DB_LANDING_SCHEMA", "mgalanding")

if DB_PASS:
    logger.info("Fetching db Password...")
    ssm_client = boto3.client("ssm")
    DB_PASSWORD = ssm_client.get_parameter(Name=DB_PASS, WithDecryption=True)["Parameter"]["Value"]
else:
    DB_PASSWORD = '1234567'
