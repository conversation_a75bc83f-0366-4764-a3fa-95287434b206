# -*- coding: utf-8 -*-
"""Common utility function library for Airflow DAGs"""

import logging
import os
import signal
from functools import lru_cache
from typing import Dict

from airflow.models import DagRun, Variable
from airflow.operators.python import get_current_context
from airflow.utils.types import DagRunType
from quantum_data_pipeline.common.dates import get_previous_date, to_date

from quantum_data_pipeline.airflow.connection_builder import AirflowConnectionAdapter

DATALAKE_ENVIRONMENT = os.environ.get("DATALAKE_ENVIRONMENT")
DAGS_ROOT = "/opt/airflow/dags"

logger = logging.getLogger(__file__)


def is_prod() -> bool:
    return DATALAKE_ENVIRONMENT == "prod"


def get_ingestion_date() -> str:
    context = get_current_context()
    dag_run: DagRun = context.get("dag_run")
    execution_date = get_execution_date()
    if not dag_run:
        # needed for tasks testing purpose
        return execution_date
    return (
        to_date(execution_date).isoformat()
        if dag_run.run_type == DagRunType.SCHEDULED
        else (get_previous_date(execution_date)).isoformat()
    )


def get_execution_date(context=None) -> str:
    if not context:
        context = get_current_context()
    return context.get("ds")


def get_execution_timestamp(context=None) -> str:
    if not context:
        context = get_current_context()
    return context.get("ts")


def get_schedule(schedules) -> str:
    """
    Returns the schedule for a specific environment from a dict

    :param schedules: a dict containing schedules per environment
    :type bash_command: dict
    """
    if DATALAKE_ENVIRONMENT == "local":
        return "@once"
    else:
        return schedules.get(DATALAKE_ENVIRONMENT, "@once")


class GracefulKiller:
    kill_now = False

    def __init__(self):
        signal.signal(signal.SIGTERM, self.exit_gracefully)

    def exit_gracefully(self, *args):
        logger.info("Received an interruption signal. Stopping now...")
        self.kill_now = True


@lru_cache
def get_dbt_env_var() -> dict:
    """
    Returns the dbt environment variables
    """
    dbt_config = Variable.get("mga_dbt_config", deserialize_json=True)
    db_config: Dict = AirflowConnectionAdapter.to_pipeline_conf("mga_dbt")
    gitlab_token: Dict = AirflowConnectionAdapter.to_pipeline_conf("gitlab_token")
    return {
        "DB_HOST": db_config.get("host", ""),
        "DB_NAME": db_config.get("db", ""),
        "DB_PORT": f'{db_config.get("port")}',
        "DB_USER": db_config.get("user", ""),
        "DB_PASSWORD": db_config.get("password", ""),
        "TARGET_SCHEMA": dbt_config.get("target_schema", ""),
        "LANDING_SCHEMA": dbt_config.get("landing_schema", ""),
        "HISTO_SCHEMA": dbt_config.get("histo_schema", ""),
        "GIT_COMMIT": "placeholder",
        "GIT_BRANCH": "placeholder",
        "GITLAB_TOKEN": gitlab_token.get("api_key"),
    }


def get_dbt_project_dir() -> str:
    """
    Returns the dbt project directory
    """
    dbt_config = Variable.get("mga_dbt_config", deserialize_json=True)

    if DATALAKE_ENVIRONMENT == "local":
        if os.environ.get("MGA_PROJECT_PATH") is not None:
            return os.path.join(f'{os.environ.get("MGA_PROJECT_PATH")}/application/', dbt_config.get("project_dir"))
        else:
            return os.path.join(DAGS_ROOT, dbt_config.get("project_dir"))
    else:
        return dbt_config.get("project_dir")
