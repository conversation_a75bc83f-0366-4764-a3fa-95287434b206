from collections import namedtuple

import pytest
from airflow.utils.types import DagRunType
from helpers import common


@pytest.mark.parametrize(
    "dag_run_type, execution_date, ingestion_date",
    [
        (DagRunType.MANUAL, "2022-01-19", "2022-01-18"),
        (DagRunType.SCHEDULED, "2022-01-19", "2022-01-19"),
        (None, "2022-01-19", "2022-01-19"),
    ],
)
def test_get_ingestion_date(mocker, dag_run_type, execution_date, ingestion_date):
    DagRun = namedtuple("DagRun", "run_type")
    dag_run = DagRun(dag_run_type) if dag_run_type else None
    mocker.patch("helpers.common.get_current_context", return_value={"dag_run": dag_run, "ds": execution_date})
    assert common.get_ingestion_date() == ingestion_date
