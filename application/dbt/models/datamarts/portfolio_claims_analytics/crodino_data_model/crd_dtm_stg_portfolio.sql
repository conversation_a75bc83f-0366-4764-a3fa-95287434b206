{{
    config(
        materialized="table",
        tags=['dtm_pca_crodino', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH
policies AS (
  SELECT
        crodino_policy_id                       AS policy_id,
        policy_id                               AS policy_id_bis,
        TRIM(po.crodino_policy_id) || '_'
                                                AS policy_for_gen,
        man_id,
        distribution_partner,
        CASE
            WHEN product_type = 'Casa' AND form_type = 'family_only'
                THEN 'PFAMILY'
            WHEN product_id = 'PFAMILY'
                THEN 'PONECLICK'
            ELSE product_id
        END                                     AS product_id,
        branch,
        source                                  AS sales_channel,
        policy_status,
        payment_frequency,
        DATE(policy_start_date)                 AS policy_start_date,
        MIN(DATE(policy_start_date))
            OVER (PARTITION BY man_id)          AS original_policy_start_date,
        DATE(policy_start_date)                 AS policy_underwriting_date,
        DATE(policy_issue_date)                 AS policy_issue_date,
        DATE(policy_end_date_after_suspension)  AS policy_end_date,
        DATE(first_created_on)                  AS transaction_date,
        cancellation_reason,
        total_premium_yearly                    AS total_premium,
        main_policy_premium_yearly_net          AS notax_premium,
        ipt_yearly                              AS tax_premium,
        premium_paid                            AS total_premium_paid,
        CASE
            WHEN policy_annual_date_rule like '%_SUBSIN%'
                THEN 'SUBSTITUTION_IN'
            ELSE policy_classification
        END                                     AS renewal_flag,
        CASE
            WHEN policy_status = 'ACTIVE'
                AND policy_start_date <= DATE(DATE_TRUNC('month', current_date)) - 1
                AND policy_end_date_after_suspension > DATE(DATE_TRUNC('month', current_date)) - 1 THEN TRUE
            ELSE FALSE
        END                                     AS inforce_indicator,
        transaction_iscurrent
    FROM {{ ref('fct_crodino_policies') }} po
    WHERE
        policy_start_date <= DATE(DATE_TRUNC('month', current_date)) - 1
        AND product_id IN ('PFAMILY', 'PHOUSE', 'PMOTOR')
),
covers AS (
    SELECT
        co.crodino_policy_id                    AS policy_id,
        co.man_id,
        COALESCE(
            ccm.cover_to, 'OTHER'
        )                                       AS cover,
        TRIM(ccm.cover_to)                      AS trimmed_cover,
        co.cover_description,
        COALESCE(
            ccm.category,
            CONCAT(SUBSTRING(co.product_id,2,1), co.cover_description)
        )                                       AS cover_category,
        COALESCE(
            cam.add_to,
            co.cover_description
        )                                       AS cover_isaddon,
        co.solvency_class,
        SUM(co.cover_premium_yearly_net)        AS cover_premium_notax,
        SUM(co.cover_ipt_yearly)                AS cover_ipt,
        SUM(co.cover_premium_paid)              AS cover_premium_paid,
        co.insured_value,
        co.deductible_value
    FROM {{ ref('fct_crodino_covers') }} co
    LEFT JOIN {{ ref('crodino_cover_mapping') }} ccm
        ON CONCAT(SUBSTRING(co.product_id,2,1), co.cover_description) = ccm.cover_from
    LEFT JOIN {{ ref('crodino_addon_mapping') }} cam
        ON co.cover_description = cam.add_from
    GROUP BY
        co.crodino_policy_id,
        co.man_id,
        COALESCE(ccm.cover_to, 'OTHER'),
        trimmed_cover,
        co.cover_description,
        cover_category,
        cover_isaddon,
        co.solvency_class,
        co.insured_value,
        co.deductible_value
),
vehicles AS (
    SELECT
        dcv.vehicle_id,
        dcv.plate_number,
        dcv.vehicle_category AS vehicle,
        acpv.policy_id       AS policy_id_bis
    FROM {{ ref('dim_crodino_vehicles') }} dcv
    INNER JOIN {{ ref('assoc_crodino_policies_vehicles') }} acpv
        ON dcv.vehicle_id = acpv.vehicle_id
),

buildings AS (
    SELECT
        acpb.policy_id,
        dcb.living_area,
        dcb.construction_year,
        dcb.building_use       AS usage_of_building,
        dcb.apartment_type     AS condominium_type,
        dcb.house_type,
        dcb.apartment_floor    AS floor
    FROM {{ ref('dim_crodino_buildings') }} dcb
    INNER JOIN {{ ref('assoc_crodino_policies_buildings') }} acpb
        ON dcb.building_id = acpb.building_id
)

SELECT
    {{ dbt_utils.generate_surrogate_key(['po.policy_for_gen || co.trimmed_cover']) }}     AS key_ptf,
    po.policy_for_gen || co.trimmed_cover                                                 AS key_ptf_orig,
    po.distribution_partner,
    po.branch,
    po.sales_channel,
    po.policy_id,
    po.product_id,
    po.policy_status,
    po.policy_start_date,
    po.original_policy_start_date,
    po.policy_underwriting_date,
    po.policy_issue_date,
    po.policy_end_date,
    po.transaction_date,
    po.cancellation_reason,
    po.payment_frequency,
    po.total_premium,
    po.notax_premium,
    po.tax_premium,
    po.total_premium_paid,
    po.renewal_flag,
    po.inforce_indicator,
    co.cover,
    co.cover_description,
    co.cover_category,
    co.cover_isaddon,
    co.solvency_class,
    co.cover_premium_notax,
    co.cover_ipt,
    co.cover_premium_paid,
    co.insured_value,
    co.deductible_value,
    cu.type,
    cu.codice_fiscale                                                                   AS fiscal_code,
    cu.insured_name,
    cu.date_of_birth,
    cu.age,
    v.vehicle,
    v.plate_number,
    a.country,
    a.region,
    a.ipt_territory,
    a.city,
    a.post_code,
    b.living_area,
    b.construction_year,
    b.usage_of_building,
    b.condominium_type,
    b.house_type,
    b.floor,
    CASE
        WHEN fc.fiscal_code IS NOT NULL
        THEN 1
        ELSE 0
    END                                                                         AS flag_branch_move,
    po.transaction_iscurrent
FROM policies po
INNER JOIN covers co
    ON po.policy_id = co.policy_id
INNER JOIN {{ ref('dim_crodino_people') }} cu
    ON po.man_id = cu.man_id
INNER JOIN {{ ref('dim_crodino_policies_address') }} a
    ON po.policy_id_bis = a.policy_id
LEFT JOIN vehicles v
    ON po.policy_id_bis = v.policy_id_bis
LEFT JOIN buildings b
    ON po.policy_id_bis = b.policy_id
LEFT JOIN {{ ref('crd_dtm_stg_assoc_fcmove') }} fc
    ON cu.codice_fiscale = fc.fiscal_code
    AND v.plate_number = fc.plate_number
