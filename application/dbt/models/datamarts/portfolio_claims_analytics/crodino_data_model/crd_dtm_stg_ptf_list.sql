{{
    config(
        materialized="table",
        tags=['dtm_pca_crodino', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH exp_base AS(
    SELECT DISTINCT
        key_ptf,
        key_ptf_orig,
        policy_id,
        cover
    FROM {{ ref('crd_dtm_stg_exp_list') }}
),

clm_base AS(
    SELECT DISTINCT
        policy_id,
        cover,
        branch,
        product_id,
        underwriting_year
    FROM {{ ref('crd_dtm_claim') }}
)

SELECT DISTINCT
    exp.key_ptf                   AS key_ptf,
    exp.key_ptf_orig              AS key_ptf_orig,
    'CRODINO'                     AS distribution_partner,
    clm.branch                    AS branch,
    'Dummy from Claims'           AS sales_channel,
    exp.policy_id                 AS policy_id,
    clm.product_id                AS product_id,
    NULL::text                    AS policy_status,
    NULL::date                    AS policy_start_date,
    NULL::date                    AS original_policy_start_date,
    CASE
      WHEN clm.underwriting_year ISNULL
        THEN NULL
        ELSE format('%s-%s-%s', clm.underwriting_year, 1, 1)::date
    END                           AS policy_underwriting_date,
    NULL::date                    AS policy_issue_date,
    NULL::date                    AS policy_end_date,
    NULL::date                    AS transaction_date,
    NULL::varchar                 AS cancellation_reason,
    'Dummy from Claims'           AS payment_frequency,
    NULL::float                   AS total_premium,
    NULL::float                   AS notax_premium,
    NULL::float                   AS tax_premium,
    NULL::float                   AS total_premium_paid,
    NULL::text                    AS renewal_flag,
    NULL::boolean                 AS inforce_indicator,
    exp.cover                     AS cover,
    NULL::varchar                 AS cover_description,
    'Dummy from Claims'           AS cover_category,
    'Dummy from Claims'           AS cover_isaddon,
    'Dummy from Claims'           AS solvency_class,
    NULL::float                   AS cover_premium_notax,
    NULL::float                   AS cover_ipt,
    NULL::float                   AS cover_premium_paid,
    NULL::int4                    AS insured_value,
    NULL::numeric                 AS deductible_value,
    'Dummy from Claims'           AS type,
    NULL::varchar                 AS fiscal_code,
    NULL::varchar                 AS insured_name,
    NULL::date                    AS date_of_birth,
    NULL::float                   AS age,
    'Dummy from Claims'           AS vehicle,
    NULL::varchar                 AS plate_number,
    'Dummy from Claims'           AS country,
    'Dummy from Claims'           AS region,
    'Dummy from Claims'           AS ipt_territory,
    'Dummy from Claims'           AS city,
    NULL::varchar                 AS post_code,
    NULL::int4                    AS living_area,
    'Dummy from Claims'           AS construction_year,
    'Dummy from Claims'           AS usage_of_building,
    'Dummy from Claims'           AS condominium_type,
    'Dummy from Claims'           AS house_type,
    'Dummy from Claims'           AS floor,
    NULL::int4                    AS flag_branch_move,
    NULL::boolean                 AS transaction_iscurrent
FROM exp_base exp
LEFT JOIN {{ ref('crd_dtm_stg_portfolio') }}  ptf
    ON exp.key_ptf = ptf.key_ptf
LEFT JOIN clm_base clm
    ON exp.policy_id = clm.policy_id
    AND exp.cover=clm.cover
WHERE
    ptf.key_ptf ISNULL
