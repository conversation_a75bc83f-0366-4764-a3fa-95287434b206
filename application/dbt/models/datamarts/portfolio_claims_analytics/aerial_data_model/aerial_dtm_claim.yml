version: 2

models:
  - name: aerial_dtm_claim
    description: The claim table contains the claims history from opening to the last closing period, registered per development month.
    columns:
      - name: "concat(claim_id, opened_claim_type, claim_view)"
        data_tests:
          - unique:
              config:
                severity: warn
                store_failures: true
          - not_null:
              config:
                severity: warn
                store_failures: true
      - name: claim_id
        data_tests:
          - not_null:
              config:
                severity: warn
                store_failures: true
      - name: occurrence_date
        data_tests:
          - not_null:
              config:
                severity: warn
                store_failures: true
      - name: cover
        data_tests:
          - not_null:
              config:
                severity: warn
                store_failures: true
      - name: "concat(trim(policy_id), trim(cover))"
        data_tests:
          - relationships:
              to: ref('aerial_dtm_exposure')
              field: "concat(trim(policy_id), trim(cover))"
              config:
                severity: warn
                store_failures: true
