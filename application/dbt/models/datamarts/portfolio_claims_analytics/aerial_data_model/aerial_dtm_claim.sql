{{
    config(
        materialized="table",
        tags=['dtm_pca_aerial', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH claim_base AS(
    SELECT
        exp.key_exposure AS key_exposure,
        cla.*
    FROM {{ ref('aerial_dtm_stg_claim') }} cla
    LEFT JOIN {{ ref('aerial_dtm_exposure') }} exp
        ON TRIM(cla.policy_id) = TRIM(exp.policy_id)
        AND TRIM(cla.cover) = TRIM(exp.cover)
        AND cla.occurrence_date >= exp.reserve_start_period
        AND cla.occurrence_date <= exp.reserve_end_period
),

claim_group AS(
    SELECT
        claim_id,
        cover,
        claim_view,
        MIN(key_exposure) AS key_exposure_min
    FROM claim_base
    GROUP BY
        claim_id,
        cover,
        claim_view
)

SELECT
    cla.*
FROM claim_base cla
JOIN claim_group grp
    ON cla.claim_id = grp.claim_id
    AND cla.cover = grp.cover
    AND cla.claim_view = grp.claim_view
    AND cla.key_exposure = grp.key_exposure_min
