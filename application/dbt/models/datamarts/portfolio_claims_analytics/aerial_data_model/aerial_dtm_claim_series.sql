{{
    config(
        materialized="table",
        tags=['dtm_pca_aerial', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH
claims_base AS (
    SELECT
        DATE(DATE_TRUNC('month', claim_view))         AS claim_series_view,
        TRIM(policy_id) || '_' || TRIM(cover) || '_'
          || DATE(DATE_TRUNC('month', claim_view))    AS key_series_orig,
        *
    FROM {{ ref('aerial_dtm_stg_claim') }}
)

SELECT
    {{ dbt_utils.generate_surrogate_key(['key_series_orig']) }} AS key_series,
    *
FROM claims_base
