{{
    config(
        materialized="table",
        tags=['dtm_pca_aerial', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH
policies AS (
  SELECT
        aerial_policy_id                                AS policy_id,
        policy_id                                       AS policy_id_bis,
        TRIM(aerial_policy_id) || '_'                   AS policy_for_gen,
        man_id,
        distribution_partner,
        product_id,
        branch,
        policy_status,
        payment_frequency,
        DATE(policy_start_date)                         AS policy_start_date,
        DATE(policy_start_date)                         AS policy_underwriting_date,
        DATE(policy_end_date)                           AS policy_end_date,
        gross_written_premium                           AS total_premium,
        gross_written_premium-insurance_premium_tax     AS notax_premium,
        insurance_premium_tax                           AS tax_premium,
        gross_written_premium                           AS total_premium_paid,
        CASE
            WHEN policy_status = 'ACTIVE'
                AND policy_start_date <= DATE(DATE_TRUNC('month', current_date)) - 1
                AND policy_end_date > DATE(DATE_TRUNC('month', current_date)) - 1 THEN TRUE
            ELSE FALSE
        END                                             AS inforce_indicator
    FROM {{ ref('fct_aerial_policies') }}
    WHERE
        policy_start_date <= DATE(DATE_TRUNC('month', current_date)) - 1
),
covers AS (
    SELECT
        co.aerial_policy_id             AS policy_id,
        co.man_id,
        COALESCE(
            acm.cover_to, 'OTHER'
        )                               AS cover,
        TRIM(acm.cover_to)              AS trimmed_cover,
        co.cover_description,
        COALESCE(
            acm.category,
            CONCAT(SUBSTRING(co.product_id,1,1), co.cover_description)
        )                               AS cover_category,
        co.solvency_class,
        co.cover_premium-co.cover_ipt   AS cover_premium_notax,
        co.cover_ipt                    AS cover_ipt,
        co.cover_premium                AS cover_premium_paid,
        co.insured_value_pd,
        co.insured_value_bi
    FROM {{ ref('fct_aerial_covers') }} co
    LEFT JOIN {{ ref('aerial_cover_mapping') }} acm
        ON CONCAT(SUBSTRING(co.product_id,1,1), co.cover_description) = acm.cover_from
    GROUP BY
        co.aerial_policy_id,
        co.man_id,
        cover,
        trimmed_cover,
        cover_description,
        cover_category,
        solvency_class,
        cover_premium_notax,
        cover_ipt,
        cover_premium_paid,
        co.insured_value_pd,
        co.insured_value_bi
),
vehicles AS (
    SELECT
        dav.vehicle_id,
        dav.vehicle_registration_number  AS plate_number,
        dav.vehicle_type                 AS vehicle,
        ap.policy_number                 AS policy_id
    FROM {{ ref('dim_aerial_vehicles') }} dav
    INNER JOIN {{ ref('aerial_portfolio') }} ap
        ON dav.vehicle_registration_number = ap.vehicle_registration_number
    GROUP BY
        dav.vehicle_id,
        dav.vehicle_registration_number,
        dav.vehicle_type,
        ap.policy_number
)

SELECT
    {{ dbt_utils.generate_surrogate_key(['po.policy_for_gen || co.trimmed_cover']) }}   AS key_ptf,
    po.policy_for_gen || co.trimmed_cover                                               AS key_ptf_orig,
    po.distribution_partner,
    po.branch,
    po.policy_id,
    po.product_id,
    po.policy_status,
    po.policy_start_date,
    po.policy_underwriting_date,
    po.policy_end_date,
    po.payment_frequency,
    po.total_premium,
    po.notax_premium,
    po.tax_premium,
    po.total_premium_paid,
    po.inforce_indicator,
    co.cover,
    co.cover_description,
    co.cover_category,
    co.solvency_class,
    co.cover_premium_notax,
    co.cover_ipt,
    co.cover_premium_paid,
    co.insured_value_pd,
    co.insured_value_bi,
    cu.tax_id                                                                           AS fiscal_code,
    cu.person_name                                                                      AS insured_name,
    cu.date_of_birth,
    v.vehicle,
    v.plate_number,
    a.country,
    a.city,
    a.post_code
FROM policies po
INNER JOIN covers co
    ON po.policy_id = co.policy_id
INNER JOIN {{ ref('dim_aerial_people') }} cu
    ON po.man_id = cu.man_id
INNER JOIN {{ ref('dim_aerial_addresses') }} a
    ON po.policy_id_bis = a.policy_id
LEFT JOIN vehicles v
    ON po.policy_id = v.policy_id
