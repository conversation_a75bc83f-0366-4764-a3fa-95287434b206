{{
    config(
        materialized="table",
        tags=['dtm_pca_aerial', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH
pol_dates_tmp AS (
    SELECT DISTINCT
        policy_number                          AS aerial_policy_id,
        policy_end_date - policy_start_date    AS policy_original_duration
    FROM {{ ref('aerial_portfolio') }}
),

pol_dates AS (
    SELECT *
    FROM pol_dates_tmp
    WHERE
      EXTRACT('epoch' FROM policy_original_duration) != 0
),

exposure_base AS (
    SELECT
        TRIM(res.policy_number) || '_' || TRIM(acm.cover_to)
                                                      AS key_ptf_orig,
        TRIM(res.policy_number)|| '_' || TRIM(acm.cover_to) || '_'
          || DATE(res.reserve_period_start) || '_' || DATE(res.reserve_period_end)
                                                      AS key_exposure_orig,
        TRIM(res.policy_number) || '_' || TRIM(acm.cover_to) || '_'
          || DATE(DATE_TRUNC('month', res.reserve_period_start))
                                                      AS key_series_orig,
        res.policy_number                             AS policy_id,
        COALESCE(
            acm.cover_to, 'OTHER'
        )                                             AS cover,
        res.cover                                     AS cover_description,
        res.reserve_period_type,
        NULL                                          as suspension_status,
        DATE(DATE_TRUNC('month', res.reserve_period_start))
                                                      AS calendar_month,
        DATE(res.reserve_period_start)                AS reserve_start_period,
        DATE(res.reserve_period_end)                  AS reserve_end_period,
        poldt.policy_original_duration,
        res.reserve_period_duration_days              AS reserve_days,
        NULL::float                                   AS reserve_years,
        poldt.policy_original_duration                AS policy_days,
        res.earned_share                              AS annual_share,
        res.earned_premium_reserve                    AS earned_premium,
        res.earned_ipt_amount                         AS earned_ipt,
        NULL::float                                   AS cumulative_earned_premium,
        NULL::float                                   AS cumulative_earned_ipt,
        '1'                                           AS source_record
    FROM {{ ref('aerial_premium_reserves') }} res
    INNER JOIN {{ ref('fct_aerial_policies') }} pol
        ON res.policy_number = pol.aerial_policy_id
    INNER JOIN pol_dates poldt
        ON res.policy_number = poldt.aerial_policy_id
    LEFT JOIN {{ ref('aerial_cover_mapping') }} acm
        ON CONCAT(SUBSTRING(pol.product_id,1,1), res.cover) = acm.cover_from
    WHERE
        DATE(res.reserve_period_end) <= DATE(DATE_TRUNC('month', current_date)) - 1
        AND res.reserve_period_end >= res.reserve_period_start
)

SELECT
    {{ dbt_utils.generate_surrogate_key(['key_ptf_orig']) }}       AS key_ptf,
    {{ dbt_utils.generate_surrogate_key(['key_exposure_orig']) }}  AS key_exposure,
    {{ dbt_utils.generate_surrogate_key(['key_series_orig']) }}    AS key_series,
    *
FROM exposure_base
