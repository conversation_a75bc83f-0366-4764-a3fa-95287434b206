{{
    config(
        materialized="table",
        tags=['dtm_pca_aerial', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH
list_pol_exp AS (
    SELECT DISTINCT
        key_series,
        policy_id,
        cover,
        calendar_month
    FROM {{ ref('aerial_dtm_stg_exposure') }}
),

list_pol_clm AS (
    SELECT DISTINCT
        TRIM(policy_id) || '_' || TRIM(cover) AS key_ptf_orig,
        TRIM(policy_id)|| '_' || TRIM(cover) || '_'
          || claim_series_view || '_' || DATE(claim_view)
                                              AS key_exposure_orig,
        key_series,
        key_series_orig,
        policy_id,
        cover,
        claim_series_view,
        claim_series_view                     AS reserve_start_period,
        DATE(claim_view)                      AS reserve_end_period
    FROM {{ ref('aerial_dtm_claim_series') }}
),

list_exp_clm AS (
    SELECT DISTINCT
        TRIM(cla.policy_id) || '_' || TRIM(cla.cover)           AS key_ptf_orig,
        TRIM(cla.policy_id)|| '_' || TRIM(cla.cover) || '_'
          || DATE(DATE_TRUNC('month', cla.occurrence_date)) || '_'
          || DATE(DATE_TRUNC('month', cla.occurrence_date) + interval '1 month - 1 day')
                                                                AS key_exposure_orig,
        TRIM(cla.policy_id) || '_' || TRIM(cla.cover) || '_'
          || DATE(DATE_TRUNC('month', cla.occurrence_date))     AS key_series_orig,
        cla.policy_id,
        cla.cover,
        DATE(DATE_TRUNC('month', cla.occurrence_date))          AS claim_series_view,
        DATE(DATE_TRUNC('month', cla.occurrence_date))          AS reserve_start_period,
        DATE(DATE_TRUNC('month', cla.occurrence_date) + interval '1 month - 1 day')
                                                                AS reserve_end_period
    FROM {{ ref('aerial_dtm_claim_series') }} cla
    LEFT JOIN {{ ref('aerial_dtm_stg_exposure') }} exp
        ON TRIM(cla.policy_id) = TRIM(exp.policy_id)
        AND TRIM(cla.cover) = TRIM(exp.cover)
        AND cla.occurrence_date >= exp.reserve_start_period
        AND cla.occurrence_date <= exp.reserve_end_period
    WHERE
        exp.policy_id ISNULL
),

list_dummy_pol_set1 AS (
    SELECT
        {{ dbt_utils.generate_surrogate_key(['key_ptf_orig']) }}
                                  AS key_ptf,
        {{ dbt_utils.generate_surrogate_key(['key_exposure_orig']) }}
                                  AS key_exposure,
        clm.key_series,
        clm.key_ptf_orig          AS key_ptf_orig,
        key_exposure_orig         AS key_exposure_orig,
        clm.key_series_orig       AS key_series_orig,
        clm.policy_id,
        clm.cover,
        NULL                      AS cover_description,
        NULL                      AS reserve_period_type,
        NULL                      AS suspension_status,
        clm.claim_series_view     AS calendar_month,
        clm.reserve_start_period  AS reserve_start_period,
        clm.reserve_end_period    AS reserve_end_period,
        NULL::interval            AS policy_original_duration,
        NULL::float               AS reserve_days,
        NULL::float               AS reserve_years,
        NULL::interval            AS policy_days,
        NULL::float               AS annual_share,
        NULL::float               AS earned_premium,
        NULL::float               AS earned_ipt,
        NULL::float               AS cumulative_earned_premium,
        NULL::float               AS cumulative_earned_ipt,
        '2'                       AS source_record
    FROM list_pol_clm clm
    LEFT JOIN list_pol_exp exp
      ON clm.key_series = exp.key_series
    WHERE
      exp.key_series ISNULL
),

list_dummy_pol_set2 AS (
    SELECT
        {{ dbt_utils.generate_surrogate_key(['lst.key_ptf_orig']) }}
                                  AS key_ptf,
        {{ dbt_utils.generate_surrogate_key(['lst.key_exposure_orig']) }}
                                  AS key_exposure,
        {{ dbt_utils.generate_surrogate_key(['lst.key_series_orig']) }}
                                  AS key_series,
        lst.key_ptf_orig          AS key_ptf_orig,
        lst.key_exposure_orig     AS key_exposure_orig,
        lst.key_series_orig       AS key_series_orig,
        lst.policy_id,
        lst.cover,
        NULL                      AS cover_description,
        NULL                      AS reserve_period_type,
        NULL                      AS suspension_status,
        lst.claim_series_view     AS calendar_month,
        lst.reserve_start_period  AS reserve_start_period,
        lst.reserve_end_period    AS reserve_end_period,
        NULL::interval            AS policy_original_duration,
        NULL::float               AS reserve_days,
        NULL::float               AS reserve_years,
        NULL::interval            AS policy_days,
        NULL::float               AS annual_share,
        NULL::float               AS earned_premium,
        NULL::float               AS earned_ipt,
        NULL::float               AS cumulative_earned_premium,
        NULL::float               AS cumulative_earned_ipt,
        '3'                       AS source_record
    FROM list_exp_clm lst
    LEFT JOIN list_dummy_pol_set1 dum
      ON lst.key_series_orig = dum.key_series_orig
    WHERE
      dum.key_series_orig ISNULL
)

SELECT
    *
FROM list_dummy_pol_set1
UNION
SELECT
    *
FROM list_dummy_pol_set2
