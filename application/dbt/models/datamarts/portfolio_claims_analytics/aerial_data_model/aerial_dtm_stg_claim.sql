{{
    config(
        materialized="table",
        tags=['dtm_pca_aerial', 'datamart', 'portfolio_claims_analytics']
    )
}}

WITH
aerial_files AS (
    SELECT DISTINCT
        chksum,
        DATE(bordereau_end_date)    AS claim_view
    FROM {{ ref('aerial_files') }}
    WHERE
        key like '%Claims%'
),

claims_base_tmp AS (
    SELECT
        *,
        COALESCE(fl.claim_view, DATE(cl.created_on))                        AS aerial_files_claim_view
    FROM {{ ref('aerial_claims') }} cl
    LEFT JOIN aerial_files fl
        ON cl.chksum = fl.chksum
),

claims_base AS (
    SELECT
        cc.branch,
        cc.claim_id,
        STRING_AGG(DISTINCT cc.claim_type, ',')                             AS claim_type,
        STRING_AGG(DISTINCT CAST(cc.class_of_business AS varchar), ',')     AS class_of_business,
        COALESCE(
            ccm.cover_to, 'OTHER'
        )                                                                   AS cover,
        cc.policy_id,
        'AMOTORES'                                                          AS product_id,
        cc.underwriting_year,
        cc.occurrence_date,
        cc.opening_date,
        cc.date_notified_to_aerial                                          AS notification_date,
        cc.closing_date,
        cc.claim_status_at_end_of_the_month,
        cc.aerial_files_claim_view                                          AS claim_view,
        (DATE_PART('year', DATE(DATE_TRUNC('month', cc.created_on)) - 1) - DATE_PART('year', cc.occurrence_date)) * 12 +
        (DATE_PART('month', DATE(DATE_TRUNC('month', cc.created_on)) - 1)  - DATE_PART('month', cc.occurrence_date))
                                                                            AS development_age,
        SUM(COALESCE(cc.paid_pd, 0))                                        AS paid_pd,
        SUM(COALESCE(cc.paid_pd_prev, 0))                                   AS paid_pd_prev,
        SUM(COALESCE(cc.paid_bi, 0))                                        AS paid_bi,
        SUM(COALESCE(cc.paid_bi_prev, 0))                                   AS paid_bi_prev,
        SUM(COALESCE(cc.reserved_pd, 0))                                    AS reserved_pd,
        SUM(COALESCE(cc.reserved_bi, 0))                                    AS reserved_bi,
        SUM(COALESCE(cc.recovered, 0))                                      AS recovered,
        SUM(COALESCE(cc.recovery_reserve, 0))                               AS recovery_reserve,
        SUM(COALESCE(cc.claim_assessment_reserves, 0))                      AS claim_assessment_reserves,
        SUM(COALESCE(cc.claim_assessment_paid, 0))                          AS claim_assessment_paid,
        SUM(COALESCE(cc.claim_assessment_paid_prev, 0))                     AS claim_assessment_paid_prev,
        SUM(COALESCE(cc.lae_reserves, 0))                                   AS lae_reserves,
        SUM(COALESCE(cc.lae_paid, 0))                                       AS lae_paid,
        SUM(COALESCE(cc.total_incurred, 0))                                 AS total_incurred,
        SUM(
            CASE
                WHEN EXTRACT(YEAR FROM cc.occurrence_date) <= 2021
                    AND cc.total_incurred > 1100000 THEN cc.total_incurred - 1100000
                WHEN EXTRACT(YEAR FROM cc.occurrence_date) = 2022
                    AND cc.total_incurred > 1250000 THEN cc.total_incurred - 1250000
                ELSE 0
            END)                                                            AS riass_incurred,
        cc.insured_province                                                 AS prov_occurrence,
        cc.transaction_iscurrent
    FROM claims_base_tmp cc
    LEFT JOIN {{ ref('aerial_claim_cover_mapping') }} ccm
        ON ccm.cover_from = CONCAT(SUBSTRING(cc.distribution_partner,1,1), UPPER(REPLACE(REPLACE(REPLACE(TRIM(cc.class_of_business), ' ', ''), '-', '_'), '''', '')))
    WHERE
        cc.opening_date <= DATE(DATE_TRUNC('month', current_date)) - 1
    GROUP BY
        branch,
        claim_id,
        cover,
        policy_id,
        product_id,
        underwriting_year,
        occurrence_date,
        opening_date,
        date_notified_to_aerial,
        closing_date,
        claim_status_at_end_of_the_month,
        closing_date,
        aerial_files_claim_view,
        development_age,
        insured_province,
        transaction_iscurrent
),

claims_tmp AS (
    SELECT
        c.*,
        paid_bi + paid_pd + claim_assessment_paid + lae_paid - recovered                          AS total_paid,
        SUM(paid_bi + paid_pd + claim_assessment_paid + lae_paid - recovered)
          OVER w_claim_id_view                                                                    AS total_paid_cum,
        reserved_bi + reserved_pd + lae_reserves + claim_assessment_reserves - recovery_reserve   AS total_reserved,
        SUM(recovered) OVER w_claim_id_view                                                       AS total_recovered_cum
    FROM claims_base c
    WINDOW w_claim_id_view AS (PARTITION BY claim_id ORDER BY claim_view ASC)
)

SELECT
    *,
    CASE
        WHEN (total_paid_cum + total_reserved) <= 50000 THEN 'ATTRITIONAL'
        ELSE 'LARGE'
    END                                                                              AS boolean_attri_large,
    CASE
        WHEN (total_paid_cum + total_reserved <> 0)
            AND cover = 'MTPL'
            AND (total_recovered_cum + recovery_reserve > 0) THEN 'MTPL SUFFERED'
        ELSE 'MTPL CAUSED'
    END                                                                              AS boolean_caused_suff,
    CASE
        WHEN (total_paid_cum + total_reserved) <> 0 THEN 1
        ELSE 0
    END                                                                              AS flag_claim_paid_count,
    CASE
        WHEN (total_paid_cum + total_reserved = 0) THEN 1
        ELSE 0
    END                                                                              AS flag_claim_not_paid_count,
    CASE
        WHEN (total_paid_cum + total_reserved <> 0)
            AND (total_recovered_cum + recovery_reserve <= 0) THEN 1
        ELSE 0
    END                                                                              AS flag_claim_caused,
    CASE
        WHEN (total_paid_cum + total_reserved <> 0)
            AND cover = 'MTPL'
            AND (total_recovered_cum + recovery_reserve > 0) THEN 1
        ELSE 0
    END                                                                              AS flag_claim_suffered,
    (total_paid_cum + total_reserved)                                                AS total_incurred_cum
FROM claims_tmp
