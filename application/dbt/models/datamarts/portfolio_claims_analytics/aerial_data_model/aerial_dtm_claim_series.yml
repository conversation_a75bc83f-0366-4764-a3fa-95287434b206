version: 2

models:
  - name: aerial_dtm_claim_series
    description: The claim table contains the claims history from opening to the last closing period, registered per development month and arranged for time series analysis.
    columns:
      - name: "concat(claim_id, opened_claim_type, claim_series_view)"
        data_tests:
          - unique:
              config:
                severity: warn
                store_failures: true
          - not_null:
              config:
                severity: warn
                store_failures: true
      - name: claim_id
        data_tests:
          - not_null:
              config:
                severity: warn
                store_failures: true
      - name: cover
        data_tests:
          - not_null:
              config:
                severity: warn
                store_failures: true
