{{
    config(
        materialized="ephemeral"
    )
}}
SELECT
    distribution_partner,
    product_id,
    crodino_policy_id,
    cover,
    suspension_status,
    reserve_period_type,
    reserve_start_period,
    reserve_end_period,
    reserve_days,
    cumulative_days,
    SUM(applicable_annual_share)           AS applicable_annual_share,
    SUM(earned_premium_reserve)            AS earned_premium_reserve,
    SUM(earned_ipt)                        AS earned_ipt,
    SUM(cumulative_earned_premium_reserve) AS cumulative_earned_premium_reserve,
    SUM(cumulative_earned_ipt)             AS cumulative_earned_ipt
FROM {{ ref('crodino_premium_reserves') }}
GROUP BY
    distribution_partner,
    product_id,
    crodino_policy_id,
    cover,
    suspension_status,
    reserve_period_type,
    reserve_start_period,
    reserve_end_period,
    reserve_days,
    cumulative_days
