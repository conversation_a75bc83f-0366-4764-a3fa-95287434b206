{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

WITH
reactivated_policies AS (
    SELECT
        distribution_partner,
        policy_id,
        crodino_policy_id,
        policy_start_date,
        suspension_status,
        suspension_effective_date,
        reactivation_effective_date,
        policy_end_date_after_suspension
    FROM {{ ref('fct_crodino_policies') }}
    WHERE
        suspension_status = 'REACTIVATED'
)

SELECT
    distribution_partner,
    policy_id,
    crodino_policy_id,
    policy_start_date                   AS period_start_date,
    suspension_effective_date           AS period_end_date,
    'PRE_SUSPENSION_PERIOD'             AS reserve_period_type,
    policy_end_date_after_suspension,
    suspension_status
FROM reactivated_policies
UNION ALL
SELECT
    distribution_partner,
    policy_id,
    crodino_policy_id,
    suspension_effective_date           AS period_start_date,
    reactivation_effective_date         AS period_end_date,
    CASE
        WHEN suspension_effective_date < (reactivation_effective_date - INTERVAL '30 days') THEN 'SUSPENSION_PERIOD'
        ELSE 'NI_SUSPENSION_PERIOD' -- Not impacting suspension period
    END                                 AS reserve_period_type,
    policy_end_date_after_suspension,
    suspension_status
FROM reactivated_policies
UNION ALL
SELECT
    distribution_partner,
    policy_id,
    crodino_policy_id,
    reactivation_effective_date         AS period_start_date,
    policy_end_date_after_suspension    AS period_end_date,
    'REACTIVATED_PERIOD'                AS reserve_period_type,
    policy_end_date_after_suspension,
    suspension_status
FROM reactivated_policies
