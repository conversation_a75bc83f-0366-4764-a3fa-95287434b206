{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

WITH
suspended_policies AS (
    SELECT
        distribution_partner,
        policy_id,
        crodino_policy_id,
        policy_start_date,
        suspension_status,
        suspension_effective_date,
        suspension_expiration_date,
        policy_end_date_after_suspension
    FROM {{ ref('fct_crodino_policies') }}
    WHERE
        suspension_status = 'SUSPENDED'
)

SELECT
    distribution_partner,
    policy_id,
    crodino_policy_id,
    policy_start_date           AS period_start_date,
    suspension_effective_date   AS period_end_date,
    'PRE_SUSPENSION_PERIOD'     AS reserve_period_type,
    policy_end_date_after_suspension,
    suspension_status
FROM suspended_policies
UNION ALL
SELECT
    distribution_partner,
    policy_id,
    crodino_policy_id,
    suspension_effective_date   AS period_start_date,
    suspension_expiration_date  AS period_end_date,
    'SUSPENSION_PERIOD'         AS reserve_period_type,
    policy_end_date_after_suspension,
    suspension_status
FROM suspended_policies
