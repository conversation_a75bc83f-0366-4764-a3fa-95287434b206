{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

/* The base case when there is no suspension is to use the policy contact date for reserving */
SELECT
    distribution_partner,
    policy_id,
    crodino_policy_id,
    policy_start_date           AS period_start_date,
    policy_end_date             AS period_end_date,
    'FULL_POLICY_PERIOD'        AS reserve_period_type,
    policy_end_date_after_suspension,
    suspension_status
FROM {{ ref('fct_crodino_policies') }}
WHERE
    suspension_status IN ('BASE', 'OTHER')
