{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}


SELECT
    crodino_policy_id,
    cover,
    class_of_business,
    reserve_start_period,
    SUM(days)                                   AS cumulative_days,
    SUM(earned_premium_reserve)                 AS cumulative_earned_premium_reserve,
    SUM(earned_ipt)                             AS cumulative_earned_ipt
FROM {{ ref('stg_crodino_portfolio_reserving_earned_premium') }}
    GROUP BY crodino_policy_id, cover, class_of_business, reserve_start_period
    ORDER BY reserve_start_period
