{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

SELECT
    distribution_partner,
    product_id,
    policy_id,
    cover_id,
    crodino_policy_id,
    cover,
    class_of_business,
    policy_annual_date_rule,
    reserve_period_type,
    suspension_status,
    reserve_start_period,
    reserve_end_period,
    reserve_quarter,
    reserve_year,
    policy_annual_start_date_local,
    policy_annual_end_date_local,
    policy_end_date_after_suspension,
    applicable_start_date,
    applicable_end_date,
    annualized_gross_written_premium,
    annualized_ipt,
    reserve_end_period - reserve_start_period                       AS reserve_days,
    applicable_end_date - applicable_start_date                     AS days,
    policy_annual_end_date_local - policy_annual_start_date_local   AS policy_days,
    CASE
        WHEN reserve_period_type = 'SUSPENSION_PERIOD' THEN 0
        WHEN EXTRACT('epoch' FROM (policy_annual_end_date_local - policy_annual_start_date_local)) = 0 THEN NULL
        WHEN policy_annual_end_date_local != policy_annual_start_date_local
            AND ABS(EXTRACT('epoch' FROM (applicable_end_date - applicable_start_date))) > ABS(EXTRACT('epoch' FROM (policy_annual_end_date_local - policy_annual_start_date_local))) THEN 0
        WHEN policy_annual_end_date_local != policy_annual_start_date_local
            THEN EXTRACT('epoch' FROM (applicable_end_date - applicable_start_date)) / EXTRACT('epoch' FROM (policy_annual_end_date_local - policy_annual_start_date_local))
    END                                                             AS applicable_annual_share
FROM {{ ref('stg_crodino_portfolio_reserving_periods') }}
