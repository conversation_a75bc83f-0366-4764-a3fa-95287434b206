{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

SELECT
    distribution_partner,
    product_id,
    policy_id,
    cover_id,
    crodino_policy_id,
    cover,
    class_of_business,
    policy_annual_date_rule,
    reserve_period_type,
    suspension_status,
    reserve_start_period,
    reserve_end_period,
    reserve_quarter,
    reserve_year,
    policy_annual_start_date_local,
    policy_annual_end_date_local,
    policy_end_date_after_suspension,
    applicable_start_date,
    applicable_end_date,
    annualized_gross_written_premium,
    annualized_ipt,
    reserve_days,
    days,
    policy_days,
    applicable_annual_share,
    scaling,
    scaling * applicable_annual_share * annualized_gross_written_premium AS earned_premium_reserve,
    scaling * applicable_annual_share * annualized_ipt                   AS earned_ipt
FROM {{ ref('stg_crodino_portfolio_reserving_scaled_amnts') }}
