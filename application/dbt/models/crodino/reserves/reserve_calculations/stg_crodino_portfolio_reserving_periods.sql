{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

WITH
periods AS (
    SELECT
        start_period,
        (start_period + '1 month') + '-1 second'                                    AS end_period,
        EXTRACT('quarter' FROM start_period)                                        AS period_quarter,
        EXTRACT('year' FROM start_period)                                           AS period_year
    FROM GENERATE_SERIES(
            --The minimum policy start date in portfolio is 2020-07-27 15:45:00.000000
            DATE_TRUNC('MONTH', '2020-07-27 15:45:00.000000'::TIMESTAMP)
            , DATE_TRUNC('QUARTER', NOW()::DATE + interval '3 months') + '-1 second'
            , '1 month'
        ) AS start_period
),
portfolio_data AS (
    SELECT
        'crodino_policy_id'                                                         AS policy_key_type,
        'crodino_cover_id'                                                          AS cover_key_type,
        agent_policy_number                                                         AS crodino_policy_id,
        product_id,
        cover,
        class_of_business,
        policy_annual_date_rule,
        policy_annual_start_date_local,
        policy_annual_end_date_local,
        SUM(annualized_gross_written_premium)                                       AS annualized_gross_written_premium,
        SUM(annualized_ipt)                                                         AS annualized_ipt
    FROM {{ ref('crodino_portfolio') }}
    GROUP BY
        crodino_policy_id,
        product_id,
        cover,
        class_of_business,
        policy_annual_date_rule,
        policy_annual_start_date_local,
        policy_annual_end_date_local
),
period_types AS (
    SELECT
        r.distribution_partner,
        p.product_id,
        r.policy_id,
        {{ dbt_utils.generate_surrogate_key(
                ['distribution_partner', 'policy_key_type', 'cover_key_type', 'crodino_policy_id', 'cover', 'class_of_business']
            )
        }}                                                                          AS cover_id,
        p.crodino_policy_id,
        p.cover,
        p.class_of_business,
        p.policy_annual_date_rule,
        p.policy_annual_start_date_local,
        p.policy_annual_end_date_local,
        r.policy_end_date_after_suspension,
        p.annualized_gross_written_premium,
        p.annualized_ipt,
        r.reserve_period_type,
        r.suspension_status,
        r.period_start_date                                                         AS r_period_start,
        r.period_end_date                                                           AS r_period_end
    FROM portfolio_data p
    LEFT JOIN {{ ref('stg_crodino_prem_reserves_suspension_p') }} r
        USING(crodino_policy_id)
)
SELECT
    pd.distribution_partner,
    pd.product_id,
    pd.policy_id,
    pd.cover_id,
    pd.crodino_policy_id,
    pd.cover,
    pd.class_of_business,
    pd.policy_annual_date_rule,
    pd.reserve_period_type,
    pd.suspension_status,
    GREATEST(p.start_period, pd.r_period_start)                                     AS reserve_start_period,
    LEAST(p.end_period, pd.r_period_end)                                            AS reserve_end_period,
    p.period_quarter                                                                AS reserve_quarter,
    p.period_year                                                                   AS reserve_year,
    GREATEST(pd.policy_annual_start_date_local, p.start_period, pd.r_period_start)  AS applicable_start_date,
    LEAST(pd.policy_end_date_after_suspension, p.end_period, pd.r_period_end)       AS applicable_end_date,
    pd.policy_annual_start_date_local,
    pd.policy_annual_end_date_local,
    pd.policy_end_date_after_suspension,
    pd.annualized_gross_written_premium,
    pd.annualized_ipt
FROM periods p
INNER JOIN period_types pd
    ON (pd.r_period_start BETWEEN p.start_period AND p.end_period)
    OR (pd.r_period_end BETWEEN p.start_period AND p.end_period)
    OR (p.start_period BETWEEN pd.r_period_start AND pd.r_period_end)
    OR (p.end_period BETWEEN pd.r_period_start AND pd.r_period_end)
