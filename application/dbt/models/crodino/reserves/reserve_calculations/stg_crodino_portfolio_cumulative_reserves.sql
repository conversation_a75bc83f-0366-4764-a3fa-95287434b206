{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}


SELECT
    distribution_partner,
    product_id,
    policy_id,
    cover_id,
    crodino_policy_id,
    cover,
    class_of_business,
    reserve_period_type,
    suspension_status,
    policy_annual_date_rule,
    reserve_start_period,
    reserve_end_period,
    reserve_quarter,
    reserve_year,
    policy_annual_start_date_local,
    policy_annual_end_date_local,
    policy_end_date_after_suspension,
    applicable_start_date,
    applicable_end_date,
    annualized_gross_written_premium,
    annualized_ipt,
    reserve_days,
    days,
    cumulative_days,
    policy_days,
    applicable_annual_share,
    earned_premium_reserve,
    cumulative_earned_premium_reserve,
    earned_ipt,
    cumulative_earned_ipt
FROM {{ ref('stg_crodino_portfolio_cumulative_reserves_1') }}

UNION ALL

SELECT
    distribution_partner,
    product_id,
    policy_id,
    cover_id,
    crodino_policy_id,
    cover,
    class_of_business,
    reserve_period_type,
    suspension_status,
    policy_annual_date_rule,
    reserve_start_period,
    reserve_end_period,
    reserve_quarter,
    reserve_year,
    policy_annual_start_date_local,
    policy_annual_end_date_local,
    policy_end_date_after_suspension,
    applicable_start_date,
    applicable_end_date,
    annualized_gross_written_premium,
    annualized_ipt,
    reserve_days,
    days,
    cumulative_days,
    policy_days,
    applicable_annual_share,
    earned_premium_reserve,
    cumulative_earned_premium_reserve,
    earned_ipt,
    cumulative_earned_ipt
FROM {{ ref('stg_crodino_portfolio_cumulative_reserves_2') }}
