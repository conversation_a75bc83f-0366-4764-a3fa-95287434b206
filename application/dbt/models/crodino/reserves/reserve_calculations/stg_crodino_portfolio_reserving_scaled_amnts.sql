{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}

SELECT
    distribution_partner,
    product_id,
    policy_id,
    cover_id,
    crodino_policy_id,
    cover,
    class_of_business,
    policy_annual_date_rule,
    reserve_period_type,
    suspension_status,
    reserve_start_period,
    reserve_end_period,
    reserve_quarter,
    reserve_year,
    policy_annual_start_date_local,
    policy_annual_end_date_local,
    policy_end_date_after_suspension,
    applicable_start_date,
    applicable_end_date,
    annualized_gross_written_premium,
    annualized_ipt,
    reserve_days,
    days,
    policy_days,
    applicable_annual_share,
    CASE
        WHEN
            policy_annual_date_rule IN (
                'ANNUAL_CANCELLED',
                'ANNUAL_SUBSINOUT',
                'ANNUAL_SUBSIN_CANCELLED',
                'ANNUAL_SUBSOUT',
                'MONTHLY_CANCELLED',
                'MONTHLY_SUBSINOUT',
                'MONTHLY_SUBSIN_CANCELLED',
                'MONTHLY_SUBSOUT',
                'SEMIANNUAL_CANCELLED',
                'SEMIANNUAL_SUBSINOUT',
                'SEMIANNUAL_SUBSIN_CANCELLED',
                'SEMIANNUAL_SUBSOUT'
            )
        AND SUM(applicable_annual_share) OVER (PARTITION BY crodino_policy_id, cover, class_of_business) > 0
        THEN 1 / SUM(applicable_annual_share) OVER (PARTITION BY crodino_policy_id, cover, class_of_business)

        WHEN policy_annual_end_date_local != policy_end_date_after_suspension AND policy_annual_end_date_local != policy_annual_start_date_local
        THEN (
            EXTRACT('epoch' FROM (policy_end_date_after_suspension - policy_annual_start_date_local))
            - SUM(EXTRACT('epoch' FROM (applicable_end_date - applicable_start_date)))
                FILTER (WHERE reserve_period_type = 'SUSPENSION_PERIOD' )
                OVER (PARTITION BY crodino_policy_id, cover, class_of_business)
        ) / EXTRACT('epoch' FROM (policy_annual_end_date_local - policy_annual_start_date_local))

        ELSE 1
    END AS scaling
FROM {{ ref('stg_crodino_portfolio_reserving_calculated_amnts') }}
