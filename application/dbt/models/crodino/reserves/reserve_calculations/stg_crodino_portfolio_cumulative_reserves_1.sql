{{
    config(
        materialized="table",
        tags=["model-crodino-premium-reserves"]
    )
}}


SELECT
    ep.distribution_partner,
    ep.product_id,
    ep.policy_id,
    ep.cover_id,
    ep.crodino_policy_id,
    ep.cover,
    ep.class_of_business,
    ep.reserve_period_type,
    ep.suspension_status,
    ep.policy_annual_date_rule,
    ep.reserve_start_period,
    ep.reserve_end_period,
    ep.reserve_quarter,
    ep.reserve_year,
    ep.policy_annual_start_date_local,
    ep.policy_annual_end_date_local,
    ep.policy_end_date_after_suspension,
    ep.applicable_start_date,
    ep.applicable_end_date,
    ep.annualized_gross_written_premium,
    ep.annualized_ipt,
    ep.reserve_days,
    ep.days,
    ra.cumulative_days,
    ep.policy_days,
    ep.applicable_annual_share,
    ep.earned_premium_reserve,
    ra.cumulative_earned_premium_reserve,
    ep.earned_ipt,
    ra.cumulative_earned_ipt
FROM {{ ref('stg_crodino_portfolio_reserving_earned_premium') }} ep
LEFT JOIN {{ ref('stg_crodino_portfolio_cumulative_reserves_aggregation') }} ra
    ON ep.crodino_policy_id = ra.crodino_policy_id
    AND ep.cover = ra.cover
    AND ep.class_of_business = ra.class_of_business
    AND ep.reserve_start_period = ra.reserve_start_period
WHERE ep.reserve_start_period <= '2022-09-01 00:00:00.000000'
    AND ra.reserve_start_period <= '2022-09-01 00:00:00.000000'
