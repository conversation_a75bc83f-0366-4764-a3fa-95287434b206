{%
    set refund_natures_of_contracts = (
		'Recesso - Annuale',
		'Recesso - Mensile',
		'Recesso - Semestrale',

		'Interruzione - Annuale',
		'Interruzione - Mensile',
		'Interruzione - Semestrale',

		'Interruzione SosIN - Annuale',
		'Interruzione SosIN - Mensile',
		'Interruzione SosIN - Semestrale',

		'Interruzione forzata - Annuale',
		'Interruzione forzata - Mensile',
		'Interruzione forzata - Semestrale',
    )
%}

SELECT
    bordereau_start_date,
    policy_id,
    product_id,
    branch,
    STRING_AGG(DISTINCT crodino_payment_type, ',')     AS natures_of_contract,
    SUM(gross_written_premium_paid)                    AS gross_written_premium_paid,
    SUM(ssn_tax_paid)                                  AS ssn_tax_paid,
    SUM(cvt_tax_paid)                                  AS cvt_tax_paid,
    SUM(antiracket_tax_paid)                           AS antiracket_tax_paid,
    SUM(insurance_premium_tax_paid)                    AS insurance_premium_tax_paid
FROM {{ ref('fct_crodino_premiums') }}
WHERE
    location_of_risk = 'IT'
GROUP BY
    bordereau_start_date,
    policy_id,
    product_id,
    branch
HAVING
    STRING_AGG(DISTINCT crodino_payment_type, ',') IN {{ refund_natures_of_contracts }}
AND
    SUM(gross_written_premium_paid) < 0
