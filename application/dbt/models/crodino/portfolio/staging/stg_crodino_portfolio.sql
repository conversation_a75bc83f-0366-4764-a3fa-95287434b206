{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"]
    )
}}

SELECT
    p.id,

    p.agent_policy_number,
    p.parent_code_renewal,
    p.parent_code,
    p.parent_issue_date,
    p.underwriting_year,
    p.location_of_underwriting,
    CASE LOWER(p.product_type)
        WHEN 'famiglia' THEN 'PFAMILY'
        WHEN 'casa' THEN 'PHOUSE'
        WHEN 'auto' THEN 'PMOTOR'
        ELSE p.product_type
    END                                                               AS product_id,
    p.product_type,
    p.form_type,
    p.cover,
    p.cover_level,
    p.class_of_business,
    p.great_lakes_policy_sequence_number,
    ROUND(
        (REGEXP_MATCH(
            CASE
                WHEN p.limit_value ~ '€' THEN REPLACE(p.limit_value, '.', '')
                ELSE NULLIF(p.limit_value, '0')
            END, '[\d.]+')
        )[1]::NUMERIC
    ,0)::INTEGER                                                      AS limit_value,
    (REGEXP_MATCH(
        CASE
            WHEN p.deductible_value ~ '€' THEN REPLACE(p.deductible_value, '.', '')
            WHEN p.deductible_value ~ '%' THEN '0.' || LPAD(p.deductible_value, 3, '0')
            ELSE NULLIF(p.deductible_value, '0')
        END, '[\d.]+')
    )[1]::NUMERIC                                                    AS deductible_value,
    CASE
        WHEN p.cover = 'infortuni_domestici' THEN 'PERCENT'
        WHEN NULLIF(p.deductible_value, '0') IS NULL THEN NULL
        ELSE 'FIXED'
    END                                                              AS deductible_type,

    p.annual_gross_written_premium,
    p.annual_ipt,

    p.instalment,
    p.payment_frequency,
    p.nature_of_contract,
    t.transaction_type,
    t.policy_status,
    t.annulment,
    COUNT(*) FILTER (WHERE t.annulment) OVER (
        PARTITION BY
            p.agent_policy_number,
            p.cover,
            p.class_of_business,
            p.instalment,
            t.transaction_type
        ORDER BY
            p.issue_date DESC
    ) > 0                                                             AS annulled,
    p.issue_date,
    p.issue_date AT TIME ZONE 'UTC' AT TIME ZONE 'Europe/Rome'        AS issue_date_local,
    p.payment_date,
    p.payment_date AT TIME ZONE 'UTC' AT TIME ZONE 'Europe/Rome'      AS payment_date_local,
    p.period_start_date,
    p.period_start_date AT TIME ZONE 'UTC' AT TIME ZONE 'Europe/Rome' AS period_start_date_local,
    p.period_end_date,
    p.period_end_date AT TIME ZONE 'UTC' AT TIME ZONE 'Europe/Rome'   AS period_end_date_local,
    p.valid_until,

    p.transaction_currency,
    p.total_underwritten,
    p.gross_written_premium,
    p.policy_substitution_fee,
    p.ipt_rate,
    p.ipt,
    p.ssn_tax_rate,
    p.ssn_tax,
    p.cvt_tax_rate,
    p.cvt_tax,
    p.antiracket_tax_rate,
    p.antiracket_tax,
    p.tax_deducted,
    p.terrorism_premium,
    p.reinsurance_premium,
    p.brokerage,
    p.agency_commission,
    p.provisional_profit_commission,
    p.commission_rate,
    p.commission,
    p.net_balance_due_to_iptiq,

    p.codice_fiscale,
    p.insured_name,
    p.insured_address,
    p.comune,
    p.post_code,
    r.province_name                                                   AS province,
    r.region_name                                                     AS region,
    p.ipt_territory,
    p.location_of_risk,

    p.plate_number,
    p.vehicle_category,

    p.property_city,
    p.property_post_code,
    INITCAP(p.building_type)                                          AS building_type,
    UPPER(
        REGEXP_REPLACE(p.house_type, '([a-z])([A-Z])','\1_\2', 'g')
    )                                                                 AS house_type,
    UPPER(
        REGEXP_REPLACE(p.apartment_type, '([a-z])([A-Z])','\1_\2', 'g')
    )                                                                 AS apartment_type,
    UPPER(p.apartment_floor)                                          AS apartment_floor,
    UPPER(
        REGEXP_REPLACE(p.building_use, '([a-z])([A-Z])','\1_\2', 'g')
    )                                                                 AS building_use,
    p.square_meters,
    UPPER(p.construction_year)                                        AS construction_year,
    p.coliving_adults,
    p.coliving_minors,
    p.past_incidents,
    p.discount_10pct,

    p.direct_or_reinsurance,
    p.source,

    p.chksum,
    p.created_on,
    p.dbt_updated_at,
    p.dbt_valid_from,
    p.dbt_valid_to,
    p.dbt_valid_to IS NULL                                            AS transaction_iscurrent
FROM {{ ref('crodino_premium_history') }} p
LEFT JOIN {{ ref('crodino_transaction_types')}} t
    ON LOWER(p.nature_of_contract) = t.nature_of_contract
LEFT JOIN {{ ref('it_region_mapping')}} r
    ON p.ipt_territory = r.province_code
