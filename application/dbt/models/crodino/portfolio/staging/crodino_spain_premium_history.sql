{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"],
        post_hook=[
          {
            "sql": "{% if env_var('DATALAKE_ENV', 'other') != 'tests' %} grant usage on schema {{ this.schema }} to readonly_athena_query_federation {% endif %} ",
            "transaction": False
          },
          {
            "sql": "{% if env_var('DATALAKE_ENV', 'other') != 'tests' %} grant select on {{ this }} to readonly_athena_query_federation {% endif %} ",
            "transaction": False
          }
        ]
    )
}}

SELECT
    s.id,

    CONCAT(
        s.policy_number,
        EXTRACT(YEAR FROM s.policy_start_date)
    )                                                                                AS agent_policy_number,
    s.policy_number,
    EXTRACT(YEAR FROM s.policy_start_date)                                           AS underwriting_year,
    s.location_of_underwriting,
    UPPER(s.policy_status)                                                           AS contract_status,
    s.policy_start_date::TIMESTAMP                                                   AS policy_start_date,
    s.policy_end_date::TIMESTAMP                                                     AS policy_end_date,
    s.cover,
    s.cover_level,
    s.class_of_business,
    s.insured_value_pd::INT,
    s.insured_value_bi::INT,
    s.insured_value_mod::INT,
    s.insured_value_other::INT,

    s.annual_gross_written_premium,
    s.annual_ipt,

    s.payment_frequency,
    s.installment::INTEGER                                                           AS instalment,
    INITCAP(s.nature_of_contract)                                                    AS nature_of_contract,
    s.issue_date::TIMESTAMP,
    s.payment_date::TIMESTAMP,
    s.period_start_date::TIMESTAMP,
    s.period_end_date::TIMESTAMP,

    s.transaction_currency,
    s.total_underwritten,
    s.gross_written_premium,
    s.ipt_rate::NUMERIC,
    s.ipt,
    s.surcharge_tax_rate,
    s.surcharge_tax,
    s.clea_rate,
    s.clea,
    s.national_guarantee_fund_rate,
    s.national_guarantee_fund,
    s.ofesauto,
    s.commission_rate,
    s.commission,
    s.net_balance_due_to_iptiq,

    s.cancellation_reason,
    CASE lower(s.cancellation_requested_by)
        WHEN 'prima' THEN 'Prima'
        WHEN 'customer' THEN 'Customer'
        WHEN 'costumer' THEN 'Customer'
        ELSE s.cancellation_requested_by
    END                                                                              AS cancellation_requested_by,
    s.cancellation_occurred_on::TIMESTAMP                                            AS cancellation_occurred_on,
    s.cancellation_processed_at::TIMESTAMP                                           AS cancellation_processed_at,

    s.insured_nif,
    INITCAP(s.insured_name)                                                          AS insured_name,
    s.street,
    INITCAP(s.city)                                                                  AS city,
    s.post_code,
    s.ipt_territory,
    s.location_of_risk,
    s.residency_country,

    s.main_driver_nif,
    s.main_driver_name,
    s.main_driver_birthdate                                                          AS date_of_birth,
    s.driving_licence_date,
    s.driving_licence_country,

    s.vehicle_registration_number                                                    AS plate_number,
    s.vehicle_category,
    s.vehicle_make,
    s.vehicle_model,
    s.registration_date,
    s.actual_value,
    s.vehicle_power,
    s.fuel_type,
    s.main_driving_region,

    s.distribution_channel                                                           AS source,

    s.chksum,
    s.created_on,
    {{ dbt_utils.generate_surrogate_key(['s.id']) }}                                 AS dbt_scd_id,
    s.created_on                                                                     AS dbt_updated_at,
    s.created_on                                                                     AS dbt_valid_from,
    -- Set valid_to to the next version's timestamp if there is a newer version
    LEAD(s.created_on) OVER (
        PARTITION BY
            s.policy_number::TEXT,
            s.cover::TEXT,
            s.nature_of_contract::TEXT,
            s.class_of_business::TEXT,
            s.installment::TEXT,
            s.period_start_date::TIMESTAMP,
            s.period_end_date::TIMESTAMP
        ORDER BY s.created_on)                                                       AS dbt_valid_to,
    f.key,
    f.branch,
    f.bordereau_start_date,
    f.bordereau_end_date
FROM {{ source('mgalanding', 'prima_premium_spain') }} s
LEFT JOIN {{ ref('crodino_files') }} f
    ON s.chksum = f.chksum
WHERE
    NOT f.to_be_skipped  -- Do not propagate the files that needs to be skipped, e.g., due to being an old version
