{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"],
        post_hook=[
          {
            "sql": "{% if env_var('DATALAKE_ENV', 'other') != 'tests' %} grant usage on schema {{ this.schema }} to readonly_athena_query_federation {% endif %} ",
            "transaction": False
          },
          {
            "sql": "{% if env_var('DATALAKE_ENV', 'other') != 'tests' %} grant select on {{ this }} to readonly_athena_query_federation {% endif %} ",
            "transaction": False
          }
        ]
    )
}}

SELECT
    id,

    agent_policy_number,
    parent_code_renewal,
    REGEXP_SUBSTR(parent_code, '([[:alnum:]]+)')           AS parent_code,
    NULLIF(data_emissione_sost, ' ')::TIMESTAMP            AS parent_issue_date,
    underwriting_year,
    location_of_underwriting,
    CASE
        WHEN product_type IS NOT NULL THEN product_type
        WHEN use = 'NO_PROPERTY' THEN 'Famiglia'
        WHEN use IS NOT NULL THEN 'Casa'
        WHEN targa IS NOT NULL THEN 'Auto'
    END                                                    AS product_type,
    CASE
        WHEN form_type IS NOT NULL THEN form_type
        WHEN square_meters IS NOT NULL THEN 'full'
        WHEN product_type = 'Casa' THEN 'family_only'
    END                                                    AS form_type,
    descrizione_garanzia                                   AS cover,
    NULLIF(tipologia_garanzia_acquistata, ' ')             AS cover_level,
    class_of_business::VARCHAR,
    great_lakes_policy_sequence_number,
    limit_value,
    deductible_value,

    annual_gross_premium                                   AS annual_gross_written_premium,
    annual_ipt_amount                                      AS annual_ipt,

    instalment,
    frequenza                                              AS payment_frequency,
    nature_of_contract,
    data_emissione                                         AS issue_date,
    data_incasso                                           AS payment_date,
    period_start_date,
    period_end_date,
    valid_until,

    transaction_currency,
    premio_totale                                          AS total_underwritten,
    gross_premium                                          AS gross_written_premium,
    substitution_fee                                       AS policy_substitution_fee,
    ipt_rate::NUMERIC                                      AS ipt_rate,
    ipt_amount                                             AS ipt,
    imposta_ssn::NUMERIC                                   AS ssn_tax_rate,
    imposta_ssn_eur                                        AS ssn_tax,
    imposta_cvt::NUMERIC                                   AS cvt_tax_rate,
    imposta_cvt_eur                                        AS cvt_tax,
    imposta_antiracket::NUMERIC                            AS antiracket_tax_rate,
    imposta_antiracket_eur                                 AS antiracket_tax,
    NULLIF(tax_deducted, ' ')::NUMERIC                     AS tax_deducted,
    NULLIF(terrorism_premium, ' ')::NUMERIC                AS terrorism_premium,
    NULLIF(riassicurazione_premi, ' ')::NUMERIC            AS reinsurance_premium,
    NULLIF(brokerage, ' ')::NUMERIC                        AS brokerage,
    agency_commission,
    provisional_profit_commission,
    provvigione::NUMERIC                                   AS commission_rate,
    provvigione_eur                                        AS commission,
    net_balance_due_to_iptq                                AS net_balance_due_to_iptiq,

    cod_fiscale                                            AS codice_fiscale,
    insured_name,
    insured_address,
    comune,
    LPAD(post_code, 5, '0')                                AS post_code,
    ipt_territory,
    location_of_risk,

    UPPER(targa)                                           AS plate_number,
    veicolo                                                AS vehicle_category,

    property_city,
    LPAD(property_zipcode, 5, '0')                         AS property_post_code,
    type_of_building                                       AS building_type,
    NULLIF(house_type, '0')                                AS house_type,
    NULLIF(condominium_type, '0')                          AS apartment_type,
    NULLIF(floor, '0')                                     AS apartment_floor,
    use                                                    AS building_use,
    square_meters,
    construction_year_or_latest_significative_renew        AS construction_year,
    coliving_adults,
    coliving_minors,
    past_incidents,
    discount_10pct,

    direct_or_reinsurance,
    source,

    chksum,
    created_on,
    {{ dbt_utils.generate_surrogate_key(['id']) }}         AS dbt_scd_id,
    created_on                                             AS dbt_updated_at,
    created_on                                             AS dbt_valid_from,
    -- Set valid_to to the next version's timestamp if there is a newer version
    LEAD(created_on) OVER (
        PARTITION BY
            agent_policy_number,
            descrizione_garanzia,
            nature_of_contract,
            class_of_business,
            instalment,
            period_start_date,
            period_end_date
        ORDER BY created_on)                               AS dbt_valid_to
FROM {{ source('mgalanding', 'prima_premium') }}
WHERE
    /* The following files were re-issued, so excluding not to have duplicated values*/
    chksum NOT IN (
        '3d8fdde2aecc4955dfc86297a7cbb605-3', --DEV: initial testfile
        '1a715421268b5c978efd852c04a4f10f', --DEV: 823a6b2cdb9f3c1b6d3dbcb84cb9b37e 07. July - Premia DE.xlsx
        'f4bccc2fe29d9498385c19e713908631', --DEV e0abeb2a0f2cdbd71251d49c7e3c5aed 07. July - Premia IT.xlsx
        '276c5a1123ea01fc681dd3403e04fab0', --PROD: 823a6b2cdb9f3c1b6d3dbcb84cb9b37e 07. July - Premia DE.xlsx
        'cff6c424fffef4a787dc5e253331894d'  --PROD: e0abeb2a0f2cdbd71251d49c7e3c5aed 07. July - Premia IT.xlsx
    )
