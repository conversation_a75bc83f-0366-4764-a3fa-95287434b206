SELECT
    500000000000 + s.id                                                     AS id,

    s.agent_policy_number,
    CASE s.underwriting_year
        WHEN MIN(s.underwriting_year) OVER (PARTITION BY s.policy_number)
            THEN NULL
        ELSE CONCAT(s.policy_number, s.underwriting_year - 1)
    END                                                                     AS parent_code_renewal,
    s.agent_policy_number                                                   AS parent_code,
    s.underwriting_year,
    s.location_of_underwriting,
    'PMOTORES'                                                              AS product_id,
    'Auto'                                                                  AS product_type,
    NULL                                                                    AS form_type,
    FALSE                                                                   AS is_household,
    s.cover,
    s.cover_level,
    s.class_of_business,
    NULL                                                                    AS great_lakes_policy_sequence_number,
    NULL::NUMERIC                                                           AS limit_value,
    NULL::NUMERIC                                                           AS deductible_value,
    /* Prima aren't currently providing insured values, but driver accident cover must be 50k from the ER surcharge */
    COALESCE(
            s.insured_value_bi,
            CASE s.cover
                WHEN 'DriverAccident'
                THEN 50000
            END
    )                                                                       AS insured_value_bi,
    s.insured_value_pd,
    s.insured_value_mod,
    s.insured_value_other,
    s.annual_gross_written_premium,
    s.annual_ipt,

    s.instalment,
    CASE
        WHEN LOWER(s.payment_frequency) = 'annual' THEN 'yearly'
        ELSE s.payment_frequency
    END                                                                     AS payment_frequency,
    s.nature_of_contract,
    s.issue_date                                                            AS issue_date,
    s.issue_date                                                            AS issue_date_local,
    COALESCE(s.cancellation_occurred_on, s.payment_date)                    AS payment_date,
    COALESCE(s.cancellation_occurred_on, s.payment_date)                    AS payment_date_local,
    CASE
        WHEN s.cancellation_processed_at < s.policy_start_date
        THEN s.policy_start_date
        ELSE COALESCE(s.cancellation_processed_at, s.period_start_date)
    END                                                                     AS period_start_date,
    CASE
        WHEN s.cancellation_processed_at < s.policy_start_date
        THEN s.policy_start_date
        ELSE COALESCE(s.cancellation_processed_at, s.period_start_date)
    END                                                                     AS period_start_date_local,
    s.period_end_date,
    s.period_end_date                                                       AS period_end_date_local,

    s.transaction_currency,
    s.total_underwritten,
    s.gross_written_premium,
    s.ipt_rate,
    s.ipt,
    s.surcharge_tax_rate,
    s.surcharge_tax,
    s.clea_rate,
    s.clea,
    s.national_guarantee_fund_rate,
    s.national_guarantee_fund,
    s.ofesauto,
    --The following are NULL because these taxes refer to Prima IT/DE and not Prima ES
    NULL::NUMERIC                                                           AS ssn_tax_rate,
    0                                                                       AS ssn_tax,
    NULL::NUMERIC                                                           AS cvt_tax_rate,
    0                                                                       AS cvt_tax,
    NULL::NUMERIC                                                           AS antiracket_tax_rate,
    0                                                                       AS antiracket_tax,
    NULL::NUMERIC                                                           AS reinsurance_premium,
    NULL::NUMERIC                                                           AS substitution_fee,
    s.commission_rate,
    s.commission,
    s.net_balance_due_to_iptiq,

    s.cancellation_reason,
    s.cancellation_requested_by,
    s.cancellation_occurred_on,
    s.cancellation_processed_at,

    CASE
        WHEN
            ARRAY_AGG(LOWER(s.contract_status)) OVER (PARTITION BY s.agent_policy_number)
                && ARRAY['cancellation', 'cancelation', 'annulment']
            AND s.payment_frequency = 'Annual'
        THEN 'ANNUAL_CANCELLED'
        WHEN 'issuance' = ANY(ARRAY_AGG(LOWER(s.contract_status)) OVER (PARTITION BY s.agent_policy_number))
            AND s.payment_frequency = 'Annual'
        THEN 'ANNUAL_BASE'
        ELSE 'OTHER'
    END                                                                     AS policy_annual_date_rule,
    s.policy_start_date                                                     AS policy_annual_start_date_local,
    MIN(COALESCE(s.cancellation_processed_at, s.policy_end_date)) OVER
        (PARTITION BY s.agent_policy_number)                                AS policy_annual_end_date_local,
    NULL::TIMESTAMP                                                         AS policy_annual_renewal_date_local,
    s.agent_policy_number                                                   AS original_policy_number,
    s.policy_start_date                                                     AS original_policy_annual_start_date_local,
    MIN(COALESCE(s.cancellation_processed_at, s.policy_end_date)) OVER
        (PARTITION BY s.agent_policy_number)                                AS original_policy_annual_end_date_local,
    NULL::TIMESTAMP                                                         AS original_policy_annual_renewal_date_local,
    s.agent_policy_number                                                   AS policy_path,
    0                                                                       AS policy_depth,

    s.insured_nif,
    s.insured_name,
    s.date_of_birth,
    DATE_PART('year', AGE(s.policy_start_date, s.date_of_birth::DATE))      AS age_at_policy_start,
    s.street                                                                AS insured_address,
    s.city,
    s.post_code,
    m.province_name                                                         AS province,
    m.region_name                                                           AS region,
    m.region_code                                                           AS ipt_territory,
    s.ipt_territory                                                         AS ipt_country,
    s.location_of_risk,

    UPPER(s.plate_number)                                                   AS plate_number,
    s.vehicle_category,
    s.vehicle_make,
    s.vehicle_model,
    s.vehicle_power,
    s.fuel_type,
    s.main_driving_region,
    s.actual_value,

    --The following are NULL due to of lack of household insurance in Prima ES
    NULL                                                                    AS property_city,
    NULL                                                                    AS property_post_code,
    NULL                                                                    AS building_type,
    NULL                                                                    AS house_type,
    NULL                                                                    AS apartment_type,
    NULL                                                                    AS apartment_floor,
    NULL                                                                    AS building_use,
    NULL::NUMERIC                                                           AS square_meters,
    NULL                                                                    AS construction_year,
    NULL::NUMERIC                                                           AS coliving_adults,
    NULL::NUMERIC                                                           AS coliving_minors,
    NULL::NUMERIC                                                           AS past_incidents,
    NULL                                                                    AS discount_10pct,

    'D'                                                                     AS direct_or_reinsurance,
    s.source,

    s.chksum,
    s.created_on,
    s.dbt_updated_at,
    s.dbt_valid_from,
    s.dbt_valid_to,
    s.key,
    s.branch,
    s.bordereau_start_date,
    s.bordereau_end_date,
    s.dbt_valid_to IS NULL                                                  AS transaction_iscurrent
FROM {{ ref('crodino_spain_premium_history') }} s
LEFT JOIN {{ ref('es_region_mapping') }} m
    ON LEFT(s.post_code, 2) = m.post_code
