{{
    config(
        tags=["model-crodino-portfolio"]
    )
}}

SELECT
    p.id,

    p.agent_policy_number,
    p.parent_code_renewal,
    b.parent_code,
    p.underwriting_year,
    p.location_of_underwriting,
    f.branch,
    p.product_id,
    p.product_type,
    p.form_type,
    f.is_household,
    p.cover,
    p.cover_level,
    p.class_of_business,
    p.great_lakes_policy_sequence_number,
    p.limit_value,
    p.deductible_value,
    NULL                                                                    AS insured_value_pd,
    NULL                                                                    AS insured_value_bi,
    NULL                                                                    AS insured_value_mod,
    NULL                                                                    AS insured_value_other,
    b.annualized_gross_written_premium,
    b.annualized_ipt,

    p.instalment,
    p.payment_frequency,
    p.nature_of_contract,
    p.issue_date,
    p.issue_date_local,
    p.payment_date,
    p.payment_date_local,
    p.period_start_date,
    p.period_start_date_local,
    p.period_end_date,
    p.period_end_date_local,

    p.transaction_currency,
    p.total_underwritten,
    p.gross_written_premium,
    p.ipt_rate,
    p.ipt,
    0                                                                       AS surcharge_tax_rate,
    0                                                                       AS surcharge_tax,
    0                                                                       AS clea_rate,
    0                                                                       AS clea,
    0                                                                       AS national_guarantee_fund_rate,
    0                                                                       AS national_guarantee_fund,
    0                                                                       AS ofesauto,
    p.ssn_tax_rate,
    p.ssn_tax,
    p.cvt_tax_rate,
    p.cvt_tax,
    p.antiracket_tax_rate,
    p.antiracket_tax,
    p.reinsurance_premium,
    b.substitution_fee,
    p.commission_rate,
    p.commission,
    p.net_balance_due_to_iptiq,

    NULL                                                                    AS cancellation_reason,
    NULL                                                                    AS cancellation_requested_by,
    NULL                                                                    AS cancellation_occurred_on,
    NULL                                                                    AS cancellation_processed_at,

    b.policy_annual_date_rule,
    b.policy_annual_start_date_local,
    b.policy_annual_end_date_local,
    b.policy_annual_renewal_date_local,
    b.original_policy_number,
    b.original_policy_annual_start_date_local,
    b.original_policy_annual_end_date_local,
    b.original_policy_annual_renewal_date_local,
    b.policy_path,
    b.policy_depth,

    p.codice_fiscale,
    p.insured_name,
    cf.date_of_birth,
    EXTRACT(YEAR FROM AGE(cf.policy_start_date, cf.date_of_birth))          AS age_at_policy_start,
    p.insured_address,
    p.comune                                                                AS city,
    p.post_code,
    p.province,
    p.region,
    p.ipt_territory,
    p.location_of_risk,

    p.plate_number,
    p.vehicle_category,
    NULL                                                                    AS vehicle_make,
    NULL                                                                    AS vehicle_model,
    NULL                                                                    AS vehicle_power,
    NULL                                                                    AS fuel_type,
    NULL                                                                    AS actual_value,


    p.property_city,
    p.property_post_code,
    p.building_type,
    p.house_type,
    p.apartment_type,
    p.apartment_floor,
    p.building_use,
    p.square_meters,
    p.construction_year,
    p.coliving_adults,
    p.coliving_minors,
    p.past_incidents,
    p.discount_10pct,

    p.direct_or_reinsurance,
    p.source,

    p.chksum,
    p.created_on,
    p.transaction_iscurrent,
    f.key,
    f.bordereau_start_date,
    f.bordereau_end_date
FROM {{ ref('stg_crodino_portfolio') }} p
LEFT JOIN {{ ref('stg_crodino_portfolio_annual_premium_amount') }} b
    ON b.id = p.id
LEFT JOIN {{ ref('stg_crodino_codice_fiscale') }} cf
    ON cf.codice_fiscale = p.codice_fiscale
LEFT JOIN {{ ref('crodino_files') }} f
    ON f.chksum = p.chksum

UNION ALL

SELECT
    s.id,

    s.agent_policy_number,
    s.parent_code_renewal,
    s.parent_code,
    s.underwriting_year,
    s.location_of_underwriting,
    s.branch,
    s.product_id,
    s.product_type,
    s.form_type,
    s.is_household,
    s.cover,
    s.cover_level,
    s.class_of_business,
    s.great_lakes_policy_sequence_number,
    s.limit_value,
    s.deductible_value,
    s.insured_value_pd,
    s.insured_value_bi,
    s.insured_value_mod,
    s.insured_value_other,
    s.annual_gross_written_premium,
    s.annual_ipt,

    s.instalment,
    s.payment_frequency,
    s.nature_of_contract,
    s.issue_date,
    s.issue_date_local,
    s.payment_date,
    s.payment_date_local,
    s.period_start_date,
    s.period_start_date_local,
    s.period_end_date,
    s.period_end_date_local,

    s.transaction_currency,
    s.total_underwritten,
    s.gross_written_premium,
    s.ipt_rate,
    s.ipt,
    s.surcharge_tax_rate,
    s.surcharge_tax,
    s.clea_rate,
    s.clea,
    s.national_guarantee_fund_rate,
    s.national_guarantee_fund,
    s.ofesauto,
    s.ssn_tax_rate,
    s.ssn_tax,
    s.cvt_tax_rate,
    s.cvt_tax,
    s.antiracket_tax_rate,
    s.antiracket_tax,
    s.reinsurance_premium,
    s.substitution_fee,
    s.commission_rate,
    s.commission,
    s.net_balance_due_to_iptiq,

    s.cancellation_reason,
    s.cancellation_requested_by,
    s.cancellation_occurred_on,
    s.cancellation_processed_at,

    s.policy_annual_date_rule,
    s.policy_annual_start_date_local,
    s.policy_annual_end_date_local,
    s.policy_annual_renewal_date_local,
    s.original_policy_number,
    s.original_policy_annual_start_date_local,
    s.original_policy_annual_end_date_local,
    s.original_policy_annual_renewal_date_local,
    s.policy_path,
    s.policy_depth,

    s.insured_nif,
    s.insured_name,
    s.date_of_birth,
    s.age_at_policy_start,
    s.insured_address,
    s.city,
    s.post_code,
    s.province,
    s.region,
    s.ipt_territory,
    s.location_of_risk,

    s.plate_number,
    s.vehicle_category,
    s.vehicle_make,
    s.vehicle_model,
    s.vehicle_power,
    s.fuel_type,
    s.actual_value,

    s.property_city,
    s.property_post_code,
    s.building_type,
    s.house_type,
    s.apartment_type,
    s.apartment_floor,
    s.building_use,
    s.square_meters,
    s.construction_year,
    s.coliving_adults,
    s.coliving_minors,
    s.past_incidents,
    s.discount_10pct,

    s.direct_or_reinsurance,
    s.source,

    s.chksum,
    s.created_on,
    s.transaction_iscurrent,
    s.key,
    s.bordereau_start_date,
    s.bordereau_end_date
FROM {{ ref('stg_crodino_spain_portfolio') }} s
