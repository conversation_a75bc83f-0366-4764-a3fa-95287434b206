{{
    config(
        materialized="table",
        tags=["crodino", "premium"]
    )
}}

{%
    set payment_columns = [
        'distribution_partner',
        'payment_key_type',
        'crodino_payment_id',
        'branch',
    ]
%}

{%
    set policy_columns = [
        'distribution_partner',
        'policy_key_type',
        'crodino_policy_id',
    ]
%}

{%
    set cover_columns = [
        'distribution_partner',
        'policy_key_type',
        'cover_key_type',
        'crodino_policy_id',
        'cover_description',
        'solvency_class',
    ]
%}

WITH
staging AS (
    SELECT
        'CRODINO'                                       AS distribution_partner,
        product_id,
        'crodino_policy_id'                             AS policy_key_type,
        'crodino_cover_id'                              AS cover_key_type,
        'crodino_payment_id'                            AS payment_key_type,
        agent_policy_number                             AS crodino_policy_id,
        cover                                           AS cover_description,
        class_of_business                               AS solvency_class,
        location_of_risk,


        TO_CHAR(issue_date, 'YYYYMMDDHH24MISS')         AS crodino_payment_id,
        nature_of_contract                              AS crodino_payment_type,
        branch,

        issue_date,
        payment_date,
        period_start_date_local                         AS period_start_date,
        period_end_date_local                           AS period_end_date,
        payment_frequency,

        gross_written_premium                           AS gross_written_premium_paid,
        ipt                                             AS insurance_premium_tax_paid,
        ipt_rate::NUMERIC                               AS insurance_premium_tax_rate,
        COALESCE(surcharge_tax, 0)                      AS surcharge_tax_paid,
        COALESCE(surcharge_tax_rate, 0)                 AS surcharge_tax_rate,
        COALESCE(clea, 0)                               AS clea_paid,
        COALESCE(clea_rate, 0)                          AS clea_rate,
        COALESCE(national_guarantee_fund, 0)            AS national_guarantee_fund_paid,
        COALESCE(national_guarantee_fund_rate, 0)       AS national_guarantee_fund_rate,
        COALESCE(ofesauto, 0)                           AS ofesauto_paid,
        COALESCE(
            ofesauto/NULLIF(gross_written_premium, 0), 0
        )::NUMERIC(6, 5)                                AS ofesauto_rate,
        total_underwritten                              AS total_underwritten_premium_paid,
        commission                                      AS commission_paid,
        commission_rate::NUMERIC                        AS commission_rate,
        net_balance_due_to_iptiq,

        /*
            IPT in Italy can be separated into three sections:
            - SSN (Servizio Sanitario Nazionale), a contribution to the national health service
            - CVT (Corpi Veicoli Terrestri), a general road tax
            - Anti-racket, a contribution to a fund for combating organised crime
        */
        ssn_tax                                         AS ssn_tax_paid,
        ssn_tax_rate                                    AS ssn_tax_rate,
        cvt_tax                                         AS cvt_tax_paid,
        cvt_tax_rate                                    AS cvt_tax_rate,
        antiracket_tax                                  AS antiracket_tax_paid,
        antiracket_tax_rate                             AS antiracket_tax_rate,

        bordereau_start_date,
        bordereau_end_date,
        chksum,
        created_on,
        transaction_iscurrent
    FROM {{ ref('crodino_portfolio') }}
)

SELECT
    distribution_partner,
    product_id,
    {{ dbt_utils.generate_surrogate_key(payment_columns) }}     AS payment_id,
    crodino_payment_id,
    crodino_payment_type,

    {{ dbt_utils.generate_surrogate_key(policy_columns) }}      AS policy_id,
    crodino_policy_id,

    {{ dbt_utils.generate_surrogate_key(cover_columns) }}       AS cover_id,
    cover_description,
    solvency_class,
    location_of_risk,
    branch,

    issue_date,
    payment_date,
    period_start_date,
    period_end_date,
    payment_frequency,

    gross_written_premium_paid,
    insurance_premium_tax_paid,
    insurance_premium_tax_rate,
    surcharge_tax_paid,
    surcharge_tax_rate,
    clea_paid,
    clea_rate,
    national_guarantee_fund_paid,
    national_guarantee_fund_rate,
    ofesauto_paid,
    ofesauto_rate,
    total_underwritten_premium_paid,
    commission_paid,
    commission_rate,
    net_balance_due_to_iptiq,

    ssn_tax_paid,
    ssn_tax_rate,
    cvt_tax_paid,
    cvt_tax_rate,
    antiracket_tax_paid,
    antiracket_tax_rate,

    bordereau_start_date,
    bordereau_end_date,
    chksum,
    created_on,
    transaction_iscurrent
FROM staging
