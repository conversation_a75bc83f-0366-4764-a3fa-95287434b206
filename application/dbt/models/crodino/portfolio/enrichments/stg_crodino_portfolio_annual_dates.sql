WITH
flags AS (
    SELECT
        agent_policy_number,
        STRING_AGG(DISTINCT payment_frequency, ', ')                           AS payment_frequency,
        MIN(instalment)                                                        AS policy_min_instalment,
        MIN(period_start_date_local)
            FILTER (WHERE policy_status = 'Active')                            AS min_instalment_start_date_local,
        MIN(period_end_date_local)
            FILTER (WHERE policy_status = 'Active')                            AS min_instalment_end_date_local,
        MIN(period_start_date_local)
            FILTER (WHERE policy_status != 'Active' AND NOT annulled)          AS policy_cancellation_date_local,
        BOOL_OR(transaction_type = 'Issue' AND NOT annulled)                   AS policy_issue,
        BOOL_OR(transaction_type = 'Substitution In' AND NOT annulled)         AS policy_substitution_in,
        BOOL_OR(transaction_type = 'Substitution Out' AND NOT annulled)        AS policy_substitution_out,
        BOOL_OR(transaction_type = 'Termination' AND NOT annulled)             AS policy_termination,
        BOOL_OR(transaction_type = 'Interruption' AND NOT annulled)            AS policy_interruption,
        BOOL_OR(annulled)                                                      AS is_annulled,
        BOOL_OR(transaction_type = 'Substitution In' AND annulled)             AS annulled_policy_substitution_in,
        BOOL_OR(transaction_type = 'Substitution Out' AND annulled)            AS annulled_policy_substitution_out,
        BOOL_OR(transaction_type = 'Termination' AND annulled)                 AS annulled_policy_termination,
        BOOL_OR(transaction_type = 'Interruption' AND annulled)                AS annulled_policy_interruption,
        BOOL_OR(transaction_type IS NULL)                                      AS policy_contains_unexpected_transactions,
        MIN(created_on)                                                        AS created_on,
        MAX(created_on)                                                        AS updated_on
    FROM {{ test_utils.testable_ref('stg_crodino_portfolio') }}
    GROUP BY
        agent_policy_number
)

SELECT
    *,
    CASE
        WHEN policy_issue
            THEN 'Original'
        WHEN policy_substitution_in OR annulled_policy_substitution_in
            THEN 'Substitution'
        ELSE 'Unknown'
    END                                                                        AS policy_start_type,
    CASE
        WHEN annulled_policy_substitution_in
            THEN 'Annulled'
        WHEN policy_termination OR policy_interruption
            THEN 'Cancelled'
        WHEN policy_substitution_out
            THEN 'Substituted'
        ELSE 'Active'
    END                                                                        AS policy_end_type,
    CASE
        /** Regular policies **/
        WHEN NOT is_annulled
        THEN
            CASE
                /** Unknown policies **/
                WHEN policy_contains_unexpected_transactions
                THEN 'OTHER'

                /** Original policies **/
                WHEN policy_issue
                THEN
                    CASE
                        WHEN (policy_interruption OR policy_termination) AND NOT policy_substitution_out
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNUAL_CANCELLED'
                                WHEN 'half-yearly' THEN 'SEMIANNUAL_CANCELLED'
                                WHEN 'monthly' THEN 'MONTHLY_CANCELLED'
                            END

                        WHEN policy_substitution_out AND NOT (policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNUAL_SUBSOUT'
                                WHEN 'half-yearly' THEN 'SEMIANNUAL_SUBSOUT'
                                WHEN 'monthly' THEN 'MONTHLY_SUBSOUT'
                            END

                        WHEN NOT (policy_substitution_out OR policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNUAL_BASE'
                                WHEN 'half-yearly' THEN 'SEMIANNUAL_BASE'
                                WHEN 'monthly' THEN 'MONTHLY_BASE'
                            END

                        ELSE 'OTHER'
                    END

                /** Substitution In policies **/
                WHEN policy_substitution_in
                THEN
                    CASE
                        WHEN (policy_interruption OR policy_termination) AND NOT policy_substitution_out
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNUAL_SUBSIN_CANCELLED'
                                WHEN 'half-yearly' THEN 'SEMIANNUAL_SUBSIN_CANCELLED'
                                WHEN 'monthly' THEN 'MONTHLY_SUBSIN_CANCELLED'
                            END

                        WHEN policy_substitution_out AND NOT (policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNUAL_SUBSINOUT'
                                WHEN 'half-yearly' THEN 'SEMIANNUAL_SUBSINOUT'
                                WHEN 'monthly' THEN 'MONTHLY_SUBSINOUT'
                            END

                        WHEN NOT (policy_substitution_out OR policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNUAL_SUBSIN'
                                WHEN 'half-yearly' THEN 'SEMIANNUAL_SUBSIN'
                                WHEN 'monthly' THEN 'MONTHLY_SUBSIN'
                            END

                        ELSE 'OTHER'
                    END

                ELSE 'OTHER'
            END

        /** Annulled policies **/
        ELSE
            CASE
                /** Unknown policies **/
                WHEN policy_contains_unexpected_transactions
                THEN 'ANNULLED_OTHER'

                /** Annulled original policies **/
                WHEN policy_issue
                THEN
                    CASE
                        WHEN (policy_interruption OR policy_termination) AND NOT policy_substitution_out
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNULLED_ANNUAL_CANCELLED'
                                WHEN 'half-yearly' THEN 'ANNULLED_SEMIANNUAL_CANCELLED'
                                WHEN 'monthly' THEN 'ANNULLED_MONTHLY_CANCELLED'
                            END

                        WHEN policy_substitution_out AND NOT (policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNULLED_ANNUAL_SUBSOUT'
                                WHEN 'half-yearly' THEN 'ANNULLED_SEMIANNUAL_SUBSOUT'
                                WHEN 'monthly' THEN 'ANNULLED_MONTHLY_SUBSOUT'
                            END

                        WHEN NOT (policy_substitution_out OR policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNULLED_ANNUAL_BASE'
                                WHEN 'half-yearly' THEN 'ANNULLED_SEMIANNUAL_BASE'
                                WHEN 'monthly' THEN 'ANNULLED_MONTHLY_BASE'
                            END

                        ELSE 'ANNULLED_OTHER'
                    END

                /** Annulled substitution in policies **/
                WHEN policy_substitution_in AND NOT annulled_policy_substitution_in
                THEN
                    CASE
                        WHEN (policy_interruption OR policy_termination) AND NOT policy_substitution_out
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNULLED_ANNUAL_SUBSIN_CANCELLED'
                                WHEN 'half-yearly' THEN 'ANNULLED_SEMIANNUAL_SUBSIN_CANCELLED'
                                WHEN 'monthly' THEN 'ANNULLED_MONTHLY_SUBSIN_CANCELLED'
                            END

                        WHEN policy_substitution_out AND NOT (policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNULLED_ANNUAL_SUBSINOUT'
                                WHEN 'half-yearly' THEN 'ANNULLED_SEMIANNUAL_SUBSINOUT'
                                WHEN 'monthly' THEN 'ANNULLED_MONTHLY_SUBSINOUT'
                            END

                        WHEN NOT (policy_substitution_out OR policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'ANNULLED_ANNUAL_SUBSIN'
                                WHEN 'half-yearly' THEN 'ANNULLED_SEMIANNUAL_SUBSIN'
                                WHEN 'monthly' THEN 'ANNULLED_MONTHLY_SUBSIN'
                            END

                        ELSE 'ANNULLED_OTHER'
                    END

                WHEN annulled_policy_substitution_in AND NOT policy_substitution_in
                THEN
                    CASE
                        WHEN NOT (policy_substitution_out OR policy_interruption OR policy_termination)
                        THEN
                            CASE payment_frequency
                                WHEN 'yearly' THEN 'FULLY_ANNULLED_ANNUAL_SUBSIN'
                                WHEN 'half-yearly' THEN 'FULLY_ANNULLED_SEMIANNUAL_SUBSIN'
                                WHEN 'monthly' THEN 'FULLY_ANNULLED_MONTHLY_SUBSIN'
                            END

                        ELSE 'ANNULLED_OTHER'
                    END

                ELSE 'ANNULLED_OTHER'
            END
    END                                                                        AS policy_annual_date_rule,

    min_instalment_start_date_local                                            AS policy_annual_start_date_local,

    CASE
        /** Fully annulled policies **/
        WHEN annulled_policy_substitution_in
            THEN min_instalment_start_date_local

        /** Cancelled or Substituted policies **/
        WHEN policy_termination OR policy_interruption OR policy_substitution_out
            THEN policy_cancellation_date_local

        WHEN payment_frequency = 'yearly'
            THEN min_instalment_end_date_local

        WHEN payment_frequency = 'half-yearly'
            THEN min_instalment_end_date_local
                     - MAKE_INTERVAL(months := policy_min_instalment * 6)
                     + interval '12 months'

        WHEN payment_frequency = 'monthly'
            THEN DATE_TRUNC('month', min_instalment_end_date_local)
                     - MAKE_INTERVAL(months := policy_min_instalment - 1)
                     + interval '12 months'
                     - interval '1 second'

    END                                                                        AS policy_annual_end_date_local,

    CASE
        WHEN payment_frequency = 'yearly'
            THEN min_instalment_end_date_local

        WHEN payment_frequency = 'half-yearly'
            THEN min_instalment_end_date_local
                     - MAKE_INTERVAL(months := policy_min_instalment * 6)
                     + interval '12 months'

        WHEN payment_frequency = 'monthly'
            THEN DATE_TRUNC('month', min_instalment_end_date_local)
                     - MAKE_INTERVAL(months := policy_min_instalment - 1)
                     + interval '12 months'
                     - interval '1 second'

END                                                                            AS policy_annual_renewal_date_local
FROM flags
