{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"]
    )
}}
SELECT
    i.parent_code,
    i.agent_policy_number,
    i.substitution_number,
    i.issue_date_local,
    i.nature_of_contract,
    i.plate_number != o.plate_number                                AS changed_vehicle,
    i.codice_fiscale != o.codice_fiscale                            AS changed_person,
    i.post_code != o.post_code                                      AS changed_address,
    ARRAY(SELECT UNNEST(i.covers) EXCEPT SELECT UNNEST(o.covers))   AS covers_added,
    ARRAY(SELECT UNNEST(o.covers) EXCEPT SELECT UNNEST(i.covers))   AS covers_removed,
    CASE
        WHEN i.rca_substitution_fee IS NOT NULL THEN i.rca_substitution_fee
        WHEN i.plate_number != o.plate_number THEN csf.substitution_fee * i.sign
        WHEN i.codice_fiscale != o.codice_fiscale THEN csf.substitution_fee * i.sign
        WHEN i.post_code != o.post_code THEN csf.substitution_fee * i.sign
        WHEN i.covers = o.covers THEN csf.substitution_fee * i.sign
        WHEN i.covers @> o.covers THEN 0
        ELSE csf.substitution_fee * i.sign
    END                                                             AS substitution_fee,
    i.rca_premium / NULLIF(i.rca_annual_premium, 0)                 AS premium_pct_in,
    o.rca_premium / NULLIF(o.rca_annual_premium, 0)                 AS premium_pct_out
FROM {{ ref('stg_crodino_substitution_in') }} i
LEFT JOIN {{ ref('stg_crodino_substitution_out') }} o
    ON i.parent_code = o.agent_policy_number
    AND i.substitution_number = o.substitution_number
LEFT JOIN {{ ref('crodino_substitution_fee') }} csf
    ON i.original_policy_annual_start_date_local >= csf.start_date
    AND i.original_policy_annual_start_date_local < COALESCE(csf.end_date, '3000-01-01'::date)
