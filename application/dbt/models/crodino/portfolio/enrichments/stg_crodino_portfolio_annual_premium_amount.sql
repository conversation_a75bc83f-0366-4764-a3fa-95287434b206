{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"]
    )
}}


WITH
substitution_fee AS (
    SELECT
        pd.*,
        COALESCE(sf.substitution_fee, 0)    AS substitution_fee
    FROM {{ test_utils.testable_ref('stg_original_policies_dates') }} pd
    LEFT JOIN {{ test_utils.testable_ref('stg_crodino_substitution_fee') }} sf
        ON pd.agent_policy_number = sf.agent_policy_number
        AND pd.nature_of_contract = sf.nature_of_contract
        AND pd.issue_date_local = sf.issue_date_local
        AND pd.cover = 'rca'
),
crodino_annual_dates_nb_months AS (
    SELECT
        *,
        CASE
            WHEN
                policy_annual_date_rule IN ('MONTHLY_BASE', 'MONTHLY_CANCELLED', 'MONTHLY_SUBSOUT')
                AND instalment = policy_min_instalment
                AND EXTRACT('DAY' FROM policy_annual_start_date_local) != 1
                AND EXTRACT('HOUR' FROM policy_annual_start_date_local)  != 0
                THEN days_360( DATE_TRUNC('DAY', period_start_date_local ) , DATE_TRUNC('DAY', period_end_date_local)  )

            WHEN
                policy_annual_date_rule IN ('MONTHLY_BASE', 'MONTHLY_CANCELLED', 'MONTHLY_SUBSOUT')
                AND instalment = policy_min_instalment
                AND EXTRACT('DAY' FROM policy_annual_start_date_local) != 1
                THEN days_360( DATE_TRUNC('DAY', period_start_date_local ) + '-1 day' , DATE_TRUNC('DAY', period_end_date_local) + '1 day' )

            WHEN
                policy_annual_date_rule IN ('MONTHLY_BASE', 'MONTHLY_CANCELLED', 'MONTHLY_SUBSOUT')
                AND instalment = policy_min_instalment
                AND EXTRACT('DAY' FROM policy_annual_start_date_local) = 1
                THEN days_360( DATE_TRUNC('DAY', period_start_date_local ) + '-1 day' , DATE_TRUNC('DAY', period_end_date_local))

            WHEN
                policy_annual_date_rule IN
                ('MONTHLY_SUBSIN', 'SEMIANNUAL_SUBSIN', 'SEMIANNUAL_SUBSIN_CANCELLED', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED')
                AND instalment = policy_min_instalment
                THEN days_360( DATE_TRUNC('DAY', period_start_date_local ), period_end_date_local)

            WHEN
                policy_annual_date_rule IN
                ('MONTHLY_BASE', 'MONTHLY_SUBSIN', 'MONTHLY_CANCELLED', 'MONTHLY_SUBSOUT', 'MONTHLY_SUBSINOUT',
                 'MONTHLY_SUBSIN_CANCELLED')
                AND instalment > policy_min_instalment
                THEN days_360( DATE_TRUNC('DAY', period_start_date_local ), period_end_date_local)

        END                                                                                             AS audit_nb_of_days_period,
        CASE
            WHEN
                policy_annual_date_rule IN('MONTHLY_BASE', 'MONTHLY_SUBSIN')
                AND EXTRACT('HOUR' FROM policy_annual_start_date_local)  != 0
                THEN days_360( DATE_TRUNC('day', policy_annual_start_date_local at time zone 'Europe/Rome' at time zone 'utc') + '-1 day' , policy_annual_end_date_local)

            WHEN
                policy_annual_date_rule IN('MONTHLY_BASE', 'MONTHLY_SUBSIN')
                THEN days_360( DATE_TRUNC('day', policy_annual_start_date_local at time zone 'Europe/Rome' at time zone 'utc') , policy_annual_end_date_local)
        END                                                                                             AS audit_nb_of_days_year,
        CASE
            WHEN
                policy_annual_date_rule IN('MONTHLY_CANCELLED', 'MONTHLY_SUBSOUT', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED')
                AND instalment = policy_min_instalment
                THEN days_360( DATE_TRUNC('day', policy_annual_start_date_local at time zone 'Europe/Rome' at time zone 'utc') , policy_annual_renewal_date_local)

            WHEN
                policy_annual_date_rule IN('MONTHLY_CANCELLED', 'MONTHLY_SUBSOUT', 'SEMIANNUAL_CANCELLED', 'SEMIANNUAL_SUBSOUT', 'MONTHLY_SUBSIN_CANCELLED')
                AND instalment > policy_min_instalment
                AND nature_of_contract IN ('Interruzione - Mensile', 'Recesso - Mensile', 'Interruzione - Semestrale', 'Recesso - Semestrale', 'Interruzione forzata - Mensile', 'Interruzione forzata - Semestrale')
                THEN days_360( DATE_TRUNC('day', policy_annual_start_date_local at time zone 'Europe/Rome' at time zone 'utc') ,policy_annual_renewal_date_local)
        END                                                                                             AS audit_original_nb_of_days_year
    FROM substitution_fee
),
crodino_annual_dates_stg AS (
    SELECT
        *,
        CASE
            WHEN policy_annual_date_rule IN(
                'ANNUAL_BASE', 'ANNUAL_SUBSIN', 'ANNUAL_CANCELLED',
                'ANNUAL_SUBSOUT', 'ANNUAL_SUBSINOUT', 'ANNULLED_ANNUAL_BASE',
                'ANNULLED_ANNUAL_CANCELLED', 'FULLY_ANNULLED_ANNUAL_SUBSIN', 'ANNUAL_SUBSIN_CANCELLED',
                'ANNULLED_ANNUAL_SUBSIN', 'ANNULLED_ANNUAL_SUBSOUT', 'ANNULLED_ANNUAL_SUBSINOUT') THEN 1.00
            WHEN policy_annual_date_rule IN(
                    'SEMIANNUAL_BASE', 'SEMIANNUAL_SUBSIN'
                ) AND instalment = policy_min_instalment THEN 2.0
            WHEN policy_annual_date_rule IN(
                 'SEMIANNUAL_SUBSOUT', 'SEMIANNUAL_CANCELLED', 'SEMIANNUAL_SUBSINOUT', 'SEMIANNUAL_SUBSIN_CANCELLED'
                )
            AND nature_of_contract IN(
                'Emissione - Semestrale',
                'SosIN - Semestrale',
                'Appendice IN',
                'Appendice OUT'
                )
            AND instalment = policy_min_instalment THEN 2.0
            WHEN policy_annual_date_rule IN(
                    'MONTHLY_BASE', 'MONTHLY_SUBSIN'
                ) AND instalment = policy_min_instalment THEN
                CASE
                    WHEN audit_nb_of_days_period !=0 THEN audit_nb_of_days_year::NUMERIC/audit_nb_of_days_period
                END
            WHEN policy_annual_date_rule IN(
                'MONTHLY_BASE', 'MONTHLY_SUBSIN',
                'SEMIANNUAL_BASE', 'SEMIANNUAL_SUBSIN', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED',
                'SEMIANNUAL_SUBSINOUT'
                ) AND instalment <> policy_min_instalment THEN 0.00
            WHEN
                policy_annual_date_rule IN(
                    'MONTHLY_SUBSOUT', 'MONTHLY_CANCELLED', 'MONTHLY_SUBSIN_CANCELLED', 'MONTHLY_SUBSINOUT'
                )
                AND nature_of_contract IN(
                    'Emissione - Mensile',
                    'Appendice IN',
                    'Appendice OUT',
                    'SosIN - Mensile'
                    )
                AND instalment = policy_min_instalment
                THEN
                    CASE
                        WHEN audit_nb_of_days_period != 0 THEN audit_original_nb_of_days_year::NUMERIC / audit_nb_of_days_period
                    END
            WHEN
                policy_annual_date_rule IN(
                    'MONTHLY_SUBSOUT', 'MONTHLY_CANCELLED',
                    'SEMIANNUAL_SUBSOUT', 'SEMIANNUAL_CANCELLED'
                )
                AND nature_of_contract IN(
                    'Emissione - Mensile',
                    'Emissione - Semestrale',
                    'Appendice IN', 'Appendice OUT')
                AND instalment > policy_min_instalment
                THEN 0.00
        END                                                                                             AS annual_scaling_factor
    FROM crodino_annual_dates_nb_months
),

crodino_annualized_amounts AS (
    SELECT
        *,
        -- calculated substitution fee = substitution_fee/(1 + ipt_rate)
        (gross_written_premium - (substitution_fee/(1 + ipt_rate))) *
            annual_scaling_factor + (substitution_fee/(1 + ipt_rate))                                   AS tmp_annualized_gross_written_premium,
        (ipt - (substitution_fee - substitution_fee/(1 + ipt_rate))) *
            annual_scaling_factor + (substitution_fee - substitution_fee/(1 + ipt_rate))                AS tmp_annualized_ipt,
        ROW_NUMBER () OVER (
            PARTITION BY agent_policy_number, cover, class_of_business
            ORDER BY
                instalment,
                CASE
                    WHEN nature_of_contract LIKE 'Emissione%' THEN 1
                    WHEN nature_of_contract LIKE 'Appendice IN' THEN 2
                    WHEN nature_of_contract LIKE 'SosIN%' THEN 3
                    WHEN nature_of_contract LIKE 'Appendice OUT' THEN 4
                    ELSE 5
                END ASC
        )   AS rn
    FROM crodino_annual_dates_stg
),
crodino_cancelations_amount AS (
    SELECT
        a.*,
        CASE
            WHEN
                a.policy_annual_date_rule IN(
                    'MONTHLY_SUBSOUT', 'MONTHLY_CANCELLED',
                    'SEMIANNUAL_SUBSOUT', 'SEMIANNUAL_CANCELLED', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED',
                    'SEMIANNUAL_SUBSINOUT', 'SEMIANNUAL_SUBSIN_CANCELLED'
                )
                AND a.rn_nature_of_contract = 1
                AND a.nature_of_contract IN (
                    'SosOUT - Mensile', 'SosOUT - Semestrale',
                    'Interruzione - Mensile', 'Interruzione - Semestrale',
                    'Recesso - Semestrale', 'Recesso - Mensile', 'Interruzione SosIN - Mensile','Interruzione forzata - Semestrale',
                    'Interruzione forzata - Mensile', 'Interruzione SosIN - Semestrale'
                )
                THEN  - (SUM(a.tmp_annualized_gross_written_premium) OVER (
                            PARTITION BY a.agent_policy_number, a.policy_annual_date_rule, a.cover, a.class_of_business
                            ORDER BY a.rn
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        )  - (
                    -- what has already been "booked" ie sum of previous instalment gross premiums
                    -- and this specific transaction
                        SUM(a.gross_written_premium) OVER (
                            PARTITION BY a.agent_policy_number, a.policy_annual_date_rule, a.cover, a.class_of_business
                            ORDER BY a.rn
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        )
                    )
                )
            WHEN
                a.policy_annual_date_rule IN(
                    'MONTHLY_SUBSOUT', 'MONTHLY_CANCELLED',
                    'SEMIANNUAL_SUBSOUT', 'SEMIANNUAL_CANCELLED', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED',
                    'SEMIANNUAL_SUBSINOUT', 'SEMIANNUAL_SUBSIN_CANCELLED'
                )
                AND a.rn_nature_of_contract != 1
                AND a.nature_of_contract IN (
                    'SosOUT - Mensile', 'SosOUT - Semestrale',
                    'Interruzione - Mensile', 'Interruzione - Semestrale',
                    'Recesso - Semestrale', 'Recesso - Mensile', 'Interruzione SosIN - Mensile','Interruzione forzata - Semestrale',
                    'Interruzione forzata - Mensile', 'Interruzione SosIN - Semestrale'
                )
                THEN 0
            ELSE a.tmp_annualized_gross_written_premium
        END::NUMERIC AS annualized_gross_written_premium,
        CASE
            WHEN
                a.policy_annual_date_rule IN(
                    'MONTHLY_SUBSOUT', 'MONTHLY_CANCELLED',
                    'SEMIANNUAL_SUBSOUT', 'SEMIANNUAL_CANCELLED', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED',
                    'SEMIANNUAL_SUBSINOUT', 'SEMIANNUAL_SUBSIN_CANCELLED'
                )
                AND a.rn_nature_of_contract = 1
                AND a.nature_of_contract IN (
                    'SosOUT - Mensile', 'SosOUT - Semestrale',
                    'Interruzione - Mensile', 'Interruzione - Semestrale',
                    'Recesso - Semestrale', 'Recesso - Mensile', 'Interruzione SosIN - Mensile','Interruzione forzata - Semestrale',
                    'Interruzione forzata - Mensile', 'Interruzione SosIN - Semestrale'
                )
                THEN  - (SUM(a.tmp_annualized_ipt) OVER (
                            PARTITION BY a.agent_policy_number, a.policy_annual_date_rule, a.cover, a.class_of_business
                            ORDER BY a.rn
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        )  - (
                    -- what has already been "booked" ie sum of previous instalment ipt
                    -- and this specific transaction
                        SUM(a.ipt) OVER (
                            PARTITION BY a.agent_policy_number, a.policy_annual_date_rule, a.cover, a.class_of_business
                            ORDER BY a.rn
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        )
                    )
                )
            WHEN
                a.policy_annual_date_rule IN(
                    'MONTHLY_SUBSOUT', 'MONTHLY_CANCELLED',
                    'SEMIANNUAL_SUBSOUT', 'SEMIANNUAL_CANCELLED', 'MONTHLY_SUBSINOUT', 'MONTHLY_SUBSIN_CANCELLED',
                    'SEMIANNUAL_SUBSINOUT', 'SEMIANNUAL_SUBSIN_CANCELLED'
                )
                AND a.rn_nature_of_contract != 1
                AND a.nature_of_contract IN (
                    'SosOUT - Mensile', 'SosOUT - Semestrale',
                    'Interruzione - Mensile', 'Interruzione - Semestrale',
                    'Recesso - Semestrale', 'Recesso - Mensile', 'Interruzione SosIN - Mensile','Interruzione forzata - Semestrale',
                    'Interruzione forzata - Mensile', 'Interruzione SosIN - Semestrale'
                )
                THEN 0
            ELSE a.tmp_annualized_ipt
        END::NUMERIC AS annualized_ipt
    FROM crodino_annualized_amounts a
)

SELECT * FROM crodino_cancelations_amount
