{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"],
    )
}}


SELECT
    p.id,
    p.agent_policy_number,
    p.cover,
    p.class_of_business,
    p.plate_number,
    p.codice_fiscale,
    p.post_code,
    p.instalment,
    p.nature_of_contract,
    p.transaction_type,
    p.payment_frequency,
    p.annulment,
    p.annulled,
    p.issue_date_local,
    p.period_start_date_local,
    p.period_end_date_local,
    p.annual_gross_written_premium,
    p.annual_ipt,
    p.gross_written_premium,
    p.ipt,
    p.ipt_rate,
    p.policy_substitution_fee,

    pd.policy_min_instalment,
    pd.policy_annual_date_rule,
    pd.policy_annual_start_date_local,
    pd.policy_annual_end_date_local,
    pd.policy_annual_renewal_date_local,

    COALESCE(op.parent_code, p.agent_policy_number)            AS parent_code,
    COALESCE(op.original_policy_number, p.agent_policy_number) AS original_policy_number,
    COALESCE(op.policy_path, p.agent_policy_number)            AS policy_path,
    COALESCE(op.policy_depth, 0)                               AS policy_depth,
    COALESCE(
        op.original_policy_annual_start_date_local,
        pd.policy_annual_start_date_local
    )                                                          AS original_policy_annual_start_date_local,
    COALESCE(
        op.original_policy_annual_end_date_local,
        pd.policy_annual_end_date_local
    )                                                          AS original_policy_annual_end_date_local,
    COALESCE(
        op.original_policy_annual_renewal_date_local,
        pd.policy_annual_renewal_date_local
    )                                                          AS original_policy_annual_renewal_date_local,

    ROW_NUMBER() OVER (
        PARTITION BY
            p.agent_policy_number,
            p.nature_of_contract,
            p.cover,
            p.class_of_business
        ORDER BY
            p.instalment,
            p.transaction_iscurrent,
            p.dbt_valid_to DESC NULLS FIRST
    )                                                          AS rn_nature_of_contract,

    p.dbt_valid_from,
    p.dbt_valid_to
FROM {{ test_utils.testable_ref('stg_crodino_portfolio') }} p
LEFT JOIN {{ test_utils.testable_ref('stg_crodino_portfolio_annual_dates') }} pd
    ON p.agent_policy_number = pd.agent_policy_number
LEFT JOIN {{ test_utils.testable_ref('stg_original_policies_dates_date_policy_extracts') }} op
    ON p.agent_policy_number = op.agent_policy_number
