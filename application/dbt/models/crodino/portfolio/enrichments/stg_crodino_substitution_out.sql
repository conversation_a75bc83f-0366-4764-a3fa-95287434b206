{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"]
    )
}}

SELECT
    agent_policy_number,
    ROW_NUMBER() OVER (
        PARTITION BY
            agent_policy_number
        ORDER BY
            issue_date_local,
            nature_of_contract
    )                                                               AS substitution_number,
    issue_date_local,
    nature_of_contract,
    STRING_AGG(DISTINCT payment_frequency, ',' )                    AS payment_frequency,
    STRING_AGG(DISTINCT instalment::VARCHAR, ',')                   AS instalment,
    MIN(period_start_date_local)                                    AS period_start_date_local,
    MAX(period_end_date_local)                                      AS period_end_date_local,
    STRING_AGG(DISTINCT plate_number, ',')                          AS plate_number,
    STRING_AGG(DISTINCT codice_fiscale, ',')                        AS codice_fiscale,
    STRING_AGG(DISTINCT post_code, ',')                             AS post_code,
    ARRAY_AGG(DISTINCT cover ORDER BY cover)                        AS covers,
    SUM(gross_written_premium) FILTER (WHERE cover = 'rca')         AS rca_premium,
    SUM(annual_gross_written_premium) FILTER (WHERE cover = 'rca')  AS rca_annual_premium
FROM {{ ref('stg_original_policies_dates') }}
WHERE transaction_type = 'Substitution Out'
GROUP BY
    agent_policy_number,
    nature_of_contract,
    issue_date_local
