{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"],
    )
}}

SELECT
    op.agent_policy_number,
    op.parent_code,
    op.original_policy_number,
    op.policy_path,
    op.policy_depth,
    pd.policy_annual_start_date_local           AS original_policy_annual_start_date_local,
    pd.policy_annual_end_date_local             AS original_policy_annual_end_date_local,
    pd.policy_annual_renewal_date_local         AS original_policy_annual_renewal_date_local
FROM {{ test_utils.testable_ref('crodino_policy_lifecycle') }} op
LEFT JOIN {{ test_utils.testable_ref('stg_crodino_portfolio_annual_dates') }} pd
    ON pd.agent_policy_number = op.original_policy_number
