WITH
individuals AS (
    SELECT
        codice_fiscale,
        MIN(period_start_date_local::DATE) AS policy_start_date,
        SUBSTRING(codice_fiscale, 7, 2)    AS year_of_birth,
        SUBSTRING(codice_fiscale, 9, 1)    AS month_of_birth,
        SUBSTRING(codice_fiscale, 10, 2)   AS day_of_birth,
        SUBSTRING(codice_fiscale, 12, 4)   AS place_of_birth,
        MIN(created_on)                    AS created_on
    FROM {{ test_utils.testable_ref('stg_crodino_portfolio') }}
    WHERE
    /** Fiscal codes for individuals are always 16 characters long **/
        LENGTH(codice_fiscale) = 16
    GROUP BY codice_fiscale
)

SELECT
    codice_fiscale,
    policy_start_date,
    MAKE_DATE(
        year_of_birth::INT +
        CASE
            WHEN year_of_birth > '10'
                THEN 1900
                ELSE 2000
        END,
        CASE month_of_birth
            WHEN 'A' THEN 01
            WHEN 'B' THEN 02
            WHEN 'C' THEN 03
            WHEN 'D' THEN 04
            WHEN 'E' THEN 05
            WHEN 'H' THEN 06
            WHEN 'L' THEN 07
            WHEN 'M' THEN 08
            WHEN 'P' THEN 09
            WHEN 'R' THEN 10
            WHEN 'S' THEN 11
            WHEN 'T' THEN 12
        END,
        CASE
            WHEN day_of_birth > '40'
                THEN day_of_birth::INT - 40
                ELSE day_of_birth::INT
        END
    )                                  AS date_of_birth,
    CASE
        WHEN day_of_birth > '40'
            THEN 'F'
            ELSE 'M'
    END                                AS gender,
    place_of_birth,
    created_on
FROM individuals
