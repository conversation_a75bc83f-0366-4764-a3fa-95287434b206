{{
    config(
        materialized="table",
        tags=["model-crodino-portfolio"]
    )
}}

SELECT
    parent_code,
    agent_policy_number,
    ROW_NUMBER() OVER (
        PARTITION BY
            parent_code
        ORDER BY
            issue_date_local
    )                                                               AS substitution_number,
    MIN(original_policy_annual_start_date_local)                    AS original_policy_annual_start_date_local,
    issue_date_local,
    STRING_AGG(DISTINCT nature_of_contract, ',')                    AS nature_of_contract,
    STRING_AGG(DISTINCT payment_frequency, ',' )                    AS payment_frequency,
    STRING_AGG(DISTINCT instalment::VARCHAR, ',')                   AS instalment,
    MIN(period_start_date_local)                                    AS period_start_date_local,
    MAX(period_end_date_local)                                      AS period_end_date_local,
    STRING_AGG(DISTINCT plate_number, ',')                          AS plate_number,
    STRING_AGG(DISTINCT codice_fiscale, ',')                        AS codice_fiscale,
    STRING_AGG(DISTINCT post_code, ',')                             AS post_code,
    ARRAY_AGG(DISTINCT cover ORDER BY cover)                        AS covers,
    SUM(policy_substitution_fee) FILTER (WHERE cover = 'rca')       AS rca_substitution_fee,
    SUM(gross_written_premium) FILTER (WHERE cover = 'rca')         AS rca_premium,
    SUM(annual_gross_written_premium) FILTER (WHERE cover = 'rca')  AS rca_annual_premium,
    CASE
        WHEN annulment
        THEN -1
        ELSE +1
    END                                                             AS sign
FROM {{ ref('stg_original_policies_dates') }}
WHERE transaction_type = 'Substitution In'
AND instalment = policy_min_instalment
GROUP BY
    parent_code,
    agent_policy_number,
    annulment,
    issue_date_local
