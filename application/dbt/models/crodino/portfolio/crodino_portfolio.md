{% docs crodino_portfolio %}
# Crodino Portfolio

## Transformations

### Local dates

The data we recieve from Crodino/Prima contains dates in UTC.
We add new columns that contain the dates in their local times (i.e. Rome timezone).

### Date of Birth

The date of birth is extracted based on the Codice Fiscale.
The column contains LE - when the codice fiscale is related to a legal entity and not a real person.
The transformation makes the assumption that the person is born in the year 19XX if his age at the start of the policy is greater than 14 (minimum required to drive a motorbike in Italy) and less than 114.

### Annual Policy Dates

We need to calculate the annual policy dates for each transaction item.
The dates are based on period_start_date_local and period_end_date_local for each transaction, consolidated at policy level.

The following rules are being applied.

* Policy Start date - Is the minimum period start date of the entire policy for transaction adding a cover (Emissione, Appendix, Substitution in)
* Policy End date -The calculation of the policy end date is can be a bit complex
  - Annual Policies: The base case for annual policies that don't have some sort of cancelation (cancellation, interuption, substitution out) is to take the policy_end_date as provided in the period_end_date_local
  - Monthly Policies: The base case for monthly behaves in such a way that it includes "bridging days" till the 1st day of the month, and carries on thereafter for 12 more months. That is if we have a policy starting in June 6th 2020, there will be bridging days till the 1st of July 2020 and the policy will end 12 months after on the 30th of June.
  - In case of withdrawal, interruption or substitution, the period_start /end dates of the transaction represent the period where the original policy becomes "inactive". We need to leverage the period_start date of these transactions to obtain the policy end date.


#### Known Issues

- We are not currently taking into account policy renewals
- We are not currently taking into account pause/resume of policies
- We are most likely not handling the annualization for dates ending in February
- Not every case is currently taken into account


### Annual Premium Data

We need to recognize premiums based on their "Annual value". This is not a straight forward "Annualization" process as it would cover the bridging days discussed in the annual policy dates transformation.
The premiums need to be rescaled based on a annual_scaling factor.
The following metrics are "annualized" in this way:
* Gross Written Premium
* Insurance Premium Tax
* Total Underwritten

The way the scaling factor is obtained is the following:
* Annual premiums
   - In case there is no substitution, withdrawal, etc.. it is quite straightforward to compute as the scaling factor is taken as 1
   - In case the policy contains substitutions, withdrawal, ?? what to do ??
* Monthly premiums
  - Only the first installment is considered for annual premium revenue recognition recognition, other installment number have a scaling factor of 0
  - In case there is no substitution, widthdrawl etc... The scaling factor is obtained by calculating the number of months in the transaction and in the "annual policy year" (obtained in the annual policy dates enrichment)
   - In case the policy contains substitutions, withdrawal, ?? what to do ??



#### In the event of Substition IN
When a policies get substituted, Prima adds a surcharge of 15euro when no new covers gets added to the policies.

#### In the event of cancelation / substitution out



{% enddocs %}
