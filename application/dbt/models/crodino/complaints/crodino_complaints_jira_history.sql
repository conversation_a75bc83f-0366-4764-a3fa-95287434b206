{{
    config(
        materialized='table',
        tags=['complaints']
    )
}}

WITH
base_deduplicated AS (
    SELECT
        chksum,
        jira_ticket,
        subject,
        field_name,
        field_value,
        created_at::TIMESTAMP                                               AS jira_ticket_created_at,
        updated_at::TIMESTAMP                                               AS jira_field_updated_at,
        created_on::TIMESTAMP                                               AS bdx_created_on,
        -- Same Jira field update may appear in multiple bdx - we want to keep only records from the latest bdx
        ROW_NUMBER() OVER w = 1                                             AS is_latest_among_duplicates
    FROM {{ source('mgalanding', 'prima_complaints_jira') }}
    WINDOW w AS (PARTITION BY jira_ticket, field_name, field_value, updated_at ORDER BY created_on DESC)
),

base_with_current_flag AS (
    SELECT
        chksum,
        jira_ticket,
        subject,
        field_name,
        field_value,
        jira_ticket_created_at,
        jira_field_updated_at,
        bdx_created_on,
        -- Jira fields can vary multiple times within a bdx, we want to keep only the latest
        ROW_NUMBER() OVER w = 1                                             AS is_latest_in_bdx
    FROM base_deduplicated
    WHERE
        is_latest_among_duplicates
    WINDOW w AS (PARTITION BY chksum, jira_ticket, field_name ORDER BY jira_field_updated_at DESC)
),

with_latest_in_bdx AS (
    SELECT
        chksum,
        jira_ticket,
        subject,
        field_name,
        field_value,
        jira_ticket_created_at,
        bdx_created_on
    FROM base_with_current_flag
    WHERE
        is_latest_in_bdx
),

-- Count the number of times Jira tickets have been reopened across all bdx
with_previous_status AS (
    SELECT
        jira_ticket,
        field_value                                                                             AS status,
        LAG(field_value) OVER (PARTITION BY jira_ticket ORDER BY jira_field_updated_at ASC)     AS previous_status
    FROM base_deduplicated
    WHERE 1=1
        AND field_name = 'status'
        AND is_latest_among_duplicates
    ORDER BY jira_ticket, jira_field_updated_at
),

with_reopened_flag AS (
    SELECT
        jira_ticket,
        status,
        CASE
            WHEN status = 'In Lavorazione' AND previous_status IN ('Respinto', 'Transatto', 'Accettato') THEN TRUE
        END                                                                                     AS is_reopened
    FROM with_previous_status
),

mapping_jiraticket_count_reopened AS (
    SELECT
        jira_ticket,
        COUNT(*) FILTER (WHERE is_reopened)                                             AS count_reopened
    FROM with_reopened_flag
    GROUP BY jira_ticket
),

-- Field 'Area aziendale' is composed of two parts, each of which should be moved to separate records.
-- The following CTEs create a subset of records having field_name 'Area aziendale', expand them into two rows based on
-- a regexp, assigns them new field names (sector and subsector) and finally union the result with the
-- original dataset where records with field_name 'Area aziendale' have been excluded.
subset_expanded_area_aziendale AS (
    SELECT
        chksum,
        jira_ticket,
        subject,
        field_name,
        UNNEST(regexp_matches(field_value, '(Parent values: .+?)(Level 1 values: .+)?$', 'g')) AS field_value,
        jira_ticket_created_at,
        bdx_created_on
    FROM with_latest_in_bdx
    WHERE
        field_name = 'Area aziendale'
),

subset_expanded_area_aziendale_refined AS (
    SELECT
        chksum,
        jira_ticket,
        subject,
        CASE
            WHEN field_value ~ 'Parent values:*' THEN 'Area aziendale'
            WHEN field_value ~ 'Level 1 values:*' THEN 'Sub area aziendale'
        END AS field_name,
        (array_remove(regexp_matches(field_value, 'Parent values: (.+)|Level 1 values: (.+)', 'g'), NULL))[1] AS field_value,
        jira_ticket_created_at,
        bdx_created_on
    FROM subset_expanded_area_aziendale
),

finalized_keyval_dataset AS (
    SELECT
        *
    FROM with_latest_in_bdx
    WHERE
        field_name <> 'Area aziendale'

    UNION ALL

    SELECT
        *
    FROM subset_expanded_area_aziendale_refined
),
-- Mapping table to translate dates in format day/month_prefix/year into date types
month_translation AS (
    SELECT * FROM (VALUES
        ('gen', '01'),
        ('jan', '01'),
        ('feb', '02'),
        ('mar', '03'),
        ('apr', '04'),
        ('mag', '05'),
        ('may', '05'),
        ('giu', '06'),
        ('jun', '06'),
        ('lug', '07'),
        ('jul', '07'),
        ('ago', '08'),
        ('aug', '08'),
        ('set', '09'),
        ('sep', '09'),
        ('ott', '10'),
        ('oct', '10'),
        ('nov', '11'),
        ('dic', '12'),
        ('dec', '12')
    ) AS mt(prefix_month, month_number)
),

unpivoted AS (
    SELECT
        jira_ticket,
        chksum,
        jira_ticket_created_at,
        bdx_created_on,
        subject,
        MAX(CASE WHEN field_name = 'Linea di business' THEN field_value END)                            AS lob,
        MAX(CASE WHEN field_name = 'Numerazione comunicazione' THEN field_value END)                    AS issue_number,
        MAX(CASE WHEN field_name = 'Reclamante: nome cognome o ragione sociale' THEN field_value END)   AS complainer_name,
        MAX(CASE WHEN field_name = 'Reclamante: zona geografica' THEN field_value END)                  AS complainer_geographic_area,
        MAX(CASE WHEN field_name = 'Reclamante: Indirizzo' THEN field_value END)                        AS complainer_address,
        MAX(CASE WHEN field_name = 'Tipologia reclamante' THEN field_value END)                         AS complainer_type,
        MAX(CASE WHEN field_name = 'Proponente: nome cognome o ragione sociale' THEN field_value END)   AS proponent_name,
        MAX(CASE WHEN field_name = 'Proponente: Indirizzo' THEN field_value END)                        AS proponent_address,
        MAX(CASE WHEN field_name = 'Tipologia proponente' THEN field_value END)                         AS proponent_type,
        MAX(
        CASE
            WHEN field_name = 'Data di chiusura del reclamo'
                THEN DATE(regexp_replace(LOWER(field_value),
                                         '(\d{1,2})/(\w+)/(\d{1,2})', '20\3 ' || mt.month_number || ' \1'))
        END)                                                                                            AS closure_date,
        MAX(
        CASE
            WHEN field_name = 'Data di ricezione del reclamo'
            THEN DATE(regexp_replace(LOWER(field_value),
                                     '(\d{1,2})/(\w+)/(\d{1,2})', '20\3 ' || mt.month_number || ' \1'))
        END)                                                                                            AS reception_date,
        MAX(CASE WHEN field_name = 'Area aziendale' THEN field_value END)                               AS sector,
        MAX(CASE WHEN field_name = 'Sub area aziendale' THEN field_value END)                           AS subsector,
        MAX(CASE WHEN field_name = 'Valore economico (Euro)' THEN field_value END)                      AS monetary_value,
        MAX(CASE WHEN field_name = 'Richiesta IVASS' THEN field_value END)                              AS handled_by_ivass,
        MAX(CASE WHEN field_name = 'Numero di protocollo esterno' THEN field_value END)                 AS external_protocol_number,
        MAX(CASE WHEN field_name = 'Tipologia di prodotto' THEN field_value END)                        AS product_type,
        MAX(CASE WHEN field_name = 'Inviato al giudice' THEN field_value END)                           AS transferred_judicial_authority,
        MAX(CASE WHEN field_name = 'Responsabile del Reclamo' THEN field_value END)                     AS complaint_manager,
        MAX(CASE WHEN field_name = 'status' THEN field_value END)                                       AS status
    FROM finalized_keyval_dataset
    LEFT JOIN month_translation mt
      ON field_value ILIKE '%/' || mt.prefix_month || '/%'
    GROUP BY jira_ticket, bdx_created_on, jira_ticket_created_at, subject, chksum
),
-- The function find_last_ignore_nulls() ensures that successive updates of a jira ticket
-- are filled with the previous non-null value. This way, by fetching the latest update, we carry over all past updates
keep_last_value AS (
    SELECT
        jira_ticket,
        chksum,
        jira_ticket_created_at,
        bdx_created_on,
        subject,
        lag_ignore_nulls(lob) over w                                            AS lob,
        lag_ignore_nulls(issue_number) over w                                   AS issue_number,
        lag_ignore_nulls(complainer_name) over w                                AS complainer_name,
        lag_ignore_nulls(complainer_geographic_area) over w                     AS complainer_geographic_area,
        lag_ignore_nulls(complainer_address) over w                             AS complainer_address,
        lag_ignore_nulls(complainer_type) over w                                AS complainer_type,
        lag_ignore_nulls(proponent_name) over w                                 AS proponent_name,
        lag_ignore_nulls(proponent_address) over w                              AS proponent_address,
        lag_ignore_nulls(proponent_type) over w                                 AS proponent_type,
        lag_ignore_nulls(closure_date) over w                                   AS closure_date,
        lag_ignore_nulls(sector) over w                                         AS sector,
        lag_ignore_nulls(subsector) over w                                      AS subsector,
        lag_ignore_nulls(monetary_value) over w                                 AS monetary_value,
        lag_ignore_nulls(handled_by_ivass) over w                               AS handled_by_ivass,
        lag_ignore_nulls(external_protocol_number) over w                       AS external_protocol_number,
        lag_ignore_nulls(product_type) over w                                   AS product_type,
        lag_ignore_nulls(transferred_judicial_authority) over w                 AS transferred_judicial_authority,
        lag_ignore_nulls(complaint_manager) over w                              AS complaint_manager,
        lag_ignore_nulls(status) over w                                         AS status,
        lag_ignore_nulls(reception_date) over w                                 AS reception_date
    FROM unpivoted
    WINDOW w AS (PARTITION BY jira_ticket ORDER BY bdx_created_on)
    ORDER BY bdx_created_on
)
SELECT
    NULL::INTEGER                                                               AS id,
    cf.distribution_partner                                                     AS distribution_partner,
    CASE
        WHEN uh.lob = 'Motor' THEN 'PMOTOR'
        WHEN uh.lob = 'Household' THEN 'PHOUSE'
    END                                                                         AS product_id,
    cf.branch                                                                   AS branch,
    cf.country                                                                  AS country,
    'Jira'                                                                      AS complaint_type,
    uh.jira_ticket                                                              AS complaint_reference,
    NULL::INTEGER                                                               AS progressive_number,
    uh.issue_number                                                             AS issue_number,
    EXTRACT(YEAR FROM uh.jira_ticket_created_at)                                AS creation_year,
    NULL                                                                        AS complaint_code,
    NULL                                                                        AS complaint_handling_code,
    uh.jira_ticket_created_at                                                   AS reception_date,
    LOWER(uh.product_type)                                                      AS product_type,
    CONCAT_WS('-', uh.sector, uh.subsector)                                     AS sector,
    uh.proponent_name                                                           AS name_policyholder,
    uh.proponent_address                                                        AS address_policyholder,
    uh.proponent_type                                                           AS type_policyholder,
    NULL                                                                        AS channel,
    uh.complainer_geographic_area                                               AS complainer_geographic_area,
    uh.complainer_name                                                          AS name_complainer,
    uh.complainer_address                                                       AS address_complainer,
    uh.complainer_type                                                          AS type_complainer,
    -- The closure date is used by the complaints report to indicate the date when a complaint was closed.
    -- However, closure date in Jira refers to the date an iteration of the complaint was closed, as a Jira ticket can be reopened.
    -- When a Jira ticket is still in progress, closure date is NULL.
    CASE
        WHEN uh.status NOT IN ('Respinto', 'Transatto', 'Accettato') THEN NULL
        ELSE uh.closure_date::TIMESTAMP
    END                                                                         AS closure_date,
    TRIM(LOWER(uh.status))                                                      AS result,
    uh.transferred_judicial_authority                                           AS judicial_authority_intervention,
    uh.monetary_value                                                           AS monetary_value,
    CASE
        WHEN uh.closure_date IS NOT NULL AND uh.status in ('Respinto', 'Transatto', 'Accettato', 'Non Trattabile')
            THEN (uh.closure_date - uh.jira_ticket_created_at)::TEXT
    END                                                                         AS duration_till_closure,
    uh.subject                                                                  AS complaint_subject,
    uh.handled_by_ivass                                                         AS handled_by_ivass,
    uh.external_protocol_number                                                 AS ref_complaint_ivass,
    NULL                                                                        AS ref_claim,
    NULL                                                                        AS ref_policy,
    NULL                                                                        AS response,
    uh.transferred_judicial_authority                                           AS transferred_judicial_authority,
    (cr.count_reopened > 0)::TEXT                                               AS reopened,
    LOWER(uh.sector)                                                            AS complaint_cause,
    NULL                                                                        AS elapsed_days,
    uh.chksum                                                                   AS chksum,
    uh.bdx_created_on                                                           AS created_on,
    {{ dbt_utils.generate_surrogate_key(['uh.chksum','uh.jira_ticket']) }}      AS dbt_scd_id,
    uh.bdx_created_on                                                           AS dbt_updated_at,
    uh.bdx_created_on                                                           AS dbt_valid_from,
    LEAD(uh.bdx_created_on) OVER (PARTITION BY uh.jira_ticket ORDER BY uh.bdx_created_on) AS dbt_valid_to
FROM keep_last_value uh
LEFT JOIN mapping_jiraticket_count_reopened cr
    USING (jira_ticket)
LEFT JOIN {{ ref('crodino_files') }} cf
    USING (chksum)
WHERE
    cf.bordereau_type = 'Complaints Jira'
