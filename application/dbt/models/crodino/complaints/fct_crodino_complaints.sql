{{
    config(
      schema='crodino',
      materialized='table',
      tags=['complaints'],
      post_hook=[
        "DROP TRIGGER IF EXISTS {{this.name}}_audit ON  {{this.schema}}.{{this.name}}",
        "CREATE TRIGGER {{this.name}}_audit AFTER INSERT OR UPDATE OR DELETE ON {{this.schema}}.{{this.name}} FOR EACH ROW EXECUTE PROCEDURE audit.if_modified_func();"
        ]
    )
}}

WITH
base AS (
    SELECT
        *
    FROM {{ ref('crodino_complaints_history') }}

    UNION ALL

    SELECT
        *
    FROM {{ ref('crodino_complaints_jira_history') }}
    WHERE
        result NOT IN ('non trattabile', 'proposto non trattabile')

    UNION ALL

    SELECT
        *
    FROM {{ ref('crodino_spain_complaints_history') }}
),

claims AS (
    SELECT DISTINCT
        product_id,
        CASE
            WHEN SUBSTR(claim_id,1,2) = 'DN' THEN REGEXP_REPLACE(claim_id, '[A-Z]+$', '')
            ELSE claim_id
        END                             AS claim_id
    FROM {{ ref('crodino_claims') }}
    WHERE
        --Only Prima IT products because Prima ES has only one product_id, so we infer it in stg_crodino_complaints
        product_id IN ('PMOTOR', 'PHOUSE', 'PFAMILY')
)

SELECT
    ba.id,
    ba.distribution_partner,
    COALESCE(ba.product_id, cl.product_id)                   AS product_id,
    CASE ba.branch
        WHEN 'IT' THEN 'Italy'
        WHEN 'DE' THEN 'Germany'
    END                                                      AS branch,
    ba.country,
    ba.complaint_type,
    ba.complaint_reference,
    ba.progressive_number,
    ba.issue_number,
    ba.creation_year,
    ba.complaint_code,
    ba.complaint_handling_code,
    ba.reception_date,
    LOWER(ba.product_type)                                      AS product_type,
    ba.sector,
    ba.name_policyholder,
    ba.address_policyholder,
    ba.type_policyholder,
    ba.geographic_area_policyholder,
    ba.name_complainer,
    ba.address_complainer,
    ba.type_complainer,
    ba.closure_date,
    ba.result                                                AS processing_result,
    CASE
        WHEN TRIM(LOWER(ba.judicial_authority_intervention)) = 'si' THEN TRUE
        WHEN TRIM(LOWER(ba.judicial_authority_intervention)) = 'no' THEN FALSE
        ELSE FALSE
    END                                                      AS judicial_authority_intervention,
    ba.monetary_value,
    ba.duration_till_closure,
    ba.complaint_subject,
    ba.complaint_cause,
    cca.caa_cause                                               AS per_cause,
    CASE
        WHEN ba.result IS NULL THEN 'pending'
        ELSE cr.is_justified
    END                                                         AS is_justified,
    ic.caa_insurance_class                                      AS per_insurance_class,
    CASE
        WHEN TRIM(LOWER(ba.handled_by_ivass)) = 'si' THEN TRUE
        WHEN TRIM(LOWER(ba.handled_by_ivass)) = 'no' THEN FALSE
        ELSE FALSE
    END                                                         AS handled_by_ivass,
    ba.ref_complaint_ivass,
    ba.ref_claim,
    ba.response,
    CASE
        WHEN TRIM(LOWER(ba.transferred_judicial_authority)) = 'si' THEN TRUE
        WHEN TRIM(LOWER(ba.transferred_judicial_authority)) = 'no' THEN FALSE
        ELSE FALSE -- as confirmed by Prima Spain, by default this fields is False unless otherwise specified
    END                                                      AS transferred_judicial_authority,
    CASE
        WHEN TRIM(LOWER(ba.reopened)) = 'si' THEN TRUE
        WHEN TRIM(LOWER(ba.reopened)) = 'no' THEN FALSE
        ELSE FALSE
    END                                                      AS reopened,
    ba.chksum,
    ba.created_on,
    ba.dbt_scd_id,
    ba.dbt_updated_at,
    ba.dbt_valid_from,
    ba.dbt_valid_to,
    ba.dbt_valid_to IS NULL                                  AS transaction_iscurrent
FROM base ba
LEFT JOIN {{ ref('complaint_cause') }} cca
    ON LOWER(ba.complaint_cause) = LOWER(cca.complaint_cause)
    AND ba.country = cca.country
LEFT JOIN {{ ref('insurance_class') }} ic
    ON LOWER(ba.product_type) = ic.product_type
    AND ba.country = ic.country
LEFT JOIN {{ ref('complaint_result') }} cr
    ON ba.result = cr.processing_result
    AND ba.country = cr.country
LEFT JOIN claims cl
    ON ba.ref_claim = cl.claim_id
WHERE
    ba.dbt_valid_to IS NULL
