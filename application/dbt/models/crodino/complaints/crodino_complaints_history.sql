{{
    config(
        materialized='table',
        tags=['complaints']
    )
}}
WITH
complaint_files AS (
    SELECT
        distribution_partner,
        chksum,
        branch,
        country,
        bordereau_start_date,
        LEAD(created_on) OVER (
            PARTITION BY
                branch,
                EXTRACT(YEAR FROM bordereau_start_date)
            ORDER BY
                bordereau_start_date, created_on
            )                                                       AS next_created_on
    FROM {{ ref('crodino_files') }}
    WHERE
        bordereau_type = 'Complaints'
        AND country = 'Italy'
        AND source_date IS NOT NULL
)

SELECT
    pc.id,
    cf.distribution_partner,
    CASE
        WHEN pc.product = 'Motor' THEN 'PMOTOR'
        WHEN pc.product = 'No Motor' THEN 'PHOUSE'
        WHEN cf.branch = 'DE' THEN 'PMOTOR'
    END                                                             AS product_id,
    cf.branch,
    cf.country,
    'Reclami'                                                       AS complaint_type,
    CONCAT_WS('/', pc.complaint_code, cf.branch)                    AS complaint_reference,
    pc.progressive_number,
    pc.issue_number,
    pc.creation_year,
    pc.complaint_code,
    pc.complaint_handling_code,
    TO_TIMESTAMP(pc.reception_date, 'YYYY-MM-DD HH24:MI:SS')    AS reception_date,
    pc.product_type,
    pc.sector,
    pc.name_policyholder,
    pc.address_policyholder,
    pc.type_policyholder,
    NULL                                                            AS channel,
    pc.geographic_area_policyholder,
    pc.name_complainer,
    pc.address_complainer,
    pc.type_complainer,
    -- Unrecoverable missing closing date from partner BDA-11963:
    -- when the closing date is missing or is not in a date format, but the processing result is available
    -- the closing date is reception_date (only when it's a date) + 30 days
    CASE
        WHEN (pc.closure_date IS NULL OR pc.closure_date !~ '^\d{4}-\d{2}-\d{2}(\s+\d{2}:\d{2}:\d{2})?$')
            AND pc.result IS NOT NULL
            AND pc.reception_date ~ '^\d{4}-\d{2}-\d{2}(\s+\d{2}:\d{2}:\d{2})?$'
        THEN pc.reception_date::TIMESTAMP + INTERVAL '30 days'
        WHEN pc.closure_date ~ '^\d{4}-\d{2}-\d{2}(\s+\d{2}:\d{2}:\d{2})?$' THEN pc.closure_date::TIMESTAMP
        ELSE NULL
    END                                                             AS closure_date,
    LOWER(pc.result)                                                AS result,
    pc.judicial_authority_intervention,
    pc.monetary_value,
    pc.duration_till_closure,
    pc.complaint_subject,
    pc.handled_by_ivass,
    pc.ref_complaint_ivass,
    --Remove spaces, new lines and non-breaking spaces
    UPPER(TRIM(TRIM(TRIM(pc.ref_claim, E'\n'),CHR(160))))           AS ref_claim,
    NULL                                                            AS ref_policy,
    pc.response,
    pc.transferred_judicial_authority,
    pc.reopened,
    pc.complaint_cause,
    NULL                                                            AS elapsed_days,

    pc.chksum,
    pc.created_on,
    {{ dbt_utils.generate_surrogate_key(['id']) }}                  AS dbt_scd_id,
    pc.created_on                                                   AS dbt_updated_at,
    pc.created_on                                                   AS dbt_valid_from,
    -- Set valid_to to the next version's timestamp if there is a newer version
    cf.next_created_on                                              AS dbt_valid_to

FROM {{ source('mgalanding', 'prima_complaints') }} pc
INNER JOIN complaint_files cf
    ON pc.chksum = cf.chksum
WHERE
    -- Excludes rows in the excel files that are in large part empty
    reception_date IS NOT NULL
