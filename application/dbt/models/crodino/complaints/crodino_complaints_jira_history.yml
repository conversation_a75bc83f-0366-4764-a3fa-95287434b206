version: 2

models:
  - name: crodino_complaints_jira_history
    description: '{{ doc("field_crodino_complaints_jira_history") }}'

    columns:
      - name: id
        description: '{{ doc("field_id") }}'

      - name: distribution_partner
        description: '{{ doc("field_distribution_partner") }}'

      - name: product_id
        description: '{{ doc("field_product_id") }}'

      - name: branch
        description: '{{ doc("field_branch") }}'

      - name: country
        description: '{{ doc("field_country") }}'

      - name: complaint_type
        description: '{{ doc("field_complaint_type") }}'

      - name: complaint_reference
        description: '{{ doc("field_complaint_reference") }}'

      - name: progressive_number
        description: '{{ doc("field_progressive_number") }}'

      - name: issue_number
        description: '{{ doc("field_issue_number") }}'

      - name: creation_year
        description: '{{ doc("field_creation_year") }}'

      - name: complaint_code
        description: '{{ doc("field_complaint_code") }}'

      - name: complaint_handling_code
        description: '{{ doc("field_complaint_handling_code") }}'

      - name: reception_date
        description: '{{ doc("field_reception_date") }}'

      - name: product_type
        description: '{{ doc("field_product_type") }}'

      - name: sector
        description: '{{ doc("field_sector") }}'

      - name: name_policyholder
        description: '{{ doc("field_name_policyholder") }}'

      - name: address_policyholder
        description: '{{ doc("field_address_policyholder") }}'

      - name: type_policyholder
        description: '{{ doc("field_type_policyholder") }}'

      - name: channel
        description: '{{ doc("field_channel") }}'

      - name: complainer_geographic_area
        description: '{{ doc("field_complainer_geographic_area") }}'

      - name: name_complainer
        description: '{{ doc("field_name_complainer") }}'

      - name: address_complainer
        description: '{{ doc("field_address_complainer") }}'

      - name: type_complainer
        description: '{{ doc("field_type_complainer") }}'

      - name: closure_date
        description: '{{ doc("field_closure_date") }}'

      - name: result
        description: '{{ doc("field_result") }}'

      - name: judicial_authority_intervention
        description: '{{ doc("field_judicial_authority_intervention") }}'

      - name: monetary_value
        description: '{{ doc("field_monetary_value") }}'

      - name: duration_till_closure
        description: '{{ doc("field_duration_till_closure") }}'

      - name: complaint_subject
        description: '{{ doc("field_complaint_subject") }}'

      - name: handled_by_ivass
        description: '{{ doc("field_handled_by_ivass") }}'

      - name: ref_complaint_ivass
        description: '{{ doc("field_ref_complaint_ivass") }}'

      - name: ref_claim
        description: '{{ doc("field_ref_claim") }}'

      - name: ref_policy
        description: '{{ doc("field_ref_policy") }}'

      - name: response
        description: '{{ doc("field_response") }}'

      - name: transferred_judicial_authority
        description: '{{ doc("field_transferred_judicial_authority") }}'

      - name: reopened
        description: '{{ doc("field_reopened") }}'

      - name: complaint_cause
        description: '{{ doc("field_complaint_cause") }}'

      - name: elapsed_days
        description: '{{ doc("field_elapsed_days") }}'

      - name: chksum
        description: '{{ doc("field_chksum") }}'

      - name: created_on
        description: '{{ doc("field_created_on") }}'

      - name: dbt_scd_id
        description: '{{ doc("field_dbt_scd_id") }}'

      - name: dbt_updated_at
        description: '{{ doc("field_dbt_updated_at") }}'

      - name: dbt_valid_to
        description: '{{ doc("field_dbt_valid_to") }}'

      - name: dbt_valid_from
        description: '{{ doc("field_dbt_valid_from") }}'
