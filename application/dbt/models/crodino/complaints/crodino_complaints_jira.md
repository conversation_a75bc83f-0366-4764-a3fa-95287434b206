{% docs field_crodino_complaints_jira_history %}
## Overview of the Complaints Jira table
This table contains transformed data deriving from Jira complaints boards.
{% enddocs %}

{% docs field_id %}
Record identifier.
{% enddocs %}

{% docs field_country %}
Country.
{% enddocs %}

{% docs field_complaint_type %}
Contains type of complaint ('Jira', 'Reclami')
{% enddocs %}

{% docs field_complaint_reference %}
Complaint reference. In case of complaints deriving from Jira, it is the Jira ticket.
{% enddocs %}

{% docs field_progressive_number %}
Mostly empty field. Kept for legacy reasons.
{% enddocs %}

{% docs field_issue_number %}
Complaint issue number. When a complaint is reopened the issue number is incremented.
{% enddocs %}

{% docs field_creation_year %}
Year of creation.
{% enddocs %}

{% docs field_complaint_code} %}
Empty - kept for legacy reasons.
{% enddocs %}

{% docs field_complaint_handling_code %}
Empty - kept for legacy reasons.
{% enddocs %}

{% docs field_reception_date %}
Date of complaint reception.
{% enddocs %}

{% docs field_product_type %}
Type of insurance - 2.a furto auto, 2.b furto altri casi, 2.c infortuni, 2.d incendio, 2.e malattia,
2.f credito/cauzione, 2.g r.c. diversi, 2.h altri danni ai beni, 2.i r.c. auto/natanti, 2.l trasporti,
2.m tutela legale, 2.n assistenza, 2.o altro, 2.o altri (comparto danni)
{% enddocs %}

{% docs field_sector %}
Category of complaint followed by subcategory. Original name of Jira field is 'Area Aziendale'.
{% enddocs %}

{% docs field_name_policyholder %}
Proponent name.
{% enddocs %}

{% docs field_address_policyholder %}
Proponent address.
{% enddocs %}

{% docs field_type_policyholder %}
Type of proponent - 1 Diretto interessato, 2 Associazione consumatori, 3 Legale, 4 Consulente, 5 Altro
{% enddocs %}

{% docs field_channel %}
{% enddocs %}

{% docs field_complainer_geographic_area %}
Geographic area.
{% enddocs %}

{% docs field_name_complainer %}
Name complainer
{% enddocs %}

{% docs field_address_complainer %}
Address complainer
{% enddocs %}

{% docs field_type_complainer %}
Type of complainer - 1 Contraente, 2 Assicurato, 3 Danneggiato, 4 Beneficiario, 5 Associazione consumatori, 6 Altro
{% enddocs %}

{% docs field_closure_date %}
Date when complaint was closed. One complaint may be closed multiple times.
{% enddocs %}

{% docs field_result %}
Result of the complaint processing.
{% enddocs %}

{% docs field_judicial_authority_intervention %}
Indicates whether complaint was transferred to judicial authorities.
{% enddocs %}

{% docs field_monetary_value %}
Monetary compensation of complaint.
{% enddocs %}

{% docs field_duration_till_closure %}
Duration of complaint from reception to closure date.
{% enddocs %}

{% docs field_complaint_subject %}
Complaint subject - Jira ticket title
{% enddocs %}

{% docs field_handled_by_ivass %}
Indicates whether complaint was handled by IVASS.
{% enddocs %}

{% docs field_ref_complaint_ivass %}
In case complaint was handled by IVASS, this field contains the IVASS complaint reference.
{% enddocs %}

{% docs field_ref_claim %}
Claim reference. For Jira complaints it is empty as there are no links to claims.
{% enddocs %}

{% docs field_ref_policy %}
Policy reference. For Jira complaints it is empty as there are no links to policies.
{% enddocs %}

{% docs field_response %}
Response to complaint. For Jira complaints the field is empty as response is contained in PDF attachments.
{% enddocs %}

{% docs field_transferred_judicial_authority %}
Indicates whether complaint was transferred to judicial authorities.
{% enddocs %}

{% docs field_reopened %}
Indicates whether the complaint has been reopened
{% enddocs %}

{% docs field_complaint_cause %}
Category cause of complaint. Derived from column above 'sector'.
{% enddocs %}

{% docs field_elapsed_days %}
Elapsed days.
{% enddocs %}

{% docs field_dbt_scd_id %}
Surrogate id.
{% enddocs %}

{% docs field_dbt_updated_at %}
Timestamp when Jira complaint was updated
{% enddocs %}

{% docs field_dbt_valid_to %}
Validity timestamp that indicates time when there is a new file having updates on this Jira ticket.
{% enddocs %}

{% docs field_dbt_valid_from %}
Validity timestamp that is derived from 'created_on' and indicates the time when the f
{% enddocs %}

{ % docs field_postal_code_loss %}
Where the claim event occurred.
{% enddocs %}
