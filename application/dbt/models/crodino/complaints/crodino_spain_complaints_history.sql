{{
    config(
        materialized='table',
        tags=['complaints']
    )
}}

SELECT
    id,
    'CRODINO'                                                       AS distribution_partner,
    'PMOTORES'                                                      AS product_id,
    'IT'                                                            AS branch,
    'Spain'                                                         AS country,
    complaint_type,
    complaint_reference,
    NULL::INT                                                       AS progressive_number,
    NULLIF(n_complaint_references, '-')                             AS issue_number,
    creation_year::INTEGER                                          AS creation_year,
    NULLIF(complaint_code, '-')                                     AS complaint_code,
    complaint_handling_code,
    reception_date::TIMESTAMP                                       AS reception_date,
    product_type,
    sector,
    name_policyholder,
    address                                                         AS address_policyholder,
    NULL                                                            AS type_policyholder,
    channel,
    geographic_area_policyholder,
    name_complainer,
    address_complainer,
    type_complainer,
    closure_date::TIMESTAMP                                         AS closure_date,
    LOWER(status)                                                   AS result,
    judicial_authority_intervention,
    monetary_value,
    duration_till_closure,
    complaint_subject,
    'No'                                                            AS handled_by_ivass,
    NULL                                                            AS ref_complaint_ivass,
    ref_claim,
    ref_policy,
    response,
    transferred_judicial_authority,
    reopened,
    complaint_cause,
    elapsed_days,
    chksum,
    created_on,
    {{ dbt_utils.generate_surrogate_key(['complaint_reference']) }} AS dbt_scd_id,
    created_on                                                      AS dbt_updated_at,
    created_on                                                      AS dbt_valid_from,
    -- Set valid_to to the next version's timestamp if there is a newer version
    LEAD(created_on) OVER (
        PARTITION BY complaint_reference
        ORDER BY created_on
    )                                                               AS dbt_valid_to

FROM {{ source('mgalanding', 'prima_complaints_spain') }}
WHERE complaint_reference IS NOT NULL
