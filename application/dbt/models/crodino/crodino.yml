version: 2

sources:
  - name: mgalanding
    tables:
      - name: prima_suspensions_reactivations
      - name: prima_complaints
      - name: prima_complaints_jira
      - name: prima_complaints_spain
      - name: prima_claims
      - name: prima_premium
        description: '{{ doc("prima_premium") }}'
        columns:
          - name: id
            description: Generic autoincrement identifier generated by the ingestion process in the MGA Database
          - name: chksum
            description: Hash of the file ingested, used to link to the file_processed model
          - name: created_on
            description: Timestamp of ingestion in the database
          - name: agent_policy_number
            description: '{{ doc("field_prima_agent_policy_number") }}'
          - name: insured_name
            description: '{{ doc("field_prima_ph_insured_name") }}'
          - name: direct_or_reinsurance
            description: '{{ doc("field_prima_direct_reinsurance") }}'
          - name: nature_of_contract
            description: '{{ doc("field_prima_nature_of_contract") }}'
          - name: location_of_underwriting
          - name: location_of_risk
          - name: class_of_business
            description:  '{{ doc("field_prima_class_of_business") }}'
          - name: great_lakes_policy_sequence_number
          - name: period_start_date
            description: '{{ doc("field_prima_period_start") }}'
          - name: period_end_date
            description: '{{ doc("field_prima_period_end") }}'
          - name: underwriting_year
            description: '{{ doc("field_prima_underwritting_year") }}'
          - name: transaction_currency
            description: '{{ doc("field_prima_transaction_currency") }}'
          - name: gross_premium
            description: '{{ doc("field_prima_gross_premium") }}'
          - name: ipt_rate
            description: '{{ doc("field_prima_ipt_rate") }}'
          - name: ipt_amount
            description: '{{ doc("field_prima_ipt_amount") }}'
          - name: brokerage
          - name: agency_commission
          - name: provisional_profit_commission
          - name: tax_deducted
          - name: terrorism_premium
          - name: net_balance_due_to_iptq
            description: '{{ doc("field_prima_net_balance_due_to_iptq") }}'
          - name: ipt_territory
            description: '{{ doc("field_prima_ipt_territory") }}'
          - name: targa
            description: '{{ doc("field_prima_motor_license_plate") }}'
          - name: data_emissione
            description: Registration date
          - name: data_incasso
            description: Payment collection date
          - name: imposta_ssn
            description: '{{ doc("field_prima_imposta_ssn") }}'
          - name: imposta_ssn_eur
            description: '{{ doc("field_prima_imposta_ssn_eur") }}'
          - name: imposta_cvt
            description: '{{ doc("field_prima_imposta_cvt") }}'
          - name: imposta_cvt_eur
            description: '{{ doc("field_prima_imposta_cvt_eur") }}'
          - name: imposta_antiracket
            description: '{{ doc("field_prima_imposta_antiracket") }}'
          - name: imposta_antiracket_eur
            description: '{{ doc("field_prima_imposta_antiracket_eur") }}'
          - name: premio_totale
            description: '{{ doc("field_prima_premio_totale") }}'
          - name: provvigione
            description: '{{ doc("field_prima_commission") }}'
          - name: provvigione_eur
            description: '{{ doc("field_prima_commission_eur") }}'
          - name: insured_address
            description: '{{ doc("field_prima_ph_address_street") }}'
          - name: comune
            description: '{{ doc("field_prima_ph_address_comune") }}'
          - name: post_code
            description: '{{ doc("field_prima_ph_address_postcode") }}'
          - name: cod_fiscale
            description: '{{ doc("field_prima_ph_cod_fiscale") }}'
          - name: descrizione_garanzia
            description: '{{ doc("field_prima_descrizione_garanzia") }}'
          - name: riassicurazione_premi
            description: '{{ doc("field_prima_reinsurance_premium") }}'
          - name: instalment
            description: '{{ doc("field_prima_instalment_number") }}'
          - name: veicolo
            description: '{{ doc("field_prima_vehicle_type") }}'
          - name: tipologia_garanzia_acquistata
            description: '{{ doc("field_prima_tipologia_garanzia_acquista") }}'
          - name: frequenza
            description: '{{ doc("field_prima_payment_frequency") }}'
          - name: parent_code
            description: '{{ doc("field_prima_parent_code") }}'
          - name: data_emissione_sost
            description: '{{ doc("field_prima_data_emissione_sosto") }}'
          - name: type_of_building
            description: '{{ doc("field_prima_property_building_type") }}'
          - name: use
            description: '{{ doc("field_prima_property_usage") }}'
          - name: square_meters
            description: '{{ doc("field_prima_property_square_meter") }}'
          - name: property_city
            description: '{{ doc("field_prima_property_city") }}'
          - name: property_zipcode
            description: '{{ doc("field_prima_property_zipcode") }}'
          - name: floor
            description: '{{ doc("field_prima_property_floor") }}'
          - name: construction_year_or_latest_significative_renew
            description: '{{ doc("field_prima_construction_year_or_latest_significative_renew") }}'
          - name: coliving_adults
          - name: coliving_minors
          - name: past_incidents
          - name: condominium_type
            description: '{{ doc("field_prima_condominium_type") }}'
          - name: house_type
            description: '{{ doc("field_prima_house_type") }}'
          - name: form_type
          - name: limit_value
          - name: deductible_value
          - name: source
            description: '{{ doc("field_prima_distribution_channel") }}'
          - name: discount_10pct
          - name: product_type
            description: '{{ doc("field_prima_product_type") }}'
          - name: valid_until
          - name: annual_gross_premium
          - name: annual_ipt_amount
          - name: parent_code_renewal
            description: '{{ doc("field_prima_parent_code_renewal") }}'
      - name: prima_premium_spain
      - name: prima_claims_spain


models:
   - name: crodino_portfolio
     description: '{{ doc("crodino_portfolio") }}'
     config:
      indexes:
        - columns: ['id']
          unique: True

   - name: stg_crodino_portfolio
     config:
      indexes:
        - columns: ['id']
          unique: True

   - name: stg_crodino_codice_fiscale
     config:
      indexes:
        - columns: ['codice_fiscale']
          unique: True
        - columns: ['created_on']

   - name: stg_crodino_portfolio_annual_dates
     config:
      indexes:
        - columns: ['agent_policy_number']
          unique: True

   - name: crodino_policy_lifecycle
     config:
       indexes:
         - columns: [ 'agent_policy_number' ]
           unique: True

   - name: stg_crodino_portfolio_annual_ipt_amount
     config:
      indexes:
        - columns: ['id']
          unique: True

   - name: stg_crodino_portfolio_annual_premium_amount
     config:
      indexes:
        - columns: ['id']
          unique: True

   - name: stg_original_policies_dates
     config:
      indexes:
        - columns: ['id']
          unique: True

   - name: stg_original_policies_dates_date_policy_extracts
     config:
       indexes:
         - columns: [ 'agent_policy_number' ]
           unique: True

   - name: stg_crodino_portfolio_cumulative_reserves_aggregation
     config:
       indexes:
         - columns: [ 'crodino_policy_id', 'cover', 'class_of_business', 'reserve_start_period' ]

   - name: stg_crodino_portfolio_reserving_earned_premium
     config:
       indexes:
         - columns: [ 'crodino_policy_id', 'cover', 'class_of_business', 'reserve_start_period' ]
