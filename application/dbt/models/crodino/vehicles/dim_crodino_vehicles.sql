{{
    config(
        materialized="table"
    )
}}


SELECT
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'vehicle_key_type', 'plate_number', 'country_code']
        )
    }}                  AS vehicle_id,
    plate_number,
    vehicle_category,
    vehicle_make,
    vehicle_model,
    vehicle_power,
    fuel_type,
    actual_value,
    created_on          AS updated_on
FROM {{ ref('stg_crodino_vehicles') }}
WHERE
    rn = 1
