{{
    config(
        materialized="table"
    )
}}


SELECT
    'CRODINO'               AS distribution_partner,
    'plate_number'          AS vehicle_key_type,
    'crodino_policy_id'     AS policy_key_type,
    location_of_risk        AS country_code,
    agent_policy_number     AS crodino_policy_id,
    plate_number,
    vehicle_category,
    vehicle_make,
    vehicle_model,
    vehicle_power,
    fuel_type,
    actual_value,
    created_on,
    ROW_NUMBER() OVER (PARTITION BY plate_number ORDER BY created_on DESC) AS rn
FROM {{ ref('crodino_portfolio') }}
WHERE
    plate_number IS NOT NULL
