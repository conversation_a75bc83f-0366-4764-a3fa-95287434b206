{% docs prima_premium %}
# Prima Premium
Bordereau containing the set of transactions happening to a policy. It is meant to serve as a cash statement.

We receive three types of Bordereaux for the policy sold in Italy:
* Prima Motor Italian Branch
* Prima Motor German Branch
* Prima Home Italian Branch Motor

{% enddocs %}

{% docs field_prima_agent_policy_number %}
The Policy ID, also called agent_policy_number. The policy id would generally translate onto an annual policy, but may be shorter in case of substitution, cancelation and can be slightly longer in case of bridging days or suspension/reactivation.

Upon substitution or renewal, a new policy will be issued.
{% enddocs %}

{% docs field_prima_nature_of_contract %}
The nature of the transaction.


The possible values are:

| nature_of_contract | translation |
| ---| ---|
| Emissione - Mensile / Annuale| Issuance - Monthly/Yearly |
| Appendice - IN | Appendix - IN (in case of adding a peril)|
| Appendice - OUT |  Appendix - OUT (in case of removing a peril)|
| Interruzione - Mensile/Annuale | Cancellation - Monthly/Yearly|
| Recesso - Mensile/Annuale | Withdrawal - Monthly/Yearly|
| SosOUT - Mensile/Annuale | Change of guarantee = peril = cover - Monthly/Yearly|
| SosIN - Mensile/Annuale | Change of guarantee = peril = cover - Monthly/Yearly|
| Annulamento di Interruzione - Mensile/Annuale |  Reinstatement - Monthly/Yearly|
{% enddocs %}


{% docs field_prima_class_of_business %}
Numerical value representing the Solvency II class line of business (integer from 1 to 18).

| class_of_business| description |
| ---|---|
| 1 | Accident|
| 3 | Land vehicle|
| 8 | Fire and Natural forces|
| 9 | Other damage to property|
| 10| Motor vehicle liability|
| 13| General liability|
| 17| Legal expenses|
| 18| Assistance|

{% enddocs %}

{% docs field_prima_period_start %}
Starting period of application of the transaction, for an initial Emissione (1st instalment), it would typically corresponds to the start date of the policy, while for cancelation type transactions (Interruzione, Substitution out, ...) it would correspond to the end date of the policy. The period where the policy is not active starting at that time.
{% enddocs %}

{% docs field_prima_period_end %}
End periuod of application of the transaction, for initial Emissione, it would typically corresponds to the end date of the instalment that has been paid for.
{% enddocs %}

{% docs field_prima_direct_reinsurance %}
Alphanumeric, 1 digit flag to indicate if it is direct or reinsurance (D = Direct, R = Reinsurance)
{% enddocs %}

{% docs field_prima_underwritting_year %}
Date format YYYY representign the year the policy was underwritten
{% enddocs %}

{% docs field_prima_transaction_currency %}
ISO 4217, code with 3 digits, e.g. EUR = euros
{% enddocs %}

{% docs field_prima_gross_premium %}
misnomer - this is the premium without tax (gross as gross of reinsurance, nothing related to tax)
{% enddocs %}

{% docs field_prima_premio_totale %}
Total premium taxes included
{% enddocs %}

{% docs field_prima_reinsurance_premium %}
Reinsurance premium
{% enddocs %}

{% docs field_prima_commission %}
Commission in percentage, based on the premium before taxes
{% enddocs %}

{% docs field_prima_commission_eur %}
Commission amount, based on the premium before taxes
{% enddocs %}

{% docs field_prima_ipt_rate %}
Percentage XX.XX%, The IPT in Italy (Insurance Premium Tax) is composed of these 3 elements: SSN + CVT + Antiracket. The taxes are applied per cover = peril = garanzia as not all can be applied to each cover = peril = garanzia.

One of the component of the IPT varies upon province (the equivalent of the canton in Switzerland).

| class_of_business|  IPT Rate |
| ---|---|
| 1 | 2.50%|
| 3 | 13.50%|
| 10 | 23% or 26.50% depending on the IPT territory|
| 17 | 12.50%|
| 18 | 10%|
{% enddocs %}

{% docs field_prima_imposta_ssn %}
Tax percentage for the Contributo Servizio Sanitario Nazionale (SSN) meaning the national health service contribution to cover medical expenses for the injured and road victims.
{% enddocs %}

{% docs field_prima_imposta_ssn_eur %}
Monetary amount for the SSN tax (Contributo Servizio Sanitario Nazionale)
{% enddocs %}

{% docs field_prima_imposta_cvt %}
Tax percentage for the Corpi Veicoli Terrestri (CVT) meaning the terrestrial vehicle parts.
{% enddocs %}

{% docs field_prima_imposta_cvt_eur %}
Monetary amount for the CVT tax
{% enddocs %}

{% docs field_prima_imposta_antiracket %}
Tax percentage for the contribution to the antiracket fund
{% enddocs %}

{% docs field_prima_imposta_antiracket_eur %}
Monetary amount for the Antiracket tax
{% enddocs %}

{% docs field_prima_descrizione_garanzia %}
Description of the peril/non-peril in iptiQ’s terminology, cover in INSIS’ terminology;

1 = infortuni conducente = accident

3 = cristalli / furto incendio = glass breakage / theft and fire

10 = rca (responsabilita civile automobile) / bonus protetto = motor liability / NCB no reduction

17 = tutela legale = legal expenses

18 = assistenza stradale = road side assistance
{% enddocs %}

{% docs field_prima_instalment_number %}
Instalment number.

For an annual policy should be 1

For bi-annual policy should be from 1 to 2

For a monthly policy should increase from 1 to 12
{% enddocs %}


{% docs field_prima_vehicle_type %}
Type of vehicle

3 possible values: car, van, motorcycle

{% enddocs %}


{% docs field_prima_payment_frequency %}
Payment frequency.

3 possible values: yearly, monthly and half-yearly
{% enddocs %}

{% docs field_prima_tipologia_garanzia_acquista %}
Tipologia_garanzia_acquistata is the level offered for some peril/non-peril in iptiQ’s terminoligy, cover in INSIS’ terminology, is used for instance for the road assistance as they propose 2 levels: Base and Super.
{% enddocs %}

{% docs field_prima_parent_code %}
Reference to another insurance policy reference, used in case of substitution. Meant to reference the previous policy which was subsituted. (Null in case the policy is a new issuance without subsitution).
{% enddocs %}

{% docs field_prima_data_emissione_sosto %}
"data emissione sostitutiva" is the effective date of the original contract in a case of substitution
{% enddocs %}

{% docs field_prima_property_usage %}
Meant to represent the type of usage of the property.

List of values:
* Rented
* Residential
* Second house
* No property
{% enddocs %}

{% docs field_prima_property_building_type %}
Represents the type of building insured, whether it is an appartment or a house.

List of values:
* Apartment
* House
{% enddocs %}

{% docs field_prima_property_floor %}
List of values:
* 0
* Ground
* Low
* Intermediate /  Medium
* Top
{% enddocs %}

{% docs field_prima_construction_year_or_latest_significative_renew %}
can take value in the format:
* before1970
* decade1970
* unknown
{% enddocs %}

{% docs field_prima_product_type %}
Used to distinguish the different Prima Home products

List of values:
* Famiglia
* Casa
{% enddocs %}

{% docs field_prima_property_zipcode %}
Risk Address of the policyholder (postcode)
{% enddocs %}

{% docs field_prima_property_city %}
Risk Address of the policyholder (city)
{% enddocs %}

{% docs field_prima_property_square_meter %}
Number of square meters for the property
{% enddocs %}

{% docs field_prima_house_type %}
Classification of housetype.

List of values:
* terraced_house
* single_family
{% enddocs %}

{% docs field_prima_condominium_type %}
Classification of condominium type.

List of values:
* up_to_eight
* more_than_eight
* 0
{% enddocs %}

{% docs field_prima_net_balance_due_to_iptq %}
This is the amount that should reach iptiQ's bank account on a monthly basis
{% enddocs %}

{% docs field_prima_ipt_amount %}
Amount of IPT tax to  be paid, calculated as IPT amount = IPT rate x Gross Premium
{% enddocs %}

{% docs field_prima_distribution_channel %}
Distribution channel, that generated the sale of the policy
{% enddocs %}

{% docs field_prima_ipt_territory %}
ISO 3166-2 code with 2 digits, the insurance tax needs to be reported per province in Italy, equivalent of the canton in Switzerland.
{% enddocs %}

{% docs field_prima_motor_license_plate %}
License plate of the insured object, the Prima Motor product supports one car plate per policy
{% enddocs %}

{% docs field_prima_parent_code_renewal %}
Field provided by Prima in order to classify whether or not a policy is a new business or a renewal.
When filled, the field is meant to represent the policy that has been renewed.
{% enddocs %}

{% docs field_prima_ph_cod_fiscale %}
Codice fiscale = tax code
{% enddocs %}

{% docs field_prima_ph_insured_name %}
Name of the insured person
{% enddocs %}

{% docs field_prima_ph_address_postcode %}
Address of the policyholder (postcode)
{% enddocs %}

{% docs field_prima_ph_address_comune %}
Address of the policyholder (comune/city)
{% enddocs %}

{% docs field_prima_ph_address_street %}
Address of the policyholder (street name and street number)
{% enddocs %}
