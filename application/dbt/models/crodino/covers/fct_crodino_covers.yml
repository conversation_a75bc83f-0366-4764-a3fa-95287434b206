version: 2

models:
   -  name: fct_crodino_covers
      description: '{{ doc("fct_crodino_covers") }}'
      columns:
      - name: distribution_partner
        description: '{{ doc("field_distribution_partner") }}'
      - name: product_id
        description: '{{ doc("field_product_id") }}'
      - name: policy_id
        description: '{{ doc("field_policy_id") }}'
      - name: cover_id
        description: '{{ doc("field_cover_id") }}'
      - name: man_id
        description: '{{ doc("field_man_id") }}'
      - name: crodino_policy_id
        description: '{{ doc("field_crodino_policy_id") }}'
      - name: cover_description
        description: '{{ doc("field_cover_description") }}'
      - name: solvency_class
        description: '{{ doc("field_solvency_class") }}'
      - name: policy_status
        description: '{{ doc("field_policy_status") }}'
      - name: cover_premium_yearly_net
        description: '{{ doc("field_crodino_cover_premium_yearly_net") }}'
      - name: cover_premium_paid
        description: '{{ doc("field_crodino_cover_premium_paid") }}'
      - name: cover_ipt_yearly
        description: '{{ doc("field_crodino_cover_ipt_yearly") }}'
      - name: main_premium_yearly_net
        description: '{{ doc("field_crodino_main_premium_yearly_net") }}'
      - name: main_ipt_yearly
        description: '{{ doc("field_crodino_main_ipt_yearly") }}'
      - name: main_premium_paid
        description: '{{ doc("field_crodino_main_premium_paid") }}'
      - name: insured_value
        description: '{{ doc("field_insured_value") }}'
      - name: deductible_value
        description: '{{ doc("field_deductible_value") }}'
      - name: is_household
        description: '{{ doc("field_is_household") }}'
