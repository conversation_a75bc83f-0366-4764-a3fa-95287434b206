{% docs fct_crodino_covers %}
# fct_crodino_covers
Provides a consolidated view of all Prima Covers.
{% enddocs %}

{% docs field_cover_id %}
A unique identifier for a cover generated by the MGA Platform.
{% enddocs %}

{% docs field_cover_description %}
A text value describing the type of coverage offered
{% enddocs %}

{% docs field_solvency_class %}
Solvency II - 18 line of business, indicates the classification of risk offered by the coverage.
{% enddocs %}

{% docs field_crodino_cover_premium_yearly_net %}
The annualized premium without tax attributable to a given policy/cover.
{% enddocs %}

{% docs field_crodino_cover_ipt_yearly %}
The annualized insurance premium tax attributable to a given policy/cover.
{% enddocs %}

{% docs field_crodino_cover_premium_paid %}
The total amount paid, attributed to a given policy/cover.
{% enddocs %}

{% docs field_crodino_main_premium_yearly_net %}
The Annualized Premium without tax for a given (main) policy/cover, across it's substitution lifecycle until this particular policy.
{% enddocs %}

{% docs field_crodino_main_ipt_yearly %}
The Annualized Insurance Premium tax for a given (main) policy/cover, across it's substitution lifecycle until this particular policy.
{% enddocs %}

{% docs field_crodino_main_premium_paid %}
The total amount paid for a given (main) policy/cover, across it's substitution lifecycle until this particular policy.
{% enddocs %}

{% docs field_insured_value %}
The maximum amount that is insured for a given coverage.
{% enddocs %}

{% docs field_deductible_value %}
The amount that would need to be paid by the insured before the insurer's coverage would kick in.
{% enddocs %}

{% docs field_product_id %}
Unique identifier for type of product/insurance contract.
{% enddocs %}

{% docs field_updated_on %}
When it was updated.
{% enddocs %}
