{{
    config(
        materialized="ephemeral"
    )
}}
SELECT
    distribution_partner,
    product_id,
    branch,
    crodino_policy_id,
    cover_description,
    man_id,
    insured_value,
    deductible_value,
    STRING_AGG(
        DISTINCT solvency_class, '|'
        ORDER BY solvency_class
    )                            AS solvency_class,
    SUM(main_premium_yearly_net) AS main_premium_yearly_net,
    SUM(main_ipt_yearly)         AS main_ipt_yearly,
    SUM(main_premium_paid)       AS main_premium_paid
FROM {{ ref('fct_crodino_covers') }}
GROUP BY
    distribution_partner,
    product_id,
    branch,
    crodino_policy_id,
    cover_description,
    man_id,
    insured_value,
    deductible_value
