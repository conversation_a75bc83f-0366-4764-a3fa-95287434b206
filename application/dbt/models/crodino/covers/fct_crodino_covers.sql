{{
    config(
        materialized="table"
    )
}}

WITH
stg_policies AS (
    SELECT
        'CRODINO'               AS distribution_partner,
        'crodino_policy_id'     AS policy_key_type,
        'crodino_cover_id'      AS cover_key_type,
        agent_policy_number     AS crodino_policy_id,
        'codice_fiscale'        AS people_key_type,
        product_id,
        branch,
        codice_fiscale,
        location_of_risk        AS country_code,
        direct_or_reinsurance,
        location_of_underwriting,
        location_of_risk,
        payment_frequency       AS frequency,
        cover                   AS cover_description,
        cover_level,
        class_of_business       AS solvency_class,
        is_household,
        limit_value             AS insured_value,
        insured_value_pd,
        insured_value_bi,
        deductible_value,
        policy_depth + 1        AS policy_transaction_id,
        original_policy_number,
        created_on,
        ROW_NUMBER() OVER (
            PARTITION BY agent_policy_number, cover, class_of_business
            ORDER BY created_on DESC
        )                       AS rn
    FROM {{ ref('crodino_portfolio') }}
),
cover_aggs AS (
    SELECT
        cp.agent_policy_number                   AS crodino_policy_id,
        cp.cover                                 AS cover_description,
        cp.class_of_business                     AS solvency_class,
        cp.policy_annual_start_date_local,
        sp.policy_status,
        MAX(cp.policy_annual_end_date_local)     AS policy_annual_end_date_local,
        MIN(cp.period_start_date_local)
            FILTER (
                WHERE LOWER(cp.nature_of_contract) IN (
                    'emissione - annuale',
                    'emissione - mensile',
                    'emissione - semestrale',
                    'appendice in',
                    'appendice out',
                    'sosin - annuale',
                    'sosin - mensile',
                    'sosin - semestrale'
                )
            )                                    AS cover_start_date_local,
        MAX(cp.instalment)                       AS instalment,
        SUM(cp.annualized_gross_written_premium) AS cover_premium_yearly_net,
        SUM(cp.annualized_ipt)                   AS cover_ipt_yearly,
        SUM(cp.gross_written_premium)            AS cover_premium_paid,
        SUM(cp.annualized_gross_written_premium)
            FILTER (
                WHERE LOWER(cp.nature_of_contract) IN (
                    'new business',
                    'emissione - annuale',
                    'emissione - mensile',
                    'emissione - semestrale',
                    'appendice in',
                    'appendice out',
                    'sosin - annuale',
                    'sosin - mensile',
                    'sosin - semestrale'
                )
            )                              AS cover_premium_yearly_net_initial
    FROM {{ ref('crodino_portfolio') }} cp
    INNER JOIN {{ ref('stg_crodino_policies') }} sp
        ON cp.agent_policy_number = sp.crodino_policy_id
    GROUP BY
    	cp.agent_policy_number,
    	cp.cover,
        cp.class_of_business,
    	cp.policy_annual_start_date_local,
    	sp.policy_status
)


SELECT
    distribution_partner,
    product_id,
    branch,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'policy_key_type', 'cover_key_type', 'crodino_policy_id', 'cover_description', 'solvency_class']
        )
    }}                                               AS cover_id,

    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'policy_key_type', 'crodino_policy_id']
        )
    }}                                               AS policy_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'policy_key_type', 'original_policy_number']
        )
    }}                                               AS main_policy_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'people_key_type', 'codice_fiscale', 'country_code']
        )
    }}                                               AS man_id,
    crodino_policy_id,
    cover_description,
    cover_level,
    solvency_class,
    is_household,
    cover_start_date_local,
    policy_annual_start_date_local,
    policy_annual_end_date_local,
    policy_status,
    instalment                                       AS max_instalment,
    cover_premium_yearly_net_initial,
    cover_premium_yearly_net,
    cover_premium_paid,
    cover_ipt_yearly,
    SUM(cover_premium_yearly_net) OVER w_main_policy AS main_premium_yearly_net,
    SUM(cover_ipt_yearly) OVER w_main_policy         AS main_ipt_yearly,
    SUM(cover_premium_paid) OVER w_main_policy       AS main_premium_paid,
    insured_value,
    insured_value_pd,
    insured_value_bi,
    deductible_value,
    created_on                                       AS updated_on
FROM stg_policies
LEFT JOIN cover_aggs
    USING (crodino_policy_id, cover_description, solvency_class)
WHERE
    rn = 1
WINDOW w_main_policy AS (
    PARTITION BY original_policy_number, cover_description, solvency_class
    ORDER BY policy_transaction_id
)
