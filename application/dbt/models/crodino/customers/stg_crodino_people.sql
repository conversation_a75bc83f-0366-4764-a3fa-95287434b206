{{
    config(
        materialized="ephemeral"
    )
}}


WITH
portfolio_people AS (
    SELECT
        'CRODINO'         AS distribution_partner,
        'codice_fiscale'  AS people_key_type,
        codice_fiscale,
        insured_name,
        location_of_risk  AS country_code,
        date_of_birth,
        CASE
            WHEN date_of_birth IS NULL
                THEN 'LEGAL_ENTITY'
                ELSE 'INDIVIDUAL'
        END               AS type,
        created_on,
        ROW_NUMBER () OVER (
            PARTITION BY codice_fiscale ORDER BY created_on DESC
        )                 AS rn
    FROM {{ ref('crodino_portfolio') }}
)

SELECT
    distribution_partner,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'people_key_type', 'codice_fiscale', 'country_code']
        )
    }}                    AS man_id,
    codice_fiscale,
    insured_name,
    date_of_birth,
    type,
    created_on            AS updated_on,
    rn                    AS rn
FROM portfolio_people
