{{
    config(
        enabled=false,
        materialized="table",
        tags=["crodino", "adm", "prima"]
    )
}}
WITH
mdm_attributes AS (
    SELECT
        'iptiQ EMEA P&C'                        AS bk_policy_sale_operation_entity_oe,
        legalentityname                         AS policy_sale_operation_entity_des,
        '1'                                     AS bk_policy_sale_operation_entity,
        legal_country_conformed_value           AS location_of_branch,
        country_of_origin_conformed_value       AS location_of_business,
        IFRS17distribution_partner_name         AS bk_policy_sale_distribution_partner_name_oe,
        IFRS17distribution_partner_name         AS policy_sale_distribution_partner_name_des,
        IFRS17distribution_partner_name         AS bk_policy_sale_distribution_partner_name,
        'Partial service model'                 AS bk_policy_sales_busines_model_oe,
        'Partial service model'                 AS policy_sales_busines_model_des,
        '1'                                     AS bk_policy_sales_busines_model,
        product_id                              AS bk_policy_sale_product_name_oe,
        product_id                              AS policy_sale_product_name_oe_des
    FROM {{ ref('ifrs17_mdm_attributes') }}
),

lob_attributes AS (
    SELECT
        lob_sii                AS solvency,
        lob_mdm                AS lob_mdm,
        lob_mdm_description    AS lob_mdm_description,
        product_id             AS product_id,
        distribution_partner   AS distribution_partner,
        ValidityFromLoB        AS ValidityFromLoB,
        ValidityToLoB          AS ValidityToLoB
    FROM {{ ref('ifrs17_lob_mapping') }}
),

country_mdm AS (
    SELECT
        con.country_sld_id      AS country_sld_id,
        con.country_name        AS country_name,
        con.continent_sld_id    AS continent_sld_id,
        con.continent_name      AS continent_name,
        cou.alpha2              AS alpha2,
        cou.alpha3              AS alpha3
    FROM {{ ref('country_continent_mdm') }} AS con
    LEFT JOIN {{ ref('seed_countries') }}   AS cou
        ON con.country_name = cou.country_name
),

crodino_portfolio_policies AS (
    SELECT
        cp.*,
        sc.alpha3 AS country_code
    FROM {{ ref('crodino_portfolio') }}     AS cp
    LEFT JOIN {{ ref('seed_countries') }}   AS sc
        on cp.location_of_risk = sc.alpha2
),

portfolio_policies AS (
    SELECT
        po.*,
        p.policy_status                AS portfolio_policy_status,
        p.policy_classification,
        p.distribution_partner,
        p.crodino_policy_id,
        p.branch,
        p.payment_frequency,
        p.main_policy_start_date,
        p.policy_id
    FROM crodino_portfolio_policies as po
    LEFT JOIN {{ ref('fct_crodino_policies') }} as p
        ON p.crodino_policy_id = po.agent_policy_number
),

portfolio_policy_and_mdm AS (
    SELECT
        mdm.bk_policy_sale_operation_entity_oe                                                                  AS bk_policy_sale_operation_entity_oe,
        mdm.policy_sale_operation_entity_des                                                                    AS policy_sale_operation_entity_des,
        mdm.bk_policy_sale_operation_entity                                                                     AS bk_policy_sale_operation_entity,
        mdm.bk_policy_sale_distribution_partner_name_oe                                                         AS bk_policy_sale_distribution_partner_name_oe,
        mdm.policy_sale_distribution_partner_name_des                                                           AS policy_sale_distribution_partner_name_des,
        mdm.bk_policy_sale_distribution_partner_name                                                            AS bk_policy_sale_distribution_partner_name,
        mdm.bk_policy_sales_busines_model_oe                                                                    AS bk_policy_sales_busines_model_oe,
        mdm.policy_sales_busines_model_des                                                                      AS policy_sales_busines_model_des,
        mdm.bk_policy_sales_busines_model                                                                       AS bk_policy_sales_busines_model,
        cou.country_name                                                                                        AS bk_policy_sale_country_oe,
        cou.country_name                                                                                        AS policy_sale_country_oe_des,
        cou.country_sld_id                                                                                      AS bk_policy_sale_country,
        cou.continent_name                                                                                      AS bk_policy_sale_region_oe,
        cou.continent_name                                                                                      AS policy_sale_region_oe_des,
        cou.continent_sld_id                                                                                    AS bk_policy_sale_region,
        p.agent_policy_number                                                                                   AS bk_policy_number,
        CASE
            WHEN p.portfolio_policy_status = 'SUBSTITUTED_OUT' THEN 'Active'
            WHEN p.policy_annual_start_date_local > NOW() THEN 'Not yet active'
            ELSE p.portfolio_policy_status
        END                                                                                                    AS bk_policy_sale_in_force_status_oe,
        CASE
            WHEN p.portfolio_policy_status = 'ACTIVE' THEN '0'
            WHEN p.portfolio_policy_status = 'SUBSTITUTED_OUT' THEN '0'
            WHEN p.portfolio_policy_status = 'CANCELLED' THEN '2'
            WHEN p.portfolio_policy_status = 'Expired' THEN  '1'
            WHEN p.policy_annual_start_date_local > NOW() THEN '3'
            ELSE '-9999'
        END                                                                                                     AS bk_policy_sale_in_force_status,
        CASE
            WHEN p.portfolio_policy_status = 'SUBSTITUTED_OUT' THEN 'Active'
            WHEN p.policy_annual_start_date_local > NOW() THEN 'Not yet active'
            ELSE p.portfolio_policy_status
        END                                                                                                     AS policy_sale_in_force_status_oe_des,
        CASE
            WHEN p.policy_annual_end_date_local < NOW() THEN '3'
            WHEN p.policy_classification = 'NEW_BUSINESS' or p.portfolio_policy_status = 'FALSE_NEW_BUSINESS' THEN '0'
            WHEN p.policy_classification = 'RENEWAL' THEN '1'
            WHEN p.portfolio_policy_status = 'CANCELLED' THEN '2'
            WHEN p.portfolio_policy_status = 'EXPIRED' THEN '4'
            ELSE '-9999'
        END                                                                                                     AS bk_policy_sales_status,
        CASE
            WHEN p.policy_annual_end_date_local < NOW() THEN 'Lapsed'
            WHEN p.policy_classification = 'NEW_BUSINESS' or p.portfolio_policy_status = 'FALSE_NEW_BUSINESS' THEN 'New'
            WHEN p.policy_classification = 'RENEWAL' THEN 'Renewal'
            WHEN p.portfolio_policy_status = 'CANCELLED' THEN 'Cancelled'
            WHEN p.portfolio_policy_status = 'EXPIRED' THEN 'Expired'
            ELSE '#N/A#'
        END                                                                                                     AS bk_policy_sales_status_oe,
        CAST(p.class_of_business AS VARCHAR)                                                                    AS bk_policy_sale_line_of_business_oe,
        lob.lob_mdm                                                                                             AS bk_policy_sale_line_of_business,
        lob.lob_mdm_description                                                                                 AS policy_sale_line_of_business_oe_des,
        p.cover                                                                                                 AS bk_policy_sale_product_coverge_oe,
        p.cover                                                                                                 AS policy_sale_product_coverge_oe_des,
        NULL                                                                                                    AS bk_policy_sale_product_object_insured_oe,
        NULL                                                                                                    AS policy_sale_product_insured_object_oe_des,
        p.product_id                                                                                            AS bk_policy_sale_product_name_oe,
        mdm.policy_sale_product_name_oe_des                                                                     AS policy_sale_product_name_oe_des,
        'EUR'                                                                                                   AS bk_policy_sale_currency_code_oe,
        cur.currency_long_name                                                                                  AS policy_sale_currency_code_oe_des,
        cur.currency_sdl_id                                                                                     AS bk_policy_sale_currency_code,
        '0'                                                                                                     AS bk_policy_sales_actual_estimate,
        NULL                                                                                                    AS bk_policy_sales_source_system_oe,
        NULL                                                                                                    AS policy_sales_source_system_oe_des,
        NULL                                                                                                    AS bk_policy_sales_source_system,
        NULL                                                                                                    AS bk_policy_sales_extract_time_oe,
        NULL                                                                                                    AS bk_policy_sales_extract_time_utc,
        NULL                                                                                                    AS bk_policy_sales_last_created_time_oe,
        NULL                                                                                                    AS bk_policy_sales_last_created_time_utc,
        NULL                                                                                                    AS bk_policy_sales_last_updated_time_oe,
        NULL                                                                                                    AS bk_policy_sales_last_updated_time_utc,
        '0'                                                                                                     AS bk_policy_sales_row_type,
        '0'                                                                                                     AS bk_policy_sales_extract_type,
        NULL                                                                                                    AS bk_policy_sales_extract_version,
        NULL                                                                                                    AS bk_policy_sales_policy_lapse_time_oe,
        NULL                                                                                                    AS bk_policy_sales_policy_lapse_time_utc,
        NULL                                                                                                    AS bk_policy_sales_policy_renewal_time_oe,
        NULL                                                                                                    AS bk_policy_sales_policy_renewal_time_utc,
        NULL                                                                                                    AS bk_policy_sales_policy_cancelled_time_oe,
        NULL                                                                                                    AS bk_policy_sales_policy_cancelled_time_utc,
        NULL                                                                                                    AS bk_policy_sales_snapshot_month,
        SUM(p.annualized_gross_written_premium)                                                                 AS val_annualised_premium,
        SUM(p.annualized_gross_written_premium)                                                                 AS val_gross_premiums_written,
        MIN(p.issue_date_local)                                                                                 AS bk_policy_sales_policy_sale_time_oe,
        MIN(p.issue_date)                                                                                       AS bk_policy_sales_policy_sale_time_utc,
        MIN(p.policy_annual_start_date_local)                                                                   AS bk_policy_sales_policy_effective_time_oe,
        (MIN(p.POLICY_ANNUAL_START_DATE_LOCAL) AT TIME ZONE 'CET') AT TIME ZONE 'UTC'                           AS bk_policy_sales_policy_effective_time_utc,
        MAX(p.policy_annual_end_date_local)                                                                     AS bk_policy_sales_policy_end_time_oe,
        (MAX(p.policy_annual_end_date_local) AT TIME ZONE 'CET') AT TIME ZONE 'UTC'                             AS bk_policy_sales_policy_end_time_utc
    FROM portfolio_policies as p
    LEFT JOIN lob_attributes AS lob
        ON CAST(p.class_of_business AS VARCHAR) = lob.solvency
        AND current_date BETWEEN lob.ValidityFromLoB and lob.ValidityToLoB
        AND p.product_id = COALESCE(lob.product_id, p.product_id)
        AND p.distribution_partner = COALESCE(lob.distribution_partner, p.distribution_partner)
    LEFT JOIN mdm_attributes AS mdm
        ON p.product_id = mdm.bk_policy_sale_product_name_oe
        AND p.country_code = mdm.location_of_branch
    LEFT JOIN {{ ref('currency_mdm') }} AS cur
        ON cur.currency_iso_code = 'EUR'
    LEFT JOIN country_mdm AS cou
        ON P.country_code = cou.alpha3
    WHERE
        p.transaction_iscurrent=True
    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52
)
SELECT
    *
FROM portfolio_policy_and_mdm
