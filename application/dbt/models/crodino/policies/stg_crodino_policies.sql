{{
    config(
        materialized="table",
        tags=["model-crodino-policies"]
    )
}}


WITH
stg_policies AS (
    SELECT
        'CRODINO'                       AS distribution_partner,
        product_id,
        'crodino_policy_id'             AS policy_key_type,
        agent_policy_number             AS crodino_policy_id,
        original_policy_number          AS crodino_main_policy_id,
        policy_depth + 1                AS transaction_id,
        'codice_fiscale'                AS people_key_type,
        codice_fiscale,
        location_of_risk                AS country_code,
        direct_or_reinsurance,
        product_type,
        form_type,
        is_household,
        branch,
        location_of_underwriting,
        location_of_risk,
        UPPER(payment_frequency)        AS payment_frequency,
        'crodino_address'               AS address_key_type,
        insured_address                 AS address,
        city,
        post_code,
        parent_code_renewal,
        source,
        CASE
            WHEN policy_annual_date_rule IN (
                'MONTHLY_BASE',
                'SEMIANNUAL_BASE',
                'ANNUAL_BASE',

                'MONTHLY_SUBSIN',
                'SEMIANNUAL_SUBSIN',
                'ANNUAL_SUBSIN',

                'ANNULLED_MONTHLY_BASE',
                'ANNULLED_SEMIANNUAL_BASE',
                'ANNULLED_ANNUAL_BASE',

                'ANNULLED_MONTHLY_SUBSIN',
                'ANNULLED_SEMIANNUAL_SUBSIN',
                'ANNULLED_ANNUAL_SUBSIN'
                ) THEN 'ACTIVE'

            WHEN policy_annual_date_rule IN (
                'MONTHLY_SUBSOUT',
                'SEMIANNUAL_SUBSOUT',
                'ANNUAL_SUBSOUT',

                'MONTHLY_SUBSINOUT',
                'SEMIANNUAL_SUBSINOUT',
                'ANNUAL_SUBSINOUT',

                'ANNULLED_MONTHLY_SUBSOUT',
                'ANNULLED_SEMIANNUAL_SUBSOUT',
                'ANNULLED_ANNUAL_SUBSOUT',

                'ANNULLED_MONTHLY_SUBSINOUT',
                'ANNULLED_SEMIANNUAL_SUBSINOUT',
                'ANNULLED_ANNUAL_SUBSINOUT'
                ) THEN 'SUBSTITUTED_OUT'

            WHEN policy_annual_date_rule IN (
                'MONTHLY_CANCELLED',
                'SEMIANNUAL_CANCELLED',
                'ANNUAL_CANCELLED',

                'MONTHLY_SUBSIN_CANCELLED',
                'SEMIANNUAL_SUBSIN_CANCELLED',
                'ANNUAL_SUBSIN_CANCELLED',

                'ANNULLED_MONTHLY_CANCELLED',
                'ANNULLED_SEMIANNUAL_CANCELLED',
                'ANNULLED_ANNUAL_CANCELLED',

                'FULLY_ANNULLED_MONTHLY_SUBSIN',
                'FULLY_ANNULLED_SEMIANNUAL_SUBSIN',
                'FULLY_ANNULLED_ANNUAL_SUBSIN'
                ) THEN 'CANCELLED'

            ELSE 'UNKNOWN'
        END                             AS policy_status,
        cancellation_reason,
        policy_annual_date_rule,
        MIN(issue_date) OVER (
            PARTITION BY agent_policy_number
        )                               AS policy_issue_date,
        policy_annual_start_date_local  AS policy_start_date,
        policy_annual_end_date_local    AS policy_end_date,
        underwriting_year,
        SUM(annualized_gross_written_premium) OVER (
            PARTITION BY agent_policy_number
        )                               AS premium_yearly_net,
        SUM(annualized_ipt)  OVER (
            PARTITION BY agent_policy_number
        )                               AS ipt_yearly,
        SUM(gross_written_premium) OVER (
            PARTITION BY agent_policy_number
        )                               AS premium_paid,
        SUM(commission) OVER (
            PARTITION BY agent_policy_number
        )                               AS commission_paid,

        RANK() OVER (PARTITION BY original_policy_number ORDER BY policy_depth DESC) = 1
                                        AS transaction_iscurrent,
        transaction_currency,
        MIN(created_on) OVER (
            PARTITION BY agent_policy_number
        )                               AS first_created_on,
        created_on,
        ROW_NUMBER() OVER (PARTITION BY agent_policy_number ORDER BY created_on DESC) AS rn
    FROM {{ ref('crodino_portfolio') }}
)

SELECT
    distribution_partner,
    product_id,
    {{ dbt_utils.generate_surrogate_key(
            [
                'distribution_partner',
                'people_key_type',
                'codice_fiscale',
                'address_key_type',
                'address',
                'city',
                'post_code'
            ]
        )
    }}                                  AS address_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'policy_key_type', 'crodino_policy_id']
        )
    }}                                  AS policy_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'policy_key_type', 'crodino_main_policy_id']
        )
    }}                                  AS main_policy_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'people_key_type', 'codice_fiscale', 'country_code']
        )
    }}                                  AS man_id,
    transaction_id,
    crodino_policy_id,
    crodino_main_policy_id,
    direct_or_reinsurance,
    product_type,
    form_type,
    is_household,
    branch,
    location_of_underwriting,
    location_of_risk,
    policy_status,
    cancellation_reason,
    payment_frequency,
    policy_issue_date,
    policy_start_date,
    policy_end_date,
    underwriting_year,
    premium_yearly_net,
    ipt_yearly,
    premium_paid,
    commission_paid,
    policy_annual_date_rule,
    transaction_iscurrent,
    transaction_currency,
    parent_code_renewal,
    source,
    first_created_on,
    created_on                          AS updated_on
FROM stg_policies
WHERE
    rn = 1
