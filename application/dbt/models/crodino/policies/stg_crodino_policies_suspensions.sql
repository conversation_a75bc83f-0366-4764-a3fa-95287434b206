{{
    config(
        materialized="table",
        tags=["model-crodino-policies"]
    )
}}


SELECT
    p.distribution_partner,
    p.product_id,
    p.address_id,
    p.policy_id,
    p.main_policy_id,
    p.man_id,
    p.crodino_policy_id,
    p.crodino_main_policy_id,
    p.transaction_id,
    p.direct_or_reinsurance,
    p.product_type,
    p.form_type,
    p.is_household,
    p.branch,
    p.location_of_underwriting,
    p.location_of_risk,
    p.policy_status,
    p.cancellation_reason,
    p.payment_frequency,
    p.policy_issue_date,
    p.policy_start_date,
    p.policy_end_date,
    MIN(p.policy_start_date) OVER w_main_policy                         AS main_policy_start_date,
    p.underwriting_year,
    COALESCE(p.premium_yearly_net, 0) + COALESCE(p.ipt_yearly, 0)       AS total_premium_yearly,
    SUM(COALESCE(p.premium_yearly_net, 0) + COALESCE(p.ipt_yearly, 0))
        OVER w_main_policy                                              AS main_total_premium_yearly,
    p.premium_yearly_net,
    SUM(p.premium_yearly_net) OVER w_main_policy                        AS main_policy_premium_yearly_net,
    p.ipt_yearly,
    SUM(ipt_yearly) OVER w_main_policy                                  AS main_ipt_yearly,
    p.premium_paid,
    SUM(p.premium_paid) OVER w_main_policy                              AS main_premium_paid,
    p.commission_paid,
    p.transaction_currency,
    p.policy_annual_date_rule,
    s.suspension_status,
    s.suspension_effective_date,
    s.suspension_expiration_date,
    s.reactivation_effective_date,
    s.reactivation_expiration_date,
    s.policy_end_date_after_suspension,
    p.transaction_iscurrent,
    p.first_created_on,
    p.updated_on,
    p.parent_code_renewal,
    p.source
FROM {{ ref('stg_crodino_policies') }} p
LEFT JOIN {{ ref('stg_crodino_suspensions') }} s
    USING(crodino_policy_id)
WINDOW w_main_policy AS (
        PARTITION BY main_policy_id
        ORDER BY transaction_id
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
