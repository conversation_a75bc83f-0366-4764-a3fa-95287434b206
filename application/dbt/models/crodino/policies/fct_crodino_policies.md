{% docs fct_crodino_policies %}
# fct_crodino_policies
Provides a view of all Prima Policies.
{% enddocs %}

{% docs field_distribution_partner %}
A unique identifier for a distribution partner.
{% enddocs %}

{% docs field_policy_id %}
A unique identifier for a policy generated by the MGA Platform.
{% enddocs %}

{% docs field_policy_status %}
The status of the policies, indicates whether the policy is active or has been cancelled or susbstituted out.
{% enddocs %}

{% docs field_crodino_policy_id %}
A unique identifier for a policy for prima, also referred to as agent_policy_number or insurance_code.
{% enddocs %}

{% docs field_crodino_main_policy_id %}
A unique identifier for the original policy for prima before any subsitutions. When changes (e.g. MTA) are made to a policy prima cancels the original policies and issues a new one, "substituting" the original policy for the new one. Substitutions can happen multiple times.
{% enddocs %}

{% docs field_crodino_transaction_id %}
The number of the policy transaction with respect to the main policy id should represent how many times a policy has been substituted + 1.
{% enddocs %}

{% docs field_crodino_transaction_iscurrent %}
Check that this is the last policy available for a given main policy id.
{% enddocs %}

{% docs field_crodino_policy_classification %}
Provides a view on the policy classification with respect to renewals.

| Policy classification       | Description                                                                               |
|-----------------------------|-------------------------------------------------------------------------------------------|
| NEW_BUSINESS 				  | We classify as new business when we haven't seen the combination of license plate, insured person before |
| RENEWAL 					  | Policy are classified as renewal when there exists for a given license plate/insured person a previous policy that ended at the same time as the renewed policy starts - that is the two policies are without gaps. |
| FALSE_NEW_BUSINESS 		  | We classify as False New business policies for which we have already had on the portfolio the combination of license plate/insured person and for which there exists a gap between the previous policy end date and the new policy start date. |
{% enddocs %}

{% docs field_crodino_previous_policy_id %}
The policy id for the last policy we had in our portfolio with Prima for a given license plate/insured person before the new policy came into force.
{% enddocs %}

{% docs field_crodino_previous_crodino_policy_id %}
The crodino policy id for the last policy we had in our portfolio with Prima for a given license plate/insured person before the new policy came into force.
{% enddocs %}

{% docs field_crodino_previous_main_policy_id %}
The main policy id for the last policy we had in our portfolio with Prima for a given license plate/insured person before the new policy came into force.
{% enddocs %}

{% docs field_crodino_previous_crodino_main_policy_id %}
The main crodino policy id for the last policy we had in our portfolio with Prima for a given license plate/insured person before the new policy came into force.
{% enddocs %}

{% docs field_ifrs17_aggregated_contract_identifier %}
A grouping of contracts for IFRS17 reporting for the Olympus project. Should be a combination of partner, product, country, start month and contract duration.
{% enddocs %}
