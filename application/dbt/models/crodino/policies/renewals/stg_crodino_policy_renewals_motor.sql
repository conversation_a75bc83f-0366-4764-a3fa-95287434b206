{{
    config(
        materialized="table",
        tags=["model-crodino-policies-renewals"]
    )
}}


WITH
motor_policies AS (
    SELECT
        fcp.main_policy_id,
        fcp.policy_id,
        fcp.crodino_main_policy_id,
        fcp.crodino_policy_id,
        fcp.man_id,
        fcp.transaction_iscurrent,
        fcp.transaction_id,
        fcp.product_id,
        v.vehicle_id,
        fcp.policy_status,
        fcp.policy_start_date                                   AS policy_start_date,
        fcp.policy_end_date_after_suspension                    AS policy_end_date_after_suspension,
        ROW_NUMBER() OVER (
            PARTITION BY fcp.man_id, v.vehicle_id
            ORDER BY fcp.policy_start_date
        ) AS policy_seq
    FROM {{ ref('stg_crodino_policies_suspensions') }} fcp
    LEFT JOIN {{ ref('assoc_crodino_policies_vehicles') }} v
        ON fcp.policy_id = v.policy_id
    WHERE fcp.product_id = 'PMOTOR'
)
/*
    Motor insurance policies
    Join on vehicle_id and man_id
*/
SELECT
    a.main_policy_id                                            AS renewed_main_policy_id,
    b.main_policy_id                                            AS original_main_policy_id,
    a.policy_id                                                 AS renewed_policy_id,
    b.policy_id                                                 AS original_policy_id,
    a.crodino_main_policy_id                                    AS renewed_crodino_main_policy_id,
    b.crodino_main_policy_id                                    AS original_crodino_main_policy_id,
    a.crodino_policy_id                                         AS renewed_crodino_policy_id,
    b.crodino_policy_id                                         AS original_crodino_policy_id,
    a.policy_start_date                                         AS renewed_start_date,
    b.policy_start_date                                         AS original_start_date,
    a.policy_end_date_after_suspension                          AS renewed_main_policy_id_policy_end_date_after_suspension,
    b.policy_end_date_after_suspension                          AS original_policy_end_date_after_suspension,
    a.policy_status                                             AS renewed__policy_status,
    b.policy_status                                             AS original_policy_status,
    a.policy_start_date - b.policy_end_date_after_suspension    AS diff_dates,
    CASE
        WHEN
            a.policy_start_date - b.policy_end_date_after_suspension BETWEEN '0 minutes' and '30 days'
            AND b.policy_status != 'CANCELLED'
            THEN 'RENEWAL'
        ELSE 'FALSE_NEW_BUSINESS'
    END                                                         AS policy_classification
FROM motor_policies a
LEFT JOIN motor_policies b
    ON a.vehicle_id = b.vehicle_id
    AND a.man_id = b.man_id
    AND a.main_policy_id != b.main_policy_id
    AND b.transaction_iscurrent = True
    AND a.policy_seq = b.policy_seq + 1
WHERE
    a.transaction_id = 1
    AND b.man_id IS NOT NULL
