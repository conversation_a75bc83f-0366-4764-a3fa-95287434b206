{{
    config(
        materialized="table",
        tags=["model-crodino-policies-renewals"]
    )
}}

WITH
spanish_policies AS (
    SELECT
        main_policy_id,
        policy_id,
        crodino_main_policy_id,
        crodino_policy_id,
        policy_start_date,
        policy_end_date_after_suspension,
        policy_status,
        parent_code_renewal
    FROM {{ ref('stg_crodino_policies_suspensions') }} fcp
    WHERE fcp.product_id = 'PMOTORES'
)

SELECT
    a.main_policy_id                                            AS renewed_main_policy_id,
    b.main_policy_id                                            AS original_main_policy_id,
    a.policy_id                                                 AS renewed_policy_id,
    b.policy_id                                                 AS original_policy_id,
    a.crodino_main_policy_id                                    AS renewed_crodino_main_policy_id,
    b.crodino_main_policy_id                                    AS original_crodino_main_policy_id,
    a.crodino_policy_id                                         AS renewed_crodino_policy_id,
    b.crodino_policy_id                                         AS original_crodino_policy_id,
    a.policy_start_date                                         AS renewed_start_date,
    b.policy_start_date                                         AS original_start_date,
    a.policy_end_date_after_suspension                          AS renewed_main_policy_id_policy_end_date_after_suspension,
    b.policy_end_date_after_suspension                          AS original_policy_end_date_after_suspension,
    a.policy_status                                             AS renewed__policy_status,
    b.policy_status                                             AS original_policy_status,
    a.policy_start_date - b.policy_end_date_after_suspension    AS diff_dates,
    'RENEWAL'                                                   AS policy_classification
FROM spanish_policies a
LEFT JOIN spanish_policies b
    ON a.parent_code_renewal = b.crodino_policy_id
WHERE
    a.parent_code_renewal IS NOT NULL
