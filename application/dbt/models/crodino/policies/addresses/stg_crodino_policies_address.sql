{{
    config(
        materialized="ephemeral"
    )
}}


WITH
portfolio_people AS (
    SELECT
        'CRODINO'               AS distribution_partner,
        'codice_fiscale'        AS people_key_type,
        'crodino_address'       AS address_key_type,
        'crodino_policy_id'     AS policy_key_type,
        agent_policy_number     AS crodino_policy_id,
        product_id,
        codice_fiscale,
        insured_address         AS address,
        city,
        post_code,
        province,
        region,
        ipt_territory,
        location_of_risk,
        created_on,
        ROW_NUMBER () OVER (
            PARTITION BY agent_policy_number ORDER BY created_on DESC
        )                       AS rn
    FROM {{ ref('crodino_portfolio') }}
)

SELECT
    distribution_partner,
    {{ dbt_utils.generate_surrogate_key(
            [
                'distribution_partner',
                'people_key_type',
                'codice_fiscale',
                'address_key_type',
                'address',
                'city',
                'post_code'
            ]
        )
    }}                       AS address_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'people_key_type', 'codice_fiscale', 'location_of_risk']
        )
    }}                       AS man_id,
    {{ dbt_utils.generate_surrogate_key(
            ['distribution_partner', 'policy_key_type', 'crodino_policy_id']
        )
    }}                       AS policy_id,
    address,
    city,
    post_code,
    province,
    region,
    ipt_territory,
    location_of_risk         AS country,
    created_on               AS updated_on,
    rn                       AS rn
FROM portfolio_people
