{{
    config(
        materialized="table",
        tags=["model-crodino-policy-lifecycle", "model-crodino-portfolio"],
        post_hook=[
            {
                "sql": "create index if not exists {{ this.name }}__index_on_agent_policy_number on {{ this }} (\"agent_policy_number\")",
                "transaction": False
            },
            {
                "sql": "create index if not exists {{ this.name }}__index_on_original_policy_number on {{ this }} (\"original_policy_number\")",
                "transaction": False
            },
            {
                "sql": "alter table {{ this.schema }}.{{ this.name }} add constraint {{ this.name }}_agent_policy_number_pk primary key(agent_policy_number)",
                "transaction": False
            }
        ]
    )
}}

WITH RECURSIVE

links AS (
    SELECT DISTINCT
        agent_policy_number,
        parent_code
    FROM {{ test_utils.testable_ref('stg_crodino_portfolio') }}
    WHERE transaction_type = 'Substitution In'
    AND parent_code IS NOT NULL
),

paths AS (
    SELECT
        l.agent_policy_number,
        l.parent_code,
        l.parent_code                                         AS original_policy_number,
        CONCAT_WS('->', l.parent_code, l.agent_policy_number) AS policy_path,
        1                                                     AS policy_depth
    FROM links l

    UNION ALL

    SELECT
        l.agent_policy_number,
        l.parent_code,
        p.original_policy_number,
        CONCAT_WS('->', p.policy_path, l.agent_policy_number) AS policy_path,
        p.policy_depth + 1                                    AS policy_depth
    FROM links l
    INNER JOIN paths p
        ON l.parent_code = p.agent_policy_number
)

SELECT DISTINCT ON (agent_policy_number)
    agent_policy_number,
    parent_code,
    original_policy_number,
    policy_path,
    policy_depth
FROM paths
ORDER BY
    agent_policy_number,
    policy_depth DESC
