{% docs stg_crodino_suspended_policies %}
# stg_crodino_suspended_policies
The staging table stg_crodino_suspended_policies provides a consolidated view at policy level of the data contained within the Prima suspension borderaux.
{% enddocs %}

{% docs field_crodino_suspension_effective_date %}
Date at which the suspension will become effective - i.e. when the policy will start being effectively paused.
{% enddocs %}

{% docs field_crodino_suspension_expiration_date %}
{% enddocs %}

{% docs field_crodino_reactivation_effective_date %}
Date at which the reactivation will become effective - i.e. when the policy will start becoming active again.
{% enddocs %}

{% docs field_crodino_reactivation_expiration_date %}
The reactivation experiation date should coincide with the final date of the policy - unless the policy becomes subsequently cancelled or substituted.
{% enddocs %}

{% docs field_crodino_suspended %}
0/1 Indicates whether the policy has been suspended.
{% enddocs %}

{% docs field_crodino_reactivated %}
0/1 Indicates whether the policy has been reactivated.
{% enddocs %}
