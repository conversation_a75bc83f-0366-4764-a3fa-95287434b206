{% docs stg_crodino_suspensions_reactivations %}
# stg_crodino_suspensions_reactivations
The stg_crodino_suspensions_reactivations provides a basic transformation layer on top of the Prima Suspension Bordereaux.

It provides transformation of the dates in local date.
{% enddocs %}

{% docs field_crodino_suspension_bdx_action_type %}
The type of suspension transaction being performed (e.g. Sospensione - Annuale or Riattivazione - Annuale)
{% enddocs %}

{% docs field_crodino_suspension_bdx_action_date %}
Date at which the suspension/reactivation was requestion (Europe/Rome local time)
{% enddocs %}

{% docs field_crodino_suspension_bdx_effective_date %}
Date at which the suspension/reactivation transaction becomes effective (Europe/Rome local time)
{% enddocs %}

{% docs field_crodino_suspension_bdx_expiration_date %}
Date of expiration of the transaction (Europe/Rome local time)
{% enddocs %}
