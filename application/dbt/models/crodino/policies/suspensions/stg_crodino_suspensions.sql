{{
    config(
        materialized="table",
        tags=["model-crodino-policy-suspension"]
    )
}}

SELECT
    p.crodino_policy_id,
    CASE
        WHEN s.crodino_policy_id IS NULL                    THEN 'BASE'
        WHEN s.suspended = 1 AND s.reactivated = 1          THEN 'REACTIVATED'
        WHEN s.suspended = 1                                THEN 'SUSPENDED'
        ELSE                                                     'OTHER'
    END                                                     AS suspension_status,
    suspension_effective_date                               AS suspension_effective_date,
    suspension_expiration_date                              AS suspension_expiration_date,
    reactivation_effective_date                             AS reactivation_effective_date,
    reactivation_expiration_date                            AS reactivation_expiration_date,
    /*
        Policies suspended for a period greater than xx days should have the duration of the policies
        extended by the same amount of days that they were suspended for.
        The field reactivation_expiration_date should reflect this new policy end date duration.
    */
    CASE
        -- if a policy has been cancelled this should be its final date
        WHEN
            (
                p.policy_annual_date_rule LIKE '%CANCELLED%'
                OR p.policy_annual_date_rule LIKE '%OUT%'
            )
            AND NOT p.policy_annual_date_rule LIKE '%ANNULLED%'
        THEN policy_end_date
        -- for policy reactivated and not cancelled, when know their new policy end date
        WHEN s.suspended = 1 AND s.reactivated = 1 THEN reactivation_expiration_date
        -- IF a normal non cancelled policy we should leverage the policy annual end date
        ELSE policy_end_date
    END                                                     AS policy_end_date_after_suspension
FROM {{ ref('stg_crodino_policies') }} p
LEFT JOIN {{ ref('stg_crodino_suspended_policies') }} s
    ON p.crodino_policy_id = s.crodino_policy_id
