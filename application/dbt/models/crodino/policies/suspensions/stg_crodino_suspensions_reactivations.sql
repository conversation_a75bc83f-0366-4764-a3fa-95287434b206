{{
    config(
        materialized="table",
        tags=["model-crodino-policy-suspension"]
    )
}}

-- we have duplicated policy in the different borderau received
SELECT
    s.id,
    s.chksum,
    s.created_on,
    s.insurance_code,
    s.action_type,
    s.action_date at time zone 'utc' at time zone 'Europe/Rome'                     AS action_date,
    s.effective_date at time zone 'utc' at time zone 'Europe/Rome'                  AS effective_date,
    s.expiration_date at time zone 'utc' at time zone 'Europe/Rome'                 AS expiration_date,
    ROW_NUMBER() OVER (PARTITION BY insurance_code, action_type, action_date)       AS rn,
    ROW_NUMBER() OVER (PARTITION BY insurance_code, action_type, action_date) = 1   AS transaction_iscurrent
FROM  {{ source('mgalanding', 'prima_suspensions_reactivations') }} s
