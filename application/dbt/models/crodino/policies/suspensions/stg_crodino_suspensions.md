{% docs stg_crodino_suspensions %}
# stg_crodino_suspensions

The staging table provides an overview of all Crodino/Prima policy and their suspension status and related dates.

## Suspension
Crodino/Prima annual policies can be suspended for an undertermined period of time.

The policies can in turn be reactivated, when that happens if the policy has been suspended for a period longer than 1 month, the policy end date should be extended by the duration of the suspension.

During the lifecycle of a policy there is only the possibility for a single substitution event.

{% enddocs %}


{% docs field_crodino_suspension_status %}
Provides an indicator to the suspension status of a given policy

| Suspension Status           | Description                                                                               |
|-----------------------------|-------------------------------------------------------------------------------------------|
| BASE                        | A policy which hasn't been suspended                                                      |
| SUSPENDED                   | A policy which have been suspended but not reactivated                                    |
| REACTIVATED                 | A policy which has been suspended but reactivation_effective_date                         |
| OTHER                       | A policy which suspensions/reactivation event is not covered by the business logic        |
{% enddocs %}


{% docs field_crodino_policy_end_date_after_suspension %}
The end date of the policy after taking into consideration suspensions.

* For policy which haven't been suspended this corresponds to their normal end date.
* For policy which have been suspended and cancelled/substuted, the effective date of cancelation/substitution
* For policy which have been substituted and not cancelled/substuted, the expiration date of reactivation
{% enddocs %}
