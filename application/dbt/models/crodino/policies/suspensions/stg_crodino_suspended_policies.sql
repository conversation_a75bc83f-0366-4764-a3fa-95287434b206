{{
    config(
        materialized="table",
        tags=["model-crodino-policy-suspension"]
    )
}}

-- a policy can only be suspended once in the lifecycle of the policy
SELECT
    insurance_code                                                              AS crodino_policy_id,
    MAX(effective_date) FILTER(WHERE action_type = 'Sospensione - Annuale')     AS suspension_effective_date,
    MAX(expiration_date) FILTER(WHERE action_type = 'Sospensione - Annuale')    AS suspension_expiration_date,
    MAX(effective_date) FILTER(WHERE action_type = 'Riattivazione - Annuale')   AS reactivation_effective_date,
    MAX(expiration_date) FILTER(WHERE action_type = 'Riattivazione - Annuale')  AS reactivation_expiration_date,
    COUNT(1) FILTER(WHERE action_type = 'Sospensione - Annuale')                AS suspended,
    COUNT(1) FILTER(WHERE action_type = 'Riattivazione - Annuale')              AS reactivated
FROM {{ ref('stg_crodino_suspensions_reactivations') }}
WHERE
    rn = 1
GROUP BY
    1
