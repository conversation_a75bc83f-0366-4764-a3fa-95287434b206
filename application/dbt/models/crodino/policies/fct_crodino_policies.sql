{{
    config(
        materialized="table",
        tags=["model-crodino-policies"]
    )
}}
SELECT
    p.distribution_partner,
    p.product_id,
    p.address_id,
    p.policy_id,
    p.main_policy_id,
    p.man_id,
    p.crodino_policy_id,
    p.crodino_main_policy_id,
    p.transaction_id,
    p.direct_or_reinsurance,
    p.product_type,
    p.form_type,
    p.is_household,
    p.branch,
    p.location_of_underwriting,
    p.location_of_risk,
    p.policy_status,
    p.cancellation_reason,
    p.payment_frequency,
    p.policy_issue_date,
    p.policy_start_date,
    p.policy_end_date,
    p.main_policy_start_date,
    p.underwriting_year,
    p.total_premium_yearly,
    p.main_total_premium_yearly,
    p.premium_yearly_net,
    p.commission_paid,
    cm.standard_commission * p.premium_yearly_net
                                                        AS commission_estimated,
    (cm.standard_commission * p.premium_yearly_net) - p.commission_paid
                                                        AS commission_remaining,
    p.main_policy_premium_yearly_net,
    p.ipt_yearly,
    p.main_ipt_yearly,
    p.premium_paid,
    p.main_premium_paid,
    p.transaction_currency                              AS currency,
    p.policy_annual_date_rule,
    p.suspension_status,
    p.suspension_effective_date,
    p.suspension_expiration_date,
    p.reactivation_effective_date,
    p.reactivation_expiration_date,
    p.policy_end_date_after_suspension,
    p.parent_code_renewal,
    p.source,
    p.transaction_iscurrent,
    r.original_main_policy_id                           AS previous_main_policy_id,
    r.original_crodino_main_policy_id                   AS previous_crodino_main_policy_id,
    r.original_policy_id                                AS previous_policy_id,
    r.original_crodino_policy_id                        AS previous_crodino_policy_id,
    COALESCE(r.policy_classification,  'NEW_BUSINESS')  AS policy_classification,
    CONCAT_WS('_',
        UPPER(dp.distribution_partner_name),
        CASE p.product_id
            WHEN 'PFAMILY' THEN 'PHOUSE'
            ELSE p.product_id
        END,
        geo.alpha3,
        COALESCE(TO_CHAR(p.policy_start_date, 'YYYYMM'), '000000'),
        'M12'
    )                                                   AS ifrs17_aggregated_contract_identifier,
    p.first_created_on,
    p.updated_on
FROM {{ ref('stg_crodino_policies_suspensions') }} p
LEFT JOIN {{ ref('stg_crodino_policy_renewals') }} r
    ON r.renewed_main_policy_id = p.main_policy_id
LEFT JOIN {{ ref('seed_countries') }} geo
    ON geo.alpha2 = p.branch
LEFT JOIN {{ ref('seed_distribution_partners') }} dp
    ON dp.distribution_partner_id = p.distribution_partner
LEFT JOIN {{ ref('crodino_commission_mapping') }} AS cm
    ON p.branch = cm.branch
    AND p.product_id = cm.product
    AND (
        p.underwriting_year = cm.underwriting_year
        OR p.main_policy_start_date BETWEEN cm.underwriting_period_start AND cm.underwriting_period_end
    )
