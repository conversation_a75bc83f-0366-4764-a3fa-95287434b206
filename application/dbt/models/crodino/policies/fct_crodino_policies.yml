version: 2

models:
   -  name: fct_crodino_policies
      description: '{{ doc("fct_crodino_policies") }}'
      columns:
      - name: distribution_partner
        description: '{{ doc("field_distribution_partner") }}'
      - name: policy_id
        description: '{{ doc("field_policy_id") }}'
      - name: crodino_policy_id
        description: '{{ doc("field_crodino_policy_id") }}'
      - name: man_id
        description: '{{ doc("field_man_id") }}'
      - name: policy_status
        description: '{{ doc("field_policy_status") }}'
      - name: suspension_status
        description: '{{ doc("field_crodino_suspension_status") }}'
      - name: suspension_effective_date
        description: '{{ doc("field_crodino_suspension_effective_date") }}'
      - name: suspension_expiration_date
        description: '{{ doc("field_crodino_suspension_expiration_date") }}'
      - name: reactivation_effective_date
        description: '{{ doc("field_crodino_reactivation_effective_date") }}'
      - name: reactivation_expiration_date
        description: '{{ doc("field_crodino_reactivation_expiration_date") }}'
      - name: policy_end_date_after_suspension
        description: '{{ doc("field_crodino_policy_end_date_after_suspension") }}'
      - name: crodino_main_policy_id
        description: '{{ doc("field_crodino_main_policy_id") }}'
      - name: transaction_id
        description: '{{ doc("field_crodino_transaction_id") }}'
      - name: transaction_iscurrent
        description: '{{ doc("field_crodino_transaction_iscurrent") }}'
      - name: policy_classification
        description: '{{ doc("field_crodino_policy_classification") }}'
      - name: previous_policy_id
        description: '{{ doc("field_crodino_previous_policy_id") }}'
      - name: previous_crodino_policy_id
        description: '{{ doc("field_crodino_previous_crodino_policy_id") }}'
      - name: previous_main_policy_id
        description: '{{ doc("field_crodino_previous_main_policy_id") }}'
      - name: previous_crodino_main_policy_id
        description: '{{ doc("field_crodino_previous_crodino_main_policy_id") }}'
      - name: is_household
        description: '{{ doc("field_is_household") }}'
      - name: ifrs17_aggregated_contract_identifier
        description: '{{ doc("field_ifrs17_aggregated_contract_identifier") }}'
