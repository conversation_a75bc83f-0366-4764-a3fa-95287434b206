
{% set solvency_classes = [1, 8, 9, 13, 17, 18] %}

WITH
base_premium AS (
    SELECT
        f.is_household,
        pp.ipt_territory,
        pp.agent_policy_number                   AS reference,
        pp.class_of_business                     AS risk_covered,
        CAST(pp.period_start_date_local AS DATE) AS inception,
        CAST(pp.period_end_date_local AS DATE)   AS expiry,
--      This is the premium without tax (gross as gross of reinsurance, nothing related to tax).
--      Not aligned to our general definition to gross premium.
        pp.gross_written_premium                 AS net_premium,
        1                                        AS exchange_rate,
        pp.ssn_tax_rate                          AS ssn_rate,
        pp.ssn_tax                               AS ssn,
        pp.cvt_tax_rate                          AS cvt_rate,
        pp.cvt_tax                               AS cvt,
        pp.antiracket_tax_rate                   AS antiracket_rate,
        pp.antiracket_tax                        AS antiracket,
        pp.ipt_rate                              AS ipt_rate,
        pp.ipt                                   AS ipt,
        f.branch,
--      The IPT reporting month should reflect the month of the premium bordereau.
--      We get the month of the premium borderau from its file name.
--      Since we receive the bordereau in the following month, we get the year by
--      subtracting a month from the time of processing the file.
        f.bordereau_start_date                   AS period,
        pp.parent_code,
        pp.codice_fiscale,
        pp.plate_number
    FROM {{ ref('crodino_portfolio') }} pp
    LEFT JOIN {{ ref('crodino_files') }} f
        ON f.chksum = pp.chksum
    WHERE
        pp.transaction_iscurrent = TRUE
),

mapping AS (
    SELECT
        bp.is_household,
        bp.ipt_territory,
        bp.branch,
        bp.parent_code,
        bp.codice_fiscale,
        bp.plate_number,
        bp.period,
        bp.reference,
        NULL                                                                                               AS insured_name,
        bp.inception,
        bp.expiry,
        'Motor Insurance'                                                                                  AS insured_risk,
        bp.risk_covered,
        ROUND(SUM(bp.net_premium) OVER w_policy_cover::NUMERIC, 2)                                         AS net_premium,
        bp.exchange_rate,
        ROUND(SUM(bp.net_premium) OVER w_policy_cover::NUMERIC, 2)                                         AS net_premium_eur,
        bp.ssn_rate,
        ROUND(SUM(bp.ssn) OVER w_policy_cover::NUMERIC, 2)                                                 AS ssn,
        bp.cvt_rate,
        ROUND(SUM(bp.cvt) OVER w_policy_cover::NUMERIC, 2)                                                 AS cvt,
        bp.antiracket_rate,
        ROUND(SUM(bp.antiracket) OVER w_policy_cover::NUMERIC, 2)                                          AS antiracket,
        bp.ipt_rate,
        ROUND(SUM(bp.ipt) OVER w_policy_cover::NUMERIC, 2)                                                 AS ipt,
        NULL                                                                                               AS net_premium_fbt,
        NULL                                                                                               AS fbt_rate,
        NULL                                                                                               AS fbt,
        ROUND(SUM(bp.ipt) OVER w_policy_cover::NUMERIC, 2)                                                 AS ipt_fbt_total,
        {% for solvency_class in solvency_classes %}
        ROUND(SUM(bp.net_premium) FILTER(WHERE risk_covered = '{{solvency_class}}') OVER w_policy_cover::NUMERIC, 2)
                                                                                                           AS s{{solvency_class}}_net_premium,

        ROUND(SUM(bp.net_premium) FILTER(WHERE risk_covered = '{{solvency_class}}') OVER w_policy_cover::NUMERIC, 2)
                                                                                                           AS s{{solvency_class}}_net_premium_eur,
        ROUND(SUM(bp.ssn) FILTER(WHERE risk_covered = '{{solvency_class}}') OVER w_policy_cover::NUMERIC, 2)
                                                                                                           AS s{{solvency_class}}_ssn,
        ROUND(SUM(bp.cvt) FILTER(WHERE risk_covered = '{{solvency_class}}') OVER w_policy_cover::NUMERIC, 2)
                                                                                                           AS s{{solvency_class}}_cvt,
        ROUND(SUM(bp.antiracket) FILTER(WHERE risk_covered = '{{solvency_class}}') OVER w_policy_cover::NUMERIC, 2)
                                                                                                           AS s{{solvency_class}}_antiracket,
        ROUND(SUM(bp.ipt) FILTER(WHERE risk_covered = '{{solvency_class}}') OVER w_policy_cover::NUMERIC, 2)
                                                                                                           AS s{{solvency_class}}_ipt,
        {% endfor %}
        ROW_NUMBER() OVER (PARTITION BY bp.reference, bp.risk_covered, bp.period ORDER BY bp.inception DESC) AS rn
    FROM base_premium bp
    WINDOW w_policy_cover AS (PARTITION BY bp.reference, bp.risk_covered, bp.period)
)

SELECT
    ma.is_household,
    ma.ipt_territory,
    ma.branch,
    ma.period AS date,
    ma.reference,
    ma.insured_name,
    ma.inception,
    ma.expiry,
    ma.parent_code,
    ma.codice_fiscale,
    ma.plate_number,
    ma.insured_risk,
    ma.risk_covered,
    ma.net_premium,
    ma.exchange_rate,
    ma.net_premium_eur,
    ma.ssn_rate,
    ma.ssn,
    ma.cvt_rate,
    ma.cvt,
    ma.antiracket_rate,
    ma.antiracket,
    ma.ipt_rate,
    ma.ipt,
    ma.net_premium_fbt,
    ma.fbt_rate,
    ma.fbt,
    {% for solvency_class in solvency_classes %}
    ma.s{{solvency_class}}_net_premium,
    ma.s{{solvency_class}}_net_premium_eur,
    ma.s{{solvency_class}}_ssn,
    CASE
        WHEN ma.s{{solvency_class}}_net_premium != 0 THEN ma.s{{solvency_class}}_ssn / ma.s{{solvency_class}}_net_premium
    END                                                                                             AS s{{solvency_class}}_ssn_rate,
    ma.s{{solvency_class}}_cvt,
    CASE
        WHEN ma.s{{solvency_class}}_net_premium != 0 THEN ma.s{{solvency_class}}_cvt / ma.s{{solvency_class}}_net_premium
    END                                                                                             AS s{{solvency_class}}_cvt_rate,
    ma.s{{solvency_class}}_antiracket,
    CASE
        WHEN ma.s{{solvency_class}}_net_premium != 0 THEN ma.s{{solvency_class}}_antiracket / ma.s{{solvency_class}}_net_premium
    END                                                                                             AS s{{solvency_class}}_antiracket_rate,
    ma.s{{solvency_class}}_ipt,
    CASE
        WHEN ma.s{{solvency_class}}_net_premium != 0 THEN ma.s{{solvency_class}}_ipt / ma.s{{solvency_class}}_net_premium
    END                                                                                             AS s{{solvency_class}}_ipt_rate,
    {% endfor %}
    ma.ipt_fbt_total
FROM mapping ma
WHERE
    ma.rn = 1 -- A cancelled policy will have two inception dates, we're only interested in the latter one
