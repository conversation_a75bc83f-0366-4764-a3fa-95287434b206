# quantum-dbt-common

A shared dbt package repository with reusable macros, seeds, and utilities to standardize and accelerate development across multiple dbt projects. This repository contains modular dbt packages that can be imported into other dbt projects to provide common functionality.

## Overview

The quantum-dbt-common repository contains the following packages:

- **logging**: Provides audit logging functionality for dbt runs with support for multiple database platforms
- **test_utils**: Comprehensive testing utilities for unit testing dbt models with fixtures and assertions

## Getting Started

### Prerequisites

- dbt Core >= 1.1.0
- Access to GitLab repository with appropriate permissions
- GitLab token for authentication (required for private repositories)

### Installation

To use these packages in your dbt project, add them to your `packages.yml` file. Here's how to configure your dbt project to use the quantum-dbt-common packages:

#### 1. Create or update your `packages.yml` file

```yaml
packages:
  # Standard dbt packages
  - package: dbt-labs/dbt_utils
    version: [">=1.0.0", "<=1.1.1"]

  # quantum-dbt-common packages
  - git: "https://gitlab-ci-token:{{ env_var('GITLAB_TOKEN') }}@gitlab.com/iptiq/data/quantum-dbt-common.git"
    revision: main  # or specify a specific branch/tag
    subdirectory: logging

  - git: "https://gitlab-ci-token:{{ env_var('GITLAB_TOKEN') }}@gitlab.com/iptiq/data/quantum-dbt-common.git"
    revision: main  # or specify a specific branch/tag
    subdirectory: test_utils
```

#### 2. Install the packages

Run the following command to install the packages:

```bash
dbt deps
```

### Authentication Setup

The packages require GitLab authentication to access the private repository. You need to set up the `GITLAB_TOKEN` environment variable.

#### For Local Development

Set the environment variable in your shell:

```bash
export GITLAB_TOKEN=your_gitlab_token_here
```

#### For Airflow Integration

If you're using Airflow to orchestrate your dbt runs, you need to configure the GitLab token in your Airflow helpers. Here's how to set it up:

##### 1. Update your Airflow connection configuration

Create or update your GitLab token connection in Airflow:

- **Connection ID**: `gitlab_token`
- **Connection Type**: `HTTP`
- **Password/API Key**: Your GitLab personal access token

##### 2. Update your Airflow helpers

In your Airflow DAG helpers (similar to the MGA/analytical implementation), ensure the GitLab token is passed to the dbt environment:

```python
@lru_cache
def get_dbt_env_var() -> dict:
    """
    Returns the dbt environment variables including GitLab token
    """
    dbt_config = Variable.get("your_dbt_config", deserialize_json=True)
    db_config: Dict = AirflowConnectionAdapter.to_pipeline_conf("your_dbt_connection")
    gitlab_token: Dict = AirflowConnectionAdapter.to_pipeline_conf("gitlab_token")

    return {
        "DB_HOST": db_config.get("host", ""),
        "DB_NAME": db_config.get("db", ""),
        "DB_PORT": f'{db_config.get("port")}',
        "DB_USER": db_config.get("user", ""),
        "DB_PASSWORD": db_config.get("password", ""),
        "TARGET_SCHEMA": dbt_config.get("target_schema", ""),
        "GITLAB_TOKEN": gitlab_token.get("api_key"),  # This line is crucial
        # ... other environment variables
    }
```

## Package Documentation

### Logging Package

The logging package provides comprehensive audit logging functionality for dbt runs.

#### Features

- **Cross-platform support**: Works with BigQuery, Snowflake, Redshift, and other databases
- **Automatic audit schema creation**: Creates audit schemas and tables automatically
- **Run-level logging**: Tracks dbt run start and end events
- **Model-level logging**: Logs individual model execution details

#### Usage

Once installed, the logging package automatically hooks into your dbt runs through the `on-run-start` and `on-run-end` hooks. No additional configuration is required.

#### Available Macros

- `logging.create_audit_schema()`: Creates the audit schema if it doesn't exist
- `logging.create_audit_log_table()`: Creates the audit log table
- `logging.log_run_start_event()`: Logs the start of a dbt run
- `logging.log_run_end_event()`: Logs the end of a dbt run
- `logging.get_audit_schema()`: Returns the audit schema name
- `logging.get_audit_relation()`: Returns the audit table relation

### Test Utils Package

The test_utils package provides a comprehensive suite of testing utilities for unit testing dbt models.

#### Features

- **CSV-based testing**: Test models against inline CSV expectations
- **SQL-based testing**: Compare model outputs with SQL statements
- **Fixture support**: Create testable models with fixture data
- **Assertion utilities**: Various assertion macros for comprehensive testing

#### Key Macros

##### Data Generation
- `csv_to_sql(csv, casting_dict)`: Convert inline CSV to SQL
- `list_of_dict_to_sql(_rows, defaults_dict, casting_dict)`: Convert list of dictionaries to SQL
- `list_of_tuples_to_sql(_rows, _row_def, defaults_dict, casting_dict)`: Convert list of tuples to SQL

##### Testing Utilities
- `test_model_against_csv(testable_model, csv_expectation, expectation_casting_dict)`: Test model against CSV
- `test_model_against_sql(testable_model, _sql)`: Test model against SQL statement
- `test_inclusion(table_name_a, table_name_b, cte_name)`: Test record inclusion
- `test_equal_count(table_name_a, table_name_b)`: Test equal record counts

##### Model Testing
- `create_testable_model(model_name)`: Create a testable version of a model
- `testable_ref(model_name, override_dict, dummy)`: Reference models with fixture overrides

#### Example Usage

```sql
-- Test a model against CSV expectation
{{ test_utils.test_model_against_csv(
    testable_model="my_model",
    csv_expectation="
        id,name,value
        1,Alice,100
        2,Bob,200
    ",
    expectation_casting_dict={"id": "INTEGER", "value": "INTEGER"}
) }}
```

## Best Practices

1. **Version Pinning**: Always specify a specific revision/branch in your packages.yml to ensure reproducible builds
2. **Environment Variables**: Use environment variables for sensitive information like tokens
3. **Testing**: Leverage the test_utils package for comprehensive model testing
4. **Audit Logging**: Enable the logging package in production environments for better observability

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure your GITLAB_TOKEN environment variable is set correctly
2. **Package Installation Failures**: Check that you have access to the GitLab repository
3. **Macro Not Found**: Ensure you've run `dbt deps` after updating packages.yml

### Getting Help

- Check the individual package README files for detailed documentation
- Review the macro documentation in the respective `macros/` directories
- Contact the data team for support and questions

## Contributing

When contributing to this repository:

1. Create feature branches for new functionality
2. Update documentation for any new macros or features
3. Test changes thoroughly before submitting merge requests
4. Follow the existing code style and conventions

## License

This project is proprietary to Qunatum and is not licensed for external use.
